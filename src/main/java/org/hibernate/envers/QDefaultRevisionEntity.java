package org.hibernate.envers;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.NumberPath;
import javax.annotation.processing.Generated;

import static com.querydsl.core.types.PathMetadataFactory.forVariable;

/**
 * QDefaultRevisionEntity is a Querydsl query type for DefaultRevisionEntity
 * 
 * Note: This class is manually created because QueryDSL doesn't automatically generate
 * Q-classes for Hibernate Envers entities. This is needed for QCustomRevisionEntity
 * which extends from this class.
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QDefaultRevisionEntity extends EntityPathBase<DefaultRevisionEntity> {

    private static final long serialVersionUID = -1491310658L;

    public static final QDefaultRevisionEntity defaultRevisionEntity = new QDefaultRevisionEntity("defaultRevisionEntity");

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final NumberPath<Long> timestamp = createNumber("timestamp", Long.class);

    public QDefaultRevisionEntity(String variable) {
        super(DefaultRevisionEntity.class, forVariable(variable));
    }

    public QDefaultRevisionEntity(Path<? extends DefaultRevisionEntity> path) {
        super(path.getType(), path.getMetadata());
    }

    public QDefaultRevisionEntity(PathMetadata metadata) {
        super(DefaultRevisionEntity.class, metadata);
    }
}

package com.arealytics.core.service;

import java.time.Instant;

import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.domain.empiricalProd.PropertyInternalToolFields;
import com.arealytics.core.domain.empiricalProd.PropertyResearchStatus;
import com.arealytics.core.domain.empiricalProd.PropertyResearchType;
import com.arealytics.core.domain.empiricalProd.PropertyStrataRelationship;
import com.arealytics.core.dto.response.PropertyResearchResponseDTO;
import com.arealytics.core.enumeration.CondoType;
import com.arealytics.core.mapper.PropertyResearchMapper;
import com.arealytics.core.repository.empiricalProd.PropertyRepository;
import com.arealytics.core.repository.empiricalProd.PropertyResearchStatusRepository;
import com.arealytics.core.repository.empiricalProd.PropertyResearchTypeRepository;
import com.arealytics.core.repository.empiricalProd.PropertyStrataRelationshipRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PropertyResearchStatusService {
    private final PropertyRepository propertyRepository;
    private final PropertyStrataRelationshipRepository strataRelationshipRepository;
    private final PropertyResearchStatusRepository propertyResearchStatusRepository;
    private final PropertyResearchTypeRepository propertyResearchTypeRepository;
    private final PropertyResearchMapper mapper;

    /**
     * Creates a default PropertyResearchStatus for a given property.
     *
     * @param property The property entity to associate with the research status
     * @return The saved PropertyResearchStatus entity
     */
    public PropertyResearchStatus createDefaultResearchStatus(Property property) {
        PropertyResearchStatus researchStatus = new PropertyResearchStatus();
        researchStatus.setProperty(property);
        researchStatus.setPropertyResearchType(new PropertyResearchType());
        researchStatus.getPropertyResearchType().setPropertyResearchTypeId(1);
        researchStatus.setIsActive(true);
        return propertyResearchStatusRepository.save(researchStatus);
    }

    @Transactional
    public List<PropertyResearchResponseDTO> getResearchStatusHistory(Integer propertyId) {
        // Validate that the property exists before proceeding
        propertyRepository.findById(propertyId)
                .orElseThrow(() -> new IllegalArgumentException("Property not found with id: " + propertyId));

        // Fetch only active research status records for the given property
        List<PropertyResearchStatus> researchStatusHistory = propertyResearchStatusRepository
                .findByProperty_PropertyIDAndIsActiveTrueOrderByPropertyResearchType_Sequence(propertyId);

        // Convert entities to DTOs and return
        return researchStatusHistory.stream()
                .map(this::toPropertyResearchDTO)
                .toList();
    }

    private PropertyResearchResponseDTO toPropertyResearchDTO(PropertyResearchStatus researchStatus) {
        // Convert entity to DTO using mapper
        PropertyResearchResponseDTO dto = mapper.toDTO(researchStatus);

        // Safely extract modified person's full name if available
        if (researchStatus.getModifiedBy() != null && researchStatus.getModifiedBy().getPerson() != null) {
            String firstName = researchStatus.getModifiedBy().getPerson().getFirstName();
            String lastName = researchStatus.getModifiedBy().getPerson().getLastName();
            dto.setModifiedPersonName(firstName + " " + lastName);
        }

        return dto;
    }

    // Collects Property IDs into a set, including the input property ID and any related strata property IDs    
    public Set<Integer> collectPropertyIds(Integer propertyId, Integer propertyResearchTypeId,
                                           Integer propertyResearchStatusId) {
        Set<Integer> propertyIds = new HashSet<>();
        Property property = propertyRepository.findByPropertyIDAndIsActive(propertyId, true)
                .orElse(null);

        if (property != null && property.getPropertyDetails() != null
                && property.getPropertyDetails().getCondoTypeID() != null
                && property.getPropertyDetails().getCondoTypeID() == CondoType.MASTER_STRATA_RECORD) {
            propertyIds.add(propertyId);
            List<PropertyStrataRelationship> strataRelationships = strataRelationshipRepository
                    .findByMasterPropertyIdAndIsActive(propertyId, true);
            propertyIds.addAll(strataRelationships.stream()
                    .map(relationship -> relationship.getStrataProperty().getPropertyID())
                    .collect(Collectors.toSet()));
        } else {
            propertyIds.add(propertyId);
        }

        return propertyIds;
    }

    
    //  Saves or updates the PropertyResearchStatus for the given property and related strata properties,
    //  and updates the ResearchTypeID and ResearchTypeName in Property

    @Transactional
    public void savePropertyResearchStatus(Integer propertyId, Integer propertyResearchTypeId,
                                           Integer propertyResearchStatusId) {
        if (propertyResearchTypeId == null) {
            return; // Skip if no research type provided
        }

        Set<Integer> propertyIds = collectPropertyIds(propertyId, propertyResearchTypeId, propertyResearchStatusId);

        PropertyResearchStatus existingStatus = propertyResearchStatusRepository
                .findByPropertyPropertyIDAndPropertyResearchType_PropertyResearchTypeIdAndIsActive(propertyId, propertyResearchTypeId, true)
                .orElse(null);

        if (existingStatus == null) {
            // No active record exists, insert new PropertyResearchStatus for all property IDs
            for (Integer propId : propertyIds) {
                PropertyResearchStatus newStatus = new PropertyResearchStatus();
                PropertyResearchType propertyResearchType = new PropertyResearchType();
                propertyResearchType.setPropertyResearchTypeId(propertyResearchTypeId);
                Property property = new Property();
                property.setPropertyID(propId);
                newStatus.setProperty(property);
                newStatus.setPropertyResearchType(propertyResearchType);
                newStatus.setIsActive(true);

                propertyResearchStatusRepository.save(newStatus);
            }
        } else {
                // Update the input property's research status
                    existingStatus.setModifiedDate(Instant.now());
                    propertyResearchStatusRepository.save(existingStatus);

                // Update related strata properties' research status
                for (Integer propId : propertyIds) {
                    if (!propId.equals(propertyId)) {
                        PropertyResearchStatus strataStatus = propertyResearchStatusRepository
                                .findByPropertyPropertyIDAndPropertyResearchType_PropertyResearchTypeIdAndIsActive(propId, propertyResearchTypeId, true)
                                .orElse(null);
                        if (strataStatus != null) {
                            strataStatus.setModifiedDate(Instant.now());
                            propertyResearchStatusRepository.save(strataStatus);
                    }
                }
            }
        }

        PropertyResearchStatus latestStatus = propertyResearchStatusRepository
                .findFirstByPropertyPropertyIDAndIsActiveOrderByModifiedDateDesc(propertyId, true)
                .orElse(null);
        Integer currentPropertyResearchTypeId = (latestStatus != null) ? latestStatus.getPropertyResearchStatusId() : null;

        PropertyResearchType researchType = (currentPropertyResearchTypeId != null)
                ? propertyResearchTypeRepository.findById(currentPropertyResearchTypeId).orElse(null)
                : null;
        String researchTypeName = (researchType != null) ? researchType.getPropertyResearchTypeName() : null;

        for (Integer propId : propertyIds) {
            Property property = propertyRepository.findById(propId).orElse(null);
            if (property != null) {
                PropertyInternalToolFields internalToolFields = property.getInternalToolFields();

                if (currentPropertyResearchTypeId != null) {
                    PropertyResearchStatus activeStatus = propertyResearchStatusRepository
                            .findByPropertyPropertyIDAndPropertyResearchType_PropertyResearchTypeIdAndIsActive(
                                    propId, currentPropertyResearchTypeId, true
                            ).orElse(null);
                    if (activeStatus != null) {
                        internalToolFields.setResearchTypeID(currentPropertyResearchTypeId);
                        internalToolFields.setResearchTypeName(researchTypeName);
                    }
                } else {
                    internalToolFields.setResearchTypeID(null);
                    internalToolFields.setResearchTypeName(null);
                }

                propertyRepository.save(property);
            }
        }

    }
}

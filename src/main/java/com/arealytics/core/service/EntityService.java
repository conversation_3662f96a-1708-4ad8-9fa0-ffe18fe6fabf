package com.arealytics.core.service;

import com.arealytics.core.domain.empiricalProd.EntityModel;
import com.arealytics.core.domain.empiricalProd.Person;
import com.arealytics.core.repository.empiricalProd.EntityRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class EntityService {
  private final EntityRepository entityRepository;

  @Transactional
  public EntityModel getEntityById(Integer id) {
    return entityRepository.findById(id).orElse( null);
  }

  public String getFullNameByEntityId(Integer entityId) {
    if (entityId != null) {
      EntityModel entity = getEntityById(entityId);
      if (entity != null) {
        Person personDetails = entity.getPerson();
        if (personDetails != null) {
          String firstName = personDetails.getFirstName();
          String lastName = personDetails.getLastName();

          // Use empty string if null and trim result
          String fullName = (firstName != null ? firstName : "") + " " + (lastName != null ? lastName : "");
          return fullName.trim().isEmpty() ? null : fullName.trim();
        }
      }
    }
    return null;
  }

}

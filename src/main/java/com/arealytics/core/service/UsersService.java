package com.arealytics.core.service;

import com.arealytics.core.domain.empiricalProd.UserPreferences;
import com.arealytics.core.dto.request.UserPreferenceRequestDTO;
import com.arealytics.core.dto.response.UserPreferenceResponseDTO;
import com.arealytics.core.repository.empiricalProd.UserPreferencesRepository;
import com.arealytics.core.utils.UserContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.arealytics.core.mapper.UsersMapper;
import com.arealytics.core.repository.empiricalProd.EntityRepository;
import com.arealytics.core.domain.empiricalProd.EntityModel;
import com.arealytics.core.dto.response.UsersDTO;

import java.util.List;
import java.util.stream.Collectors;

import com.arealytics.core.enumeration.UserPreferenceScreen;
import com.arealytics.core.enumeration.UserPreferenceType;

@Service
@RequiredArgsConstructor
public class UsersService {
    private final EntityRepository entityRepository;
    private final UserPreferencesRepository userPreferencesRepository;
    private final UsersMapper usersMapper;

     public List<UsersDTO> getUsersByCompanyID(Integer companyId) {
        List<EntityModel> entities = entityRepository.findByCompanyCompanyId(companyId);
        return entities.stream()
                .map(usersMapper::toUsersDTO)
                .collect(Collectors.toList());
    }

    public UserPreferenceResponseDTO getUserPreferecesByTypeAndScreen(UserPreferenceType type,
        UserPreferenceScreen screen) {
      Integer userId = UserContext.getLoginEntity().getEntityId();
      UserPreferences userPreferences = userPreferencesRepository
          .findFirstByUserIdAndTypeAndScreenOrderByModifiedDateDesc(userId, type.getLabel(), screen.getLabel())
          .orElse(null);
      return usersMapper.toUserPreferenceDTO(userPreferences);
    }

    public UserPreferenceResponseDTO createUserPreference(UserPreferenceRequestDTO request) {
      UserPreferences userPreferences = usersMapper.toUserPreferencesEntity(request, new UserPreferences());
      return saveUserPreference(userPreferences);
    }

    public UserPreferenceResponseDTO updateUserPreference(UserPreferenceRequestDTO request, Integer userPreferenceId) {
      UserPreferences existingUserPreference = userPreferencesRepository.findByUserPreferencesId(userPreferenceId)
          .orElseThrow(() -> new IllegalArgumentException("The given user preference does not exist"));

      UserPreferences updatedUserPreference = usersMapper.toUserPreferencesEntity(request, existingUserPreference);
      return saveUserPreference(updatedUserPreference);
    }

    private UserPreferenceResponseDTO saveUserPreference(UserPreferences userPreferences) {
      UserPreferences savedData = userPreferencesRepository.save(userPreferences);
      return usersMapper.toUserPreferenceDTO(savedData);
    }
}

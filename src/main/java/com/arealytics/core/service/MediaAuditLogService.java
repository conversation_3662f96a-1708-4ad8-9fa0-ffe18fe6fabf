package com.arealytics.core.service;

import java.time.Instant;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.arealytics.core.constants.AuditLogConstants;
import com.arealytics.core.domain.empiricalProd.MediaRelationship;
import com.arealytics.core.enumeration.AuditEntity;
import com.arealytics.core.repository.empiricalProd.MediaRelationshipRepository;

@Service
public class MediaAuditLogService {

    @Autowired
    private AuditService auditService;

    @Autowired
    private MediaRelationshipRepository mediaRelationshipRepository;

    public List<Map<String, Object>> fetchMediaAuditLogs(String entityType, Integer entityId) {

        // Fetch existing MediaRelationship
        MediaRelationship mediaRelationship = mediaRelationshipRepository
                .findByMedia_MediaIdAndIsActiveTrue(entityId)
                .orElseThrow(() -> new IllegalArgumentException("Media not found"));

        // Retrieve related IDs
        Integer mediaRelationshipId = mediaRelationship.getMediaRelationshipId();

        // Fetch audit logs for all entities
        List<Map<String, Object>> combinedRevisions = Arrays.asList(
                fetchAuditLogs(AuditEntity.MEDIA.getName(), entityId),
                fetchAuditLogs(AuditEntity.MEDIA_RELATIONSHIP.getName(), mediaRelationshipId)).stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        // Sort combined revisions by the "timeStamp" field in descending order
        combinedRevisions.sort(
                Comparator.comparing(
                        revision -> Optional.ofNullable((Instant) revision.get(AuditLogConstants.CHANGED_DATE))
                                .orElse(Instant.MIN),
                        Comparator.reverseOrder()));

        return combinedRevisions;
    }

    /**
     * Helper method to fetch audit logs for a given entity.
     */
    private List<Map<String, Object>> fetchAuditLogs(String entityName, Integer entityId) {
        return auditService.fetchAuditLogsByEntityId(entityName, entityId);
    }
}

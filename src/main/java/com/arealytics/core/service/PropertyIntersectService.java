package com.arealytics.core.service;

import com.arealytics.core.domain.empiricalProd.Address;
import com.arealytics.core.domain.empiricalProd.Use;
import com.arealytics.core.dto.response.PropertyIntersectResponseDTO;
import com.arealytics.core.enumeration.ParentTable;
import com.arealytics.core.repository.empiricalProd.AddressRepository;
import com.arealytics.core.repository.empiricalProd.PropertyRepository;
import com.arealytics.core.repository.empiricalProd.UseRepository;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class PropertyIntersectService {

    @Autowired
    private AddressRepository addressRepository;

    @Autowired
    private UseRepository useRepository;

    @Autowired
    private PropertyRepository propertyRepository;

    private final GeometryFactory geometryFactory = new GeometryFactory();

    public List<PropertyIntersectResponseDTO> getPropertyIntersect(
            Integer metroID,
            Integer propertyID,
            Integer useTypeID,
            BigDecimal latitude,
            BigDecimal longitude) {

        List<PropertyIntersectResponseDTO> responses = new ArrayList<>();

        if (propertyID != null) {
            Optional<Address> addressOpt = addressRepository.findByParentIdAndParentTableIdAndIsActiveAndSequence(
                    propertyID, ParentTable.Property, true, 1);
            if (addressOpt.isEmpty() || addressOpt.get().getLocation() == null) return responses;

            BigDecimal locLongitude = addressOpt.get().getLocation().getLongitude();
            BigDecimal locLatitude = addressOpt.get().getLocation().getLatitude();
            if (locLongitude == null || locLatitude == null) return responses;

            Point point = geometryFactory.createPoint(new Coordinate(locLongitude.doubleValue(), locLatitude.doubleValue()));
            responses.addAll(propertyRepository.findMarketsAndSubMarketsByPoint(point, useTypeID, propertyID));
        } else {
            if (latitude == null || longitude == null) return responses;
            Point point = geometryFactory.createPoint(new Coordinate(longitude.doubleValue(), latitude.doubleValue()));
            responses.addAll(propertyRepository.findMarketsAndSubMarketsByPoint(point, useTypeID, null));
        }
        return responses;
    }
}

package com.arealytics.core.service;

import com.arealytics.core.dto.request.AgentSearchRequestDTO;
import com.arealytics.core.dto.response.AgentSearchResponseDTO;
import com.arealytics.core.dto.response.CompanySearchResponseDTO;
import com.arealytics.core.repository.empiricalProd.custom.Impl.CompanyRepositoryImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class CompanyRelationshipService {
  private final CompanyRepositoryImpl companyRepository;

  public List<CompanySearchResponseDTO> getCompanies(Integer companyId, Integer branchId, String text) {
    return companyRepository.getCompanies(companyId, branchId, text);
  }

  public List<AgentSearchResponseDTO> getAgents(AgentSearchRequestDTO agentSearchRequestDTO) {
    return companyRepository.getAgents(agentSearchRequestDTO);
  }
}

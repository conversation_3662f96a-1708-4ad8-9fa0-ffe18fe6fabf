package com.arealytics.core.service;

import com.arealytics.core.domain.empiricalProd.City;
import com.arealytics.core.dto.request.CityRequestDTO;
import com.arealytics.core.repository.empiricalProd.CityRepository;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class CityService {

    @Autowired
    private CityRepository cityRepository;

    @Transactional
    public City createCity(CityRequestDTO cityRequest) {

        Optional<City> optionalCity = cityRepository.findByCityNameAndStateId(
                cityRequest.getCityName(),
                cityRequest.getStateId()
        );

        if (optionalCity.isPresent()) {
            return optionalCity.get(); // only returns if city is present
        }

        // Create a new city
        City newCity = new City();
        newCity.setCityName(cityRequest.getCityName());
        newCity.setStateId(cityRequest.getStateId());
        newCity.setIsActive(true);
        newCity.setActiveMunicipality(false);
        newCity.setMarketStateId(cityRequest.getStateId()); // Set MarketStateID to StateID

        return cityRepository.save(newCity);
    }
}

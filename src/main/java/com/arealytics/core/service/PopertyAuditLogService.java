package com.arealytics.core.service;

import java.time.Instant;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.arealytics.core.constants.AuditLogConstants;
import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.domain.empiricalProd.PropertyLocation;
import com.arealytics.core.enumeration.AuditEntity;
import com.arealytics.core.repository.empiricalProd.PropertyRepository;

@Service
public class PopertyAuditLogService {

   @Autowired
    private AuditService auditService;

    @Autowired
    private PropertyRepository propertyRepository;

    public List<Map<String, Object>> fetchPropertyAuditLogs(String entityType, Integer entityId) {
        // Fetch property and validate existence
        Property property = propertyRepository.findById(entityId)
            .orElseThrow(() -> new IllegalArgumentException("Property not found"));

        PropertyLocation locationDetails = Optional.ofNullable(property.getPropertyLocation())
            .orElseThrow(() -> new IllegalStateException("Property location details not found"));

        // Retrieve related IDs
        Integer locationId = Optional.ofNullable(locationDetails.getLocation())
            .map(loc -> loc.getLocationID())
            .orElseThrow(() -> new IllegalStateException("Location ID not found"));

        Integer addressId = Optional.ofNullable(locationDetails.getAddressId())
            .map(addr -> addr.getAddressId())
            .orElseThrow(() -> new IllegalStateException("Address ID not found"));

        // Fetch audit logs for all entities
        List<Map<String, Object>> combinedRevisions = Arrays.asList(
            fetchAuditLogs(AuditEntity.ADDRESS.getName(), addressId, false),
            fetchAuditLogs(AuditEntity.LOCATION.getName(), locationId, null),
            fetchAuditLogs(AuditEntity.PROPERTY.getName(), entityId, null)
        ).stream()
            .flatMap(Collection::stream)
            .collect(Collectors.toList());

        // Sort combined revisions by the "timeStamp" field in descending order
        combinedRevisions.sort(
            Comparator.comparing(
                revision -> Optional.ofNullable((Instant) revision.get(AuditLogConstants.CHANGED_DATE))
                    .orElse(Instant.MIN),
                Comparator.reverseOrder())
        );

        return combinedRevisions;
    }

    /**
     * Helper method to fetch audit logs for a given entity.
     */
    private List<Map<String, Object>> fetchAuditLogs(String entityName, Integer entityId, Boolean includeAddActionInAuditLogs) {
        return auditService.fetchAuditLogsByEntityId(entityName, entityId, includeAddActionInAuditLogs);
    }
}

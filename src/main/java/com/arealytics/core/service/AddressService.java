package com.arealytics.core.service;

import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.arealytics.core.dto.request.AdditionalAddressRequestDTO;
import com.arealytics.core.dto.response.AdditionalAddressDTO;
import com.arealytics.core.dto.response.PropertyDetailsDTO;
import com.arealytics.core.enumeration.AddressType;
import com.arealytics.core.enumeration.ParentTable;

import com.arealytics.core.enumeration.Prefix;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.arealytics.core.domain.empiricalProd.Address;
import com.arealytics.core.domain.empiricalProd.EntityModel;
import com.arealytics.core.domain.empiricalProd.Suffix;
import com.arealytics.core.mapper.AddressMapper;
import com.arealytics.core.repository.empiricalProd.AddressRepository;
import com.arealytics.core.repository.empiricalProd.SuffixRepository;
import com.arealytics.core.utils.UserContext;

import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class AddressService {
    @Autowired
    private final EntityManager en;
    private final AddressRepository addressRepository;
    private final AddressMapper addressMapper;
    private final SuffixRepository suffixRepository;

    public Address createAddress(PropertyDetailsDTO propertyDTO, Integer locationId, Integer propertyId) {
        Address address = addressMapper.toAddress(propertyDTO, locationId, propertyId);
        return addressRepository.save(address);
    }

    public Address updateAddress(PropertyDetailsDTO propertyDTO, Integer locationId, Integer propertyId) {

        Address address =
                addressRepository
                        .findFirstByParentIdAndIsActiveTrueAndSequenceOrderByAddressIdDesc(propertyId, 1)
                        .stream()
                        .findFirst()
                        .orElseThrow(
                                () ->
                                        new RuntimeException(
                                                "No active address found for property: "
                                                        + propertyId));

        address = addressMapper.updateAddressFromDto(propertyDTO, address);
        return addressRepository.save(address);
    }

    /**
     * Finds an active address by parent ID.
     *
     * @param propertyId The ID of the property
     * @return An Optional containing the active address, if found
     */
    public Optional<Address> findByParentIdAndIsActiveTrue(Integer propertyId) {
        return addressRepository.findFirstByParentIdAndIsActiveTrueAndSequenceOrderByAddressIdDesc(propertyId, 1);
    }

    public List<AdditionalAddressDTO> getAdditionalAddress(Integer propertyId) {
        return addressRepository
                .findByParentTableIdAndParentIdAndSequenceNotAndIsActiveTrue(ParentTable.Property, propertyId, 1)
                .stream().map(addressMapper::toAdditionalAddressDto).toList();
    }

    @Transactional
    public AdditionalAddressDTO saveAdditionalAddress(AdditionalAddressRequestDTO dto) {
        Integer propertyId = dto.getParentId();

        List<Address> addresses = addressRepository
            .findByParentIdAndParentTableIdAndAddressTypeIdOrderBySequenceAsc(
                propertyId, ParentTable.Property, AddressType.PHYSICAL
            );

        // Get main and top addresses
        Address primaryAddress = addresses.isEmpty() ? null : addresses.getFirst(); // Lowest sequence
        Address lastSequenceAddress = addresses.isEmpty() ? null : addresses.getLast(); // Highest sequence

        int nextSequence = (lastSequenceAddress != null && lastSequenceAddress.getSequence() != null)
            ? lastSequenceAddress.getSequence() + 1
            : 1;

        Address address = addressMapper.addtionalAddresstoDto(dto);
        address.setSequence(nextSequence);

        if (primaryAddress != null) {
            if (primaryAddress.getLocationId() != null && address.getLocation() != null) {
                address.getLocation().setLocationID(primaryAddress.getLocationId());
            }
            address.setCountryId(primaryAddress.getCountryId());
        }

        EntityModel createdBy = new EntityModel();
        createdBy.setEntityId(UserContext.getLoginEntity().getEntityId());
        address.setCreatedBy(createdBy);
        address.setCreatedDate(Instant.now());
        address.setAddressText(generateAddressText(address));

        Address updatedAddress = addressRepository.save(address);
        en.refresh(updatedAddress);
        return addressMapper.toAdditionalAddressDto(updatedAddress);
    }

    public AdditionalAddressDTO updateAdditionalAddress(Integer additionalAddressId, AdditionalAddressRequestDTO dto) {
        Address existing = addressRepository.findByAddressIdAndIsActiveTrue(additionalAddressId)
                .orElseThrow(() -> new IllegalArgumentException("Address not found with id: " + additionalAddressId));

        EntityModel modifiedBy = new EntityModel();
        modifiedBy.setEntityId(UserContext.getLoginEntity().getEntityId());

        // Update fields from DTO to existing
        addressMapper.updateAdditionalAddressFromDto(dto, existing);

        existing.setModifiedDate(Instant.now());
        existing.setModifiedBy(modifiedBy);
        existing.setAddressText(generateAddressText(existing));

        addressRepository.save(existing);

        return addressMapper.toAdditionalAddressDto(existing);
    }

    private String generateAddressText(Address address) {
        // todo: change prefix to enum
        Prefix prefix = address.getPrefixId();
        Prefix prefix2 = address.getPrefix2Id();
        String suffix = getSuffix(address.getSuffixId());
        String suffix2 = getSuffix(address.getSuffix2Id());

        String number = (address.getStreetNumberMin() != null && address.getStreetNumberMax() != null &&
                !address.getStreetNumberMin().equals(address.getStreetNumberMax()))
                        ? address.getStreetNumberMin() + "-" + address.getStreetNumberMax()
                        : address.getStreetNumberMin() != null ? address.getStreetNumberMin().toString() : null;

        return Stream.of(
                number,
                prefix != null ? prefix.toString() : null,
                prefix2 != null ? prefix2.toString() : null,
                address.getAddressStreetName(),
                suffix != null ? suffix.toString() : null,
                suffix2 != null ? suffix2.toString() : null)
                .filter(Objects::nonNull)
                .filter(s -> !s.isBlank())
                .collect(Collectors.joining(" "));
    }

    private String getSuffix(Integer suffixId) {
        if (suffixId == null)
            return null;
        return suffixRepository.findById(suffixId)
                .filter(Suffix::getIsActive)
                .map(Suffix::getSuffix)
                .orElse(null);
    }

    public void deleteAdditionalAddress(Integer additionalAddressId) {
        Address address = addressRepository.findByAddressIdAndIsActiveTrue(additionalAddressId)
                .orElseThrow(() -> new IllegalArgumentException("Address not found with id: " + additionalAddressId));
        EntityModel modifiedBy = new EntityModel();
        modifiedBy.setEntityId(UserContext.getLoginEntity().getEntityId());
        address.setIsActive(false);
        address.setModifiedBy(modifiedBy);
        address.setModifiedDate(Instant.now());
        addressRepository.save(address);
    }

}



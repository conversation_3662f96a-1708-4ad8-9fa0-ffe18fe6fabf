package com.arealytics.core.service;

import java.util.List;
import java.util.stream.Collectors;

import com.arealytics.core.repository.empiricalProd.MarketRepository;
import com.arealytics.core.specification.MarketSpecification;

import lombok.AllArgsConstructor;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import com.arealytics.core.domain.empiricalProd.Market;
import com.arealytics.core.dto.request.MarketSearchCriteriaDTO;
import com.arealytics.core.dto.response.MarketInfoDTO;
import com.arealytics.core.mapper.MarketMapper;

@Service
@AllArgsConstructor
public class MarketService {

    private final MarketRepository marketRepository;
    private final MarketMapper marketMapper;

    public List<MarketInfoDTO> getMarkets(MarketSearchCriteriaDTO requestDTO) {
        Integer stateId = normalize(requestDTO.getStateId());
        Integer metroId = normalize(requestDTO.getMetroId());
        Integer useTypeId = normalize(requestDTO.getUseTypeId());

        // Start with base spec: active markets only
        Specification<Market> spec = Specification.where(MarketSpecification.isActive());

        // Apply state or metro filter if available
        if (stateId != null) {
            spec = spec.and(MarketSpecification.hasStateId(stateId));
        } else if (metroId != null) {
            spec = spec.and(MarketSpecification.hasMetroId(metroId));
        }

        // Apply useType filter if available
        if (useTypeId != null) {
            spec = spec.and(MarketSpecification.hasUseTypeId(useTypeId));
        }

        // Query and convert results
        List<Market> markets = marketRepository.findAll(spec);
        return markets.stream()
                .map(marketMapper::toDTO)
                .collect(Collectors.toList());
    }

    private Integer normalize(Integer id) {
        return (id != null && id == 0) ? null : id;
    }
}

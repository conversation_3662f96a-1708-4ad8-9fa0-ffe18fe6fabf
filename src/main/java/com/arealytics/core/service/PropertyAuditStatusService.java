package com.arealytics.core.service;

import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.repository.empiricalProd.PropertyAuditStatusRepository;

import jakarta.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.arealytics.core.domain.empiricalProd.PropertyAuditLog;
import com.arealytics.core.domain.empiricalProd.StatusDefination;
import com.arealytics.core.repository.empiricalProd.StatusDefinationRepository;


@Service
public class PropertyAuditStatusService {

    @Autowired
    private PropertyAuditStatusRepository propertyAuditStatusRepository;
    @Autowired
    private StatusDefinationRepository statusDefinationRepository;

    public Integer savePropertyAuditStatus(Integer propertyId, Integer statusDefinationId) {

        if (!statusDefinationRepository.existsById(statusDefinationId)) {
         throw new IllegalArgumentException("Invalid PropertyAuditStatusID: " + statusDefinationId + " does not exists.");
       }

        try {
        // Deactivate existing active audit log for the property
        PropertyAuditLog existingActiveLog = propertyAuditStatusRepository
                .findByPropertyPropertyIDAndIsActiveTrue(propertyId);
        
        if (existingActiveLog != null) {
            existingActiveLog.setIsActive(false);
            propertyAuditStatusRepository.save(existingActiveLog);
        }

        // Create new audit log entry
        PropertyAuditLog newLog = new PropertyAuditLog();
        Property property = new Property();
        property.setPropertyID(propertyId);
        
        StatusDefination statusDefination = new StatusDefination();
        statusDefination.setStatusDefinationID(statusDefinationId);
        
        newLog.setProperty(property);
        newLog.setStatusDefination(statusDefination);
        newLog.setIsActive(true);
        
        propertyAuditStatusRepository.save(newLog);

        // Return the property ID
        return propertyId;
        } catch (Exception e) {
            throw new RuntimeException("Failed to save property audit status: " + e.getMessage(), e);
        }
    }
}

package com.arealytics.core.service;

import com.arealytics.core.dto.request.ImageUploadRequestDTO;
import com.arealytics.core.dto.response.ImageUploadDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

import java.util.Base64;

@Service
@RequiredArgsConstructor
public class S3Service {
    @Value("${aws.s3.bucket.name}")
    private String bucketName;

    @Value("${aws.s3.path}")
    private String bucketPath;

    @Value("${aws.s3.acl}")
    private String acl;

    private final S3Client s3Client;

    public ImageUploadDTO upload(ImageUploadRequestDTO request) {
        String base64 = request.getBase64().replaceAll("^.+?(;base64),", "");
        byte[] buffer = Base64.getDecoder().decode(base64);

        // Determine upload path
        String fullPath = resolveUploadPath(request);

        // Upload to S3
        try {
          PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                  .bucket(bucketName)
                  .key(fullPath + request.getFileName())
                  .acl(acl)
                  .build();

          s3Client.putObject(putObjectRequest, RequestBody.fromBytes(buffer));
          ImageUploadDTO dto = new ImageUploadDTO();
          dto.setUploaded(true);
          dto.setFilename(request.getFileName());
          return dto;
        } catch (Exception e) {
          throw new RuntimeException("Upload failed: '%s'".formatted(e.getMessage()));
        }
    }

    private String resolveUploadPath(ImageUploadRequestDTO request) {
        if (Boolean.TRUE.equals(request.getIsMarketBrief())) {
            return bucketPath + "/MarketBrief/Media/";
        } else if (Boolean.TRUE.equals(request.getIsClientLogo())) {
            return bucketPath + "/MarketBrief/ClientLogo/";
        } else if (Boolean.TRUE.equals(request.getIsSignature())) {
            return bucketPath + "/MarketBrief/Signatures/";
        } else {
            return bucketPath + "/Media/";
        }
    }
}

package com.arealytics.core.service;

import com.arealytics.core.domain.empiricalProd.ReleaseUpdates;
import com.arealytics.core.dto.response.ReleaseUpdatesResponseDTO;
import com.arealytics.core.mapper.ReleaseUpdatesMapper;
import com.arealytics.core.repository.empiricalProd.ReleaseUpdatesRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReleaseUpdatesService {

  private final ReleaseUpdatesRepository releaseUpdatesRepository;
  private final ReleaseUpdatesMapper mapper;

  public ReleaseUpdatesResponseDTO getReleaseUpdates(Integer applicationId) {
    return releaseUpdatesRepository.findByApplication_ApplicationIdAndIsActiveTrue(applicationId)
        .map(mapper::toDTO)
        .orElseThrow(() -> {
          return new IllegalArgumentException(
              "No release updates available for the given Application ID: " + applicationId);
        });
  }
}

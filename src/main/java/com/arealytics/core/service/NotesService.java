package com.arealytics.core.service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.arealytics.core.domain.empiricalProd.MediaRelationship;
import com.arealytics.core.repository.empiricalProd.MediaRelationshipRepository;
import org.springframework.stereotype.Service;

import com.arealytics.core.domain.empiricalProd.Notes;
import com.arealytics.core.domain.empiricalProd.NotesMediaRelationship;
import com.arealytics.core.dto.request.NotesRequestDTO;
import com.arealytics.core.dto.response.NotesResponseDTO;
import com.arealytics.core.enumeration.ParentTable;
import com.arealytics.core.mapper.NotesMapper;
import com.arealytics.core.repository.empiricalProd.NotesMediaRelationshipRepository;
import com.arealytics.core.repository.empiricalProd.NotesRespository;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class NotesService {
    private final NotesRespository notesRepository;
    private final NotesMapper notesMapper;
    private final NotesMediaRelationshipRepository notesMediaRepository;
    private final MediaRelationshipRepository mediaRelationshipRepository;

    public List<NotesResponseDTO> getNotes(Integer parentId, ParentTable parentTableId) {
        List<Notes> notes = parentTableId.equals(ParentTable.Person)
                ? notesRepository.findByParentTableIdAndParentIdAndIsActiveTrueOrderByModifiedDate(
                        ParentTable.ContactRole, parentId)
                : notesRepository.findByParentTableIdAndParentIdAndIsActiveTrueOrderByModifiedDateDesc(parentTableId,
                        parentId);

        List<Integer> noteIds = notes.stream().map(n -> n.getNoteId()).toList();

        List<NotesMediaRelationship> allRelationships = notesMediaRepository
                .findByNotes_NoteIdInAndIsActiveTrue(noteIds);

        Map<Integer, List<NotesMediaRelationship>> mediaByNoteId = allRelationships.stream()
                .collect(Collectors.groupingBy(rel -> rel.getNotes().getNoteId()));

        return notes.stream()
                .map(note -> mapNoteToDto(note, mediaByNoteId))
                .toList();
    }

    private String formatName(String firstName, String lastName) {
        return ((firstName != null ? firstName : "") +
                (lastName != null ? " " + lastName : "")).trim();
    }

    public NotesResponseDTO saveNotes(NotesRequestDTO requestDto) {
        Notes notes = notesMapper.toEntity(requestDto);
        notesRepository.save(notes);
        return mapNoteToDto(notes, null); // No media yet
    }

    public NotesResponseDTO updateNotes(Integer noteId, NotesRequestDTO requestDto) {
        Notes note = notesRepository.findByNoteIdAndIsActiveTrue(noteId)
                .orElseThrow(() -> new EntityNotFoundException("Note not found with ID: " + noteId));

        notesMapper.updateNotesFromDto(requestDto, note);
        notesRepository.save(note);

        // Fetch media for this note
        List<NotesMediaRelationship> media = notesMediaRepository.findByNotes_NoteIdAndIsActiveTrue(noteId);
        Map<Integer, List<NotesMediaRelationship>> mediaMap = Map.of(noteId, media);

        return mapNoteToDto(note, mediaMap);
    }

    private NotesResponseDTO mapNoteToDto(Notes note, Map<Integer, List<NotesMediaRelationship>> mediaByNoteId) {
        NotesResponseDTO dto = notesMapper.toDto(note);

        // Set linked media if available
        if (mediaByNoteId != null) {
            List<NotesMediaRelationship> relationships = mediaByNoteId.getOrDefault(note.getNoteId(), List.of());
            List<NotesResponseDTO.LinkedMediaDTO> linkedMediaDTOs = notesMapper.toLinkedMediaDtoList(relationships);
            dto.setLinkedMedias(linkedMediaDTOs);
        }

        // Set createdByName
        if (note.getCreatedBy() != null && note.getCreatedBy().getPerson() != null) {
            dto.setCreatedByName(formatName(
                    note.getCreatedBy().getPerson().getFirstName(),
                    note.getCreatedBy().getPerson().getLastName()));
        }

        // Set modifiedByName
        if (note.getModifiedBy() != null && note.getModifiedBy().getPerson() != null) {
            dto.setModifiedByName(formatName(
                    note.getModifiedBy().getPerson().getFirstName(),
                    note.getModifiedBy().getPerson().getLastName()));
        }

        return dto;
    }

    public void deleteNotes(Integer noteId) {

        // Step 1: Soft delete the note
        Notes note = notesRepository.findByNoteIdAndIsActiveTrue(noteId)
                .orElseThrow(() -> new EntityNotFoundException("Note not found or already inactive"));
        note.setIsActive(false);
        notesRepository.save(note);

        // Step 2: Soft delete associated NotesMediaRelationships
        List<NotesMediaRelationship> relationships = notesMediaRepository.findByNotes_NoteIdAndIsActiveTrue(noteId);
        for (NotesMediaRelationship rel : relationships) {
            rel.setIsActive(false);
        }
        notesMediaRepository.saveAll(relationships);
        // Step 3: Soft delete associated MediaRelationship entries
        List<Integer> mediaIds = relationships.stream()
                .map(rel -> rel.getMedia().getMediaId())
                .filter(Objects::nonNull)
                .toList();

        if (!mediaIds.isEmpty()) {
            List<MediaRelationship> mediaRelationships = mediaRelationshipRepository
                    .findByMedia_MediaIdInAndIsActiveTrue(mediaIds);
            for (MediaRelationship media : mediaRelationships) {
                media.setIsActive(false);
            }
            mediaRelationshipRepository.saveAll(mediaRelationships);
        }
    }

}

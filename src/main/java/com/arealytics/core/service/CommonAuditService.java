package com.arealytics.core.service;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.arealytics.core.enumeration.ParentTable;

@Service
public class CommonAuditService {

    @Autowired
    private AuditService auditService;

    @Autowired
    private PopertyAuditLogService propertyService;

    @Autowired
    private MediaAuditLogService mediaService;

    public List<Map<String, Object>> fetchAuditLogs(String entityType, Integer entityId) {

        if (entityType.equals(ParentTable.Property.getLabel())) {
            return propertyService.fetchPropertyAuditLogs(entityType, entityId);

        } else if (entityType.equals(ParentTable.Media.getLabel())) {
            return mediaService.fetchMediaAuditLogs(entityType, entityId);
        } else {
            return auditService.fetchAuditLogsByEntityId(entityType, entityId);
        }
    }

}

package com.arealytics.core.service;

import com.arealytics.core.constants.AppConstants;
import com.arealytics.core.constants.Applications;
import com.arealytics.core.constants.LoginErrorMessages;
import com.arealytics.core.domain.empiricalProd.Login;
import com.arealytics.core.domain.empiricalProd.SSO;
import com.arealytics.core.dto.request.RefreshTokenRequestDTO;
import com.arealytics.core.dto.request.SSORequestDTO;
import com.arealytics.core.dto.request.UserInfoDTO;
import com.arealytics.core.dto.response.login.*;
import com.arealytics.core.mapper.LoginMapper;
import com.arealytics.core.repository.empiricalProd.ApplicationRepository;
import com.arealytics.core.repository.empiricalProd.EntityRepository;
import com.arealytics.core.repository.empiricalProd.LoginRepository;
import com.arealytics.core.repository.empiricalProd.SSORepository;
import com.arealytics.core.security.JwtTokenProvider;
import com.arealytics.core.security.SecurityUtils;
import com.arealytics.core.utils.HashUtil;
import com.arealytics.core.utils.LoginSessionBuilder;
import com.arealytics.core.utils.RequestUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class LoginService {
    private final LoginRepository loginRepository;
    private final HttpServletRequest request;
    private final JwtTokenProvider jwtTokenProvider;
    private final EntityRepository entityRepository;
    private final ApplicationRepository applicationRepository;
    private final LoginSessionBuilder loginSessionBuilder;
    private final SSORepository ssoRepository;
    private final SecurityUtils securityUtils;
    private final LoginMapper loginMapper;

    @Transactional
    public LoginUserNameValidationResponseDTO validateUsername(String username) {
        UserNameValidationLoginStatusDTO loginStatus = loginRepository.validateUsername(username);
        List<UserListDTO> userDetails = loginRepository.getUserDetails(username);
        return new LoginUserNameValidationResponseDTO(List.of(loginStatus), userDetails);
    }

    @Transactional("transactionManager")
    public LoginDTO login(String username, String password,  Integer applicationID, String visitorID) {
        String passwordSalt = loginRepository.findPasswordSaltByUsername(username);
        String saltedPassword = password + passwordSalt;
        String hashedPassword = HashUtil.sha512Hex(saltedPassword).toUpperCase();

      // Step 2: Fetch client IP and initialize variables
      String ipAddress = RequestUtils.extractClientIp(request);
      String token = null;
      String errorMessage = null;

      // Step 3: Fetch user login info and access metadata
      LoginUserInfoDTO loginUserInfoDTO = loginRepository.getLoginUserInfo(username, hashedPassword, null);
      LoginAccessMetaInfoDTO access = loginRepository.getLoginAccessMetaInfo(username, hashedPassword, null);

      LoginResponseStatusDTO loginResponseStatusDTO = new LoginResponseStatusDTO();

      loginMapper.toDTO(access, loginUserInfoDTO);

      // Step 4: Validate access
      errorMessage = validateAccess(applicationID, access, null, username, hashedPassword);

      if (access == null) {
        // Step 6: Handle invalid credentials
        loginRepository.incrementFailedAttempts(username);

        Integer failedAttemptsCount = loginRepository.getFailedAttemptsCount(username);
        if (failedAttemptsCount != null && failedAttemptsCount == 5) {
          loginRepository.lockAccount(username);
          errorMessage = LoginErrorMessages.ACCOUNT_LOCKED_AREALYTICS;
        }
      }

      // Step 7: Generate response
      if (errorMessage != null) {
        loginResponseStatusDTO.setResponseCode(-1);
        loginResponseStatusDTO.setErrorMessage(errorMessage);
      } else {
        loginResponseStatusDTO.setResponseCode(1);

        // Token generation
        token = jwtTokenProvider.generateToken(
            loginUserInfoDTO.getEntityID(),
            loginUserInfoDTO.getRoleID().getId(),
            loginUserInfoDTO.getPersonName(),
            ipAddress,
            applicationID);

        // Create login session
        createLoginSession(
            loginUserInfoDTO.getEntityID(),
            applicationID,
            ipAddress,
            visitorID,
            request,
            null);

      }

      // Step 10: Return final login DTO
      return new LoginDTO(loginResponseStatusDTO, loginUserInfoDTO, token);
    }

    @Transactional("empiricalProdTransactionManager")
    public SSODetailsDTO saveLoginSSO(SSORequestDTO ssoRequestDTO) {
      SSO sso = loginSessionBuilder.buildSSOSession(ssoRequestDTO);
      SSO savedSSO = ssoRepository.save(sso);
      return new SSODetailsDTO(savedSSO.getSsoId(), savedSSO.getToken());
    }

    public String getRefreshToken(RefreshTokenRequestDTO refreshTokenRequestDTO) {
      String ipAddress = RequestUtils.extractClientIp(request);
      UserInfoDTO userInfoDTO = securityUtils.getUserInfo();
      Integer applicationId = Integer.parseInt(userInfoDTO.getApplicationID().toString());

      // Token generation
      return jwtTokenProvider.generateToken(
          refreshTokenRequestDTO.getEntityID(),
          refreshTokenRequestDTO.getRoleID(),
          refreshTokenRequestDTO.getPersonName(),
          ipAddress,
          applicationId);
    }

    @Transactional("empiricalProdTransactionManager")
    public LoginDTO ssoValidateToken(String token, Integer applicationID) {

      Integer impersonateUserId = loginRepository.findImpersonateUserIdByActiveToken(token);
      LoginUserInfoDTO loginUserInfoDTO = loginRepository.ssoValidateTokenUserInfo(token);
      LoginAccessMetaInfoDTO access = loginRepository.ssoValidateTokenAccessMetaInfo(token);
      LoginResponseStatusDTO loginResponseStatusDTO = new LoginResponseStatusDTO();

      String errorMessage = null;
      String ipAddress = RequestUtils.extractClientIp(request);

      loginMapper.toDTO(access, loginUserInfoDTO);

      // Step 4: Validate access
      errorMessage = validateAccess(applicationID, access, impersonateUserId, null, null);

      if (errorMessage == null && loginUserInfoDTO == null) {
        errorMessage = LoginErrorMessages.INVALID_USERNAME_PASSWORD;
      }

      if (errorMessage != null) {
        loginResponseStatusDTO.setResponseCode(-1);
        loginResponseStatusDTO.setErrorMessage(errorMessage);
      } else {
        loginResponseStatusDTO.setResponseCode(1);

        // Token generation
        token = jwtTokenProvider.generateToken(
            loginUserInfoDTO.getEntityID(),
            loginUserInfoDTO.getRoleID().getId(),
            loginUserInfoDTO.getPersonName(),
            ipAddress,
            applicationID);

        // Create login session
        createLoginSession(
            loginUserInfoDTO.getEntityID(),
            applicationID,
            ipAddress,
            null,
            request,
            token);
      }

      return new LoginDTO(loginResponseStatusDTO, loginUserInfoDTO, token);
    }

    public String validateAccess(Integer applicationID, LoginAccessMetaInfoDTO access, Integer impersonateUserId, String userName, String hasedPassword) {
      String errorMessage = null;
      if (access == null) {
        return LoginErrorMessages.INVALID_USERNAME_PASSWORD;
      }

      // --- Case 1: Commercial AU ---
      if (Applications.COMMERCIAL_AU.equals(applicationID)) {
        if (Integer.valueOf(1).equals(access.getAccountLockID())) {
          errorMessage = LoginErrorMessages.ACCOUNT_LOCKED_COMMERCIAL_AU;
        }

        if (Boolean.TRUE.equals(access.getHasAccess()) && Boolean.TRUE.equals(access.getBranchHasAccess())
            || Boolean.TRUE.equals(access.getCauHasAccess())
            || Boolean.TRUE.equals(access.getCauBranchHasAccess())
            || Boolean.TRUE.equals(access.getIsLimitedAccess())) {
          resetFailedAttempts(access, impersonateUserId, userName, hasedPassword);
          // errorMessage = null;
        } else {
          errorMessage = LoginErrorMessages.NOT_SUBSCRIBED_COMMERCIAL_AU;
        }
      }

      // --- Case 2: Limited Access with ECRE or Ultimate company ---
      if (Boolean.TRUE.equals(access.getIsLimitedAccess())
          && (Applications.ECRE.equals(applicationID)
              || AppConstants.ULTIMATE_COMPANY_ID == access.getUltimateCompanyID())) {
        resetFailedAttempts(access, impersonateUserId, userName, hasedPassword);
        // errorMessage = null;
      }

      // --- Case 3: Company not subscribed ---
      if (!Integer.valueOf(AppConstants.ULTIMATE_COMPANY_ID).equals(access.getUltimateCompanyID())
          && !Applications.ECRE.equals(applicationID)) {
        errorMessage = LoginErrorMessages.COMPANY_NOT_SUBSCRIBED;
      }

      // --- Case 4: General case for Arealytics and other apps ---
      if (!Boolean.TRUE.equals(access.getBranchHasAccess()) || !Boolean.TRUE.equals(access.getBranchIsMember())) {
        errorMessage = LoginErrorMessages.COMPANY_NOT_SUBSCRIBED;
      }

      if (!Boolean.TRUE.equals(access.getIsActive())) {
        errorMessage = LoginErrorMessages.ACCOUNT_INACTIVE;
      }

      if (access.getAccountLockID() != null && access.getAccountLockID() != 0) {
        errorMessage = LoginErrorMessages.ACCOUNT_LOCKED_AREALYTICS;
      }

      if (!Boolean.TRUE.equals(access.getHasAccess())) {
        errorMessage = LoginErrorMessages.NOT_SUBSCRIBED_AREALYTICS;
      }

      // ✅ Success case
      resetFailedAttempts(access, impersonateUserId, userName, hasedPassword);
      return errorMessage;
    }

    private void resetFailedAttempts(LoginAccessMetaInfoDTO access, Integer impersonateUserId, String userName, String hasedPassword) {
      loginRepository.resetFailedAttempts(userName, hasedPassword, impersonateUserId);
      access.setFailedAttemptsCount(0);
    }

    private void createLoginSession(
        Integer entityId,
        Integer applicationId,
        String ipAddress,
        String visitorId,
        HttpServletRequest request,
        String token) {

      // Validate entity exists
      entityRepository.findById(entityId)
          .orElseThrow(() -> new RuntimeException("Entity not found with ID: " + entityId));

      // Validate application exists
      applicationRepository.findById(applicationId)
          .orElseThrow(() -> new RuntimeException("Application not found with ID: " + applicationId));

      // Build login session
      Login login = loginSessionBuilder.buildLoginSession(entityId, applicationId, ipAddress, visitorId, request, null);

      // Save login
      loginRepository.save(login);

      // Optionally invalidate SSO token if provided
      if (token != null) {
        loginRepository.invalidateSSOToken(token);
      }
    }

}


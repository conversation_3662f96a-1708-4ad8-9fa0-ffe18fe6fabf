package com.arealytics.core.service;

import org.springframework.stereotype.Service;

import com.arealytics.core.dto.response.AcknowledgeStatusResponseDTO;
import com.arealytics.core.repository.empiricalProd.AcknowledgementRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class AcknowledgeService {
    private final AcknowledgementRepository acknowledgementRepository;

    public AcknowledgeStatusResponseDTO getAcknowledgementStatus(Integer entityId) {
        boolean exists = acknowledgementRepository.existsByEntity_EntityIdAndDoNotShowAckTrue(entityId);
        return new AcknowledgeStatusResponseDTO(exists ? 1 : 0);
    }
}

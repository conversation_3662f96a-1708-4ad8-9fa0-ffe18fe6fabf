
package com.arealytics.core.service;

import org.springframework.stereotype.Service;

import com.arealytics.core.dto.response.GeneralSettingsDTO;
import com.arealytics.core.mapper.GeneralSettingsMapper;
import com.arealytics.core.repository.empiricalProd.GeneralSettingsRepository;


import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class GeneralSettingsService {

    private final GeneralSettingsRepository settingsRepository;
    private final GeneralSettingsMapper settingsMapper;

    public GeneralSettingsDTO getActiveSettingsByKeyAndGroup(String key, String group) {
        return settingsRepository
                .findByKeyAndSettingsGroupAndIsActiveTrue(key, group)
                .map(settingsMapper::toDTO)
                .orElseThrow(() -> new IllegalArgumentException("Settings Not Found"));
    }

}

package com.arealytics.core.service;

import com.arealytics.core.constants.ListingTypeConstants;
import com.arealytics.core.constants.SavedSearchConstants;
import com.arealytics.core.domain.empiricalProd.SavedSearch;
import com.arealytics.core.domain.empiricalProd.SavedSearchPortfolio;
import com.arealytics.core.domain.empiricalProd.UserActivity;
import com.arealytics.core.dto.request.SavedSearchRequestDTO;
import com.arealytics.core.dto.response.GeneralSettingsDTO;
import com.arealytics.core.dto.response.SavedSearchDetailsResponseDTO;
import com.arealytics.core.dto.response.SavedSearchFieldsResponseDTO;
import com.arealytics.core.enumeration.ParentTable;
import com.arealytics.core.mapper.GeneralSettingsMapper;
import com.arealytics.core.mapper.SavedSearchMapper;
import com.arealytics.core.repository.empiricalProd.*;
import com.arealytics.core.utils.UserContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.querydsl.jpa.impl.JPAQueryFactory;

import jakarta.persistence.EntityNotFoundException;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Getter
@Setter
public class SavedSearchService {

    private final GeneralSettingsRepository generalSettingsRepository;
    private final SavedSearchPortfolioRepository savedSearchPortfoliaRepository;
    private final SavedSearchRepository savedSearchRepository;
    private final UserActivityRepository userActivityRepository;
    private final ChangeLogFieldsRepository changeLogFieldsRepository;
    private final SavedSearchMapper mapper;
    private final GeneralSettingsMapper settingsMapper;
    private final JPAQueryFactory queryFactory;
    private final EntityService entityService;

  public List<SavedSearchFieldsResponseDTO> getSavedSearchFields(ParentTable parentTableId) {
    return changeLogFieldsRepository.findByParentTableIdAndShowInSavedSearchTrueAndIsActiveTrue(parentTableId)
        .stream()
        .map(mapper::toFieldsDTO)
        .toList();
  }

  
  public Integer saveDetailsOfSavedSearch(SavedSearchRequestDTO savedSearchRequestDTO) {
      return getSavedSearchFieldsData(savedSearchRequestDTO, null);
  }

  @Transactional
  public Integer updateDetailsOfSavedSearch(SavedSearchRequestDTO savedSearchRequestDTO, Integer searchId) {
      return getSavedSearchFieldsData(savedSearchRequestDTO,
              searchId);
  }

  public void deleteSavedSearchbyId(Integer id) {
      Optional<SavedSearch> savedSearchOpt = savedSearchRepository.findById(id);
      if (savedSearchOpt.isPresent()) {
          SavedSearch savedSearch = savedSearchOpt.get();
          savedSearch.setActive(false);
          savedSearch.setModifiedDate(Instant.now());
          savedSearchRepository.save(savedSearch);
      }
  }

  public List<SavedSearchDetailsResponseDTO> getSavedSearchDetailsBySearchType(String searchType) {

      // Fetch saved searches
      List<SavedSearch> savedSearches = savedSearchRepository
              .findByCreatedBy_EntityIdAndIsActiveTrueAndSaveTypeOrderByLastRunDateDesc(UserContext.getLoginEntity().getEntityId(), searchType);

      // Map entities to DTOs
      return savedSearches.stream()
              .map(savedSearch -> mapper.toDTO(savedSearch))
              .collect(Collectors.toList());
  }

  public Integer updateTheExpirationDateOfSavedSearch(Integer savedSearchId) {
      SavedSearch savedSearch = savedSearchRepository.findById(savedSearchId)
              .orElseThrow(() -> new EntityNotFoundException("SavedSearch not found with ID: " + savedSearchId));

      Instant expDate = savedSearch.getExpirationDate();
      String saveType = savedSearch.getSaveType();
      Integer searchTypeId = savedSearch.getSearchParentTable().getId();

      // extract ListingType from JSON
      String listingType = null;
      if (savedSearch.getCriteria() != null) {
          ObjectMapper objectMapper = new ObjectMapper();
          try {
              JsonNode criteriaJson = objectMapper.readTree(savedSearch.getCriteria());
              listingType = extractJsonValue(criteriaJson, "$.ListingType");
          } catch (JsonProcessingException e) {
              e.printStackTrace(); 
          }
      }

      if (listingType == null) {
          listingType = resolveListingType(searchTypeId); 
      }

      Integer expirationDateFromSettings = calculateExpirationInstant(fetchGeneralSettings(saveType).getFilterJSON(),
              listingType);
      Instant newExpDate = (expDate != null ? expDate : Instant.now())
              .plusSeconds((long) expirationDateFromSettings * SavedSearchConstants.SECONDS_IN_A_DAY);
      savedSearch.setExpirationDate(newExpDate);
      savedSearch.setModifiedBy(UserContext.getLoginEntity());
      savedSearch.setModifiedDate(Instant.now());
      savedSearchRepository.save(savedSearch);

      return savedSearchId;
  }

  public static String resolveListingType(Integer searchTypeId) {
      return switch (searchTypeId) {
          case 11 -> ListingTypeConstants.LEASECOMP;
          case 10 -> ListingTypeConstants.SALECOMP;
          default -> null;
      };
  }
  private Integer getSavedSearchFieldsData(SavedSearchRequestDTO dto, Integer searchId) {
      // Create or get the portfolio ID for this saved search
      Integer newPortfolioId = createOrGetPortfolio(dto, searchId);

      JsonNode searchResult = null;
      JsonNode userCriteria = null;
      String listingType = null;
      ObjectMapper objectMapper = new ObjectMapper();

      // If a user activity ID is provided, fetch criteria and search result
      if (dto.getUserActivityID() != null) {
          Optional<UserActivity> activityOpt = userActivityRepository.findByUserActivityId(dto.getUserActivityID());
          if (activityOpt.isPresent()) {
              UserActivity activity = activityOpt.get();
              userCriteria = activity.getUserCriteria(); // JSON criteria
              searchResult = activity.getDetails(); // Search results

              // Extract listing type from criteria JSON
              listingType = extractJsonValue(userCriteria, "$.ListingType");

              // Fallback: If listing type is missing, use search type mapping
              if (listingType == null) {
                 listingType=resolveListingType(dto.getSearchTypeID().getId());
              }
          }
      }

      // Fetch expiration settings and calculate expiration date if listing type is
      // known
      GeneralSettingsDTO generalSettings = fetchGeneralSettings(dto.getSaveType());
      Instant expirationDate = (listingType == null)
              ? null
              : Instant.now().plusSeconds((long) calculateExpirationInstant(generalSettings.getFilterJSON(), listingType) * SavedSearchConstants.SECONDS_IN_A_DAY);

      SavedSearch existingSavedSearch = null;
      if (searchId != null) {
          // Validate and fetch the existing saved search for update
          existingSavedSearch = savedSearchRepository.findById(searchId)
                  .orElseThrow(() -> new IllegalArgumentException(
                          "SavedSearch not found for ID: " + searchId));
      }

      // Use new portfolio ID if created, otherwise fallback to existing one in
      // request
      Integer portfolioId = newPortfolioId != null ? newPortfolioId : dto.getPortfolioID();

      // Map DTO to entity (create new or update existing)
      SavedSearch savedSearch = mapper.toEntity(
              dto,
              portfolioId,
              expirationDate,
              searchId == null ? new SavedSearch() : existingSavedSearch);

      // Save the entity to generate ID if it's a new saved search
      SavedSearch updatedSavedSearch = savedSearchRepository.save(savedSearch);

      try {
          // Set search result JSON (from user activity or request)
          updatedSavedSearch.setResult(objectMapper.writeValueAsString(searchResult) != null
                  ? objectMapper.writeValueAsString(searchResult)
                  : dto.getResult());

          if (searchId == null) {
              // For new saved searches, inject the generated ID into the criteria
              JsonNode criteriaJson = userCriteria != null
                      ? objectMapper.valueToTree(userCriteria)
                      : objectMapper.readTree(dto.getCriteria());

              updatedSavedSearch.setCriteria(
                      injectSavedSearchIdIntoCriteria(criteriaJson, updatedSavedSearch.getId()));
          } else {
              // For updates, just set modifiedBy and criteria
              updatedSavedSearch.setModifiedBy(UserContext.getLoginEntity());
              updatedSavedSearch.setCriteria(objectMapper.writeValueAsString(userCriteria) != null
                      ? objectMapper.writeValueAsString(userCriteria)
                      : dto.getCriteria());
          }
      } catch (JsonProcessingException e) {
          // Log or handle JSON serialization errors
          e.printStackTrace();
      }

      // Final save to persist updated criteria and result
      savedSearchRepository.save(updatedSavedSearch);

      return updatedSavedSearch.getId();
  }

  private String injectSavedSearchIdIntoCriteria(JsonNode criteriaJson, Integer savedSearchId) {
      try {
          ObjectMapper mapper = new ObjectMapper();

          // Ensure that the JSON node is an object before attempting to add a field
          if (criteriaJson.isObject()) {
              // Add or overwrite the "SavedSearchID" field with the provided value
              ((ObjectNode) criteriaJson).put("SavedSearchID", savedSearchId);
          }

          // Convert the modified JSON node back into a string
          return mapper.writeValueAsString(criteriaJson);
      } catch (Exception e) {
          // Wrap any exception in a RuntimeException for simplified error handling
          throw new RuntimeException("Failed to inject SavedSearchID into criteria JSON", e);
      }
  }

   private Integer createOrGetPortfolio(SavedSearchRequestDTO dto, Integer searchId) {
      // Check if a portfolio with the same name already exists for the logged-in user
      boolean exists = savedSearchPortfoliaRepository
              .existsByPortfolioNameAndCreatedByAndIsActive(
                      dto.getPortfolioName(),
                      UserContext.getLoginEntity(),
                      true);

      // If no PortfolioID is provided in the request AND no portfolio with this name
      // exists,
      // create a new portfolio and return its ID
      if (dto.getPortfolioID() == null && !exists) {
          SavedSearchPortfolio portfolio = new SavedSearchPortfolio();
          portfolio.setPortfolioName(dto.getPortfolioName());
          portfolio.setActive(true);
          return savedSearchPortfoliaRepository.save(portfolio).getPortfolioID();
      }

      // Check if the saved search has no portfolio linked to it
      boolean noSavedSearch = savedSearchRepository
              .findByIdAndSavedSearchPortfolioIsNull(searchId)
              .isEmpty();

      // If there's no saved search linked to any portfolio,
      // fetch and return the existing portfolio ID by name and creator
      if (noSavedSearch) {
          return savedSearchPortfoliaRepository
                  .findByPortfolioNameAndCreatedBy(dto.getPortfolioName(), UserContext.getLoginEntity())
                  .map(SavedSearchPortfolio::getPortfolioID)
                  .orElse(null);
      }

      // If none of the above conditions match, no portfolio is created or returned
      return null;
  }

   private GeneralSettingsDTO fetchGeneralSettings(String saveType) {
        String key, group;

        // Determine the settings key and group based on the save type
        switch (saveType.toLowerCase()) {
            case SavedSearchConstants.SAVEDSEARCH -> {
                // For Saved Search, use saved search expiration days and group
                key = SavedSearchConstants.SAVED_SEARCH_EXPIRATION_DAYS;
                group = SavedSearchConstants.SAVED_SEARCH;
            }
            case SavedSearchConstants.SAVEDRESULT -> {
                // For Saved Result, use saved results expiration days and group
                key = SavedSearchConstants.SAVED_RESULTS_EXPIRATION_DAYS;
                group = SavedSearchConstants.SAVED_RESULT;
            }
            default ->
                // Throw an exception if the save type is not recognized
                throw new IllegalArgumentException("Invalid save type: " + saveType);
        }

        // Retrieve active settings from repository and map to DTO
        return generalSettingsRepository.findByKeyAndSettingsGroupAndIsActiveTrue(key, group)
                .map(settingsMapper::toDTO)
                // Throw exception if no settings are found
                .orElseThrow(() -> new IllegalArgumentException("Settings Not Found"));
    }

    private String extractJsonValue(JsonNode node, String path) {
        // Return null immediately if the provided node is null
        if (node == null)
            return null;
        try {
            // Remove the leading "$." from the path if present
            if (path.startsWith("$."))
                path = path.substring(2);

            // Convert dot notation to JSON pointer format and extract the value
            return node.at("/" + path.replace(".", "/")).asText(null);
        } catch (Exception e) {
            // Return null if any exception occurs (invalid path or access issue)
            return null;
        }
    }

    public static Integer calculateExpirationInstant(String filterJSON, String listingType) {
        try {
            // Parse the filter JSON into a JsonNode
            JsonNode rootNode = new ObjectMapper().readTree(filterJSON);


            // Handle combined listing type "LeaseAndSale" mapping
            String typeKey = listingType != null && listingType.equals(ListingTypeConstants.LEASEANDSALE)
                    ? SavedSearchConstants.LEASEANDSALE
                    : listingType;
            // Extract the number of days for the given listing type from the JSON
            int days = Optional.ofNullable(rootNode.path(typeKey))
                    .filter(JsonNode::isInt) // Ensure the value is an integer
                    .map(JsonNode::asInt) // Convert to int
                    .orElseThrow(() -> new IllegalArgumentException(
                            "Invalid or missing days in Filter JSON for listing type: " + listingType));

            // Convert days to seconds (1 day = 86400 seconds) and add to current timestamp
            return days;
        } catch (Exception e) {
            // Wrap and rethrow any parsing or calculation errors
            throw new RuntimeException("Error extracting expiration days from JSON", e);
        }
    }
}

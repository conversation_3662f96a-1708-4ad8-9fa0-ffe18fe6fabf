package com.arealytics.core.service;

import com.arealytics.core.dto.response.PropertyDetailsDTO;
import com.arealytics.core.enumeration.ParentTable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.domain.empiricalProd.Use;
import com.arealytics.core.domain.empiricalProd.SpecificUses;
import com.arealytics.core.domain.empiricalProd.UseType;
import com.arealytics.core.dto.request.PropertyDTO;
import com.arealytics.core.repository.empiricalProd.SpecificUsesRepository;
import com.arealytics.core.repository.empiricalProd.UseRepository;
import com.arealytics.core.repository.empiricalProd.UseTypeRepository;

import lombok.RequiredArgsConstructor;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class PropertyUseService {

    private final UseRepository useRepository;
    private final UseTypeRepository useTypeRepository;
    private final SpecificUsesRepository specificUsesRepository;

    /**
     * Sets use type and specific use details on a property during creation.
     *
     * @param propertyDTO The DTO containing use type and specific use data
     * @param property The property entity to update
     * @throws RuntimeException if useType or specificUse is not found
     */
    public void setUseDetailsForCreate(PropertyDetailsDTO propertyDTO, Property property) {
        Integer useTypeId = propertyDTO.getUseTypeID();
        Integer specificUseId = propertyDTO.getSpecificUseID();
        if (useTypeId != null && useTypeId > 0 && specificUseId != null && specificUseId > 0) {
            // Set use type and specific use IDs
            property.getPropertyDetails().setUseTypeID(useTypeId);
            property.getPropertyDetails().setSpecificUseID(specificUseId);

            // Retrieve and set use type name
            UseType useType =
                    useTypeRepository
                            .findById(useTypeId)
                            .orElseThrow(
                                    () ->
                                            new RuntimeException(
                                                    "UseType not found with ID: " + useTypeId));
            property.getPropertyDetails().setUseTypeName(useType.getUseTypeName());

            // Retrieve and set specific use name
            SpecificUses specificUses =
                    specificUsesRepository
                            .findById(specificUseId)
                            .orElseThrow(
                                    () ->
                                            new RuntimeException(
                                                    "SpecificUses not found with ID: "
                                                            + specificUseId));
            property.getPropertyDetails().setSpecificUseName(specificUses.getSpecificUsesName());
        }
    }

    /**
     * Updates use type and specific use details on a property during update.
     *
     * @param propertyDTO The DTO containing use type and specific use data
     * @param existingProperty The existing property entity
     * @param updatedProperty The property entity being updated
     * @throws RuntimeException if useType or specificUse is not found
     */
    public void setUseDetailsForUpdate(
            PropertyDetailsDTO propertyDTO, Property existingProperty, Property updatedProperty) {
        Integer useTypeId = propertyDTO.getUseTypeID();
        Integer specificUseId = propertyDTO.getSpecificUseID();

        if (useTypeId != null && useTypeId > 0 && specificUseId != null && specificUseId > 0) {
            // Set IDs
            updatedProperty.getPropertyDetails().setUseTypeID(useTypeId);
            updatedProperty.getPropertyDetails().setSpecificUseID(specificUseId);

            // Fetch and set use type name
            UseType useType =
                    useTypeRepository
                            .findById(useTypeId)
                            .orElseThrow(
                                    () ->
                                            new RuntimeException(
                                                    "UseType not found with ID: " + useTypeId));
            updatedProperty.getPropertyDetails().setUseTypeName(useType.getUseTypeName());

            // Fetch and set specific use name
            SpecificUses specificUses =
                    specificUsesRepository
                            .findById(specificUseId)
                            .orElseThrow(
                                    () ->
                                            new RuntimeException(
                                                    "SpecificUses not found with ID: "
                                                            + specificUseId));
            updatedProperty
                    .getPropertyDetails()
                    .setSpecificUseName(specificUses.getSpecificUsesName());
        } else {
            // Keep existing use type information if not provided
            updatedProperty
                    .getPropertyDetails()
                    .setUseTypeID(existingProperty.getPropertyDetails().getUseTypeID());
            updatedProperty
                    .getPropertyDetails()
                    .setSpecificUseID(existingProperty.getPropertyDetails().getSpecificUseID());
            updatedProperty
                    .getPropertyDetails()
                    .setUseTypeName(existingProperty.getPropertyDetails().getUseTypeName());
            updatedProperty
                    .getPropertyDetails()
                    .setSpecificUseName(existingProperty.getPropertyDetails().getSpecificUseName());
        }
    }

    /**
     * Creates or updates a Use record for a property if use type and specific use are provided.
     *
     * @param propertyDTO The DTO containing use type and specific use data
     * @param property The saved property entity
     * @throws RuntimeException if useType or specificUse is not found
     */
    public void saveUseRecord(PropertyDetailsDTO propertyDTO, Property property) {
        Integer useTypeId = propertyDTO.getUseTypeID();
        Integer specificUseId = propertyDTO.getSpecificUseID();

        if (useTypeId != null && useTypeId > 0) {
            // Check for existing Use record
            Optional<Use> existingUse = useRepository.findByParentTableIdAndPropertyAndSequence(
                    ParentTable.Property, property, 1);

            // Use existing record or create a new one
            Use use = existingUse.orElse(new Use());

            // Set fields
            use.setParentTableId(ParentTable.Property);
            use.setProperty(property);
            use.setUseType(useTypeRepository.findById(useTypeId)
                    .orElseThrow(() -> new RuntimeException("UseType not found with ID: " + useTypeId)));
            use.setSequence(1);
            use.setIsActive(true);

            // Set specific use if provided
            if (specificUseId != null && specificUseId > 0) {
                use.setSpecificUses(specificUsesRepository.findById(specificUseId)
                        .orElseThrow(() -> new RuntimeException("SpecificUses not found with ID: " + specificUseId)));
            } else {
                use.setSpecificUses(null);
            }
            useRepository.save(use);
        }
    }
}

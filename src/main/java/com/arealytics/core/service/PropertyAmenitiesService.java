package com.arealytics.core.service;

import com.arealytics.core.domain.empiricalProd.Amenities;
import com.arealytics.core.enumeration.AmenitiesType;
import com.arealytics.core.repository.empiricalProd.AmenitiesRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class PropertyAmenitiesService {

    @Autowired
    private AmenitiesRepository amenitiesRepository;

    public void savePropertyAmenities(Integer propertyId, String amenitiesType) {
        // Parse amenities into a set of AmenitiesType values
        Set<AmenitiesType> amenitiesTypes = amenitiesType != null && !amenitiesType.trim().isEmpty()
                ? Arrays.stream(amenitiesType.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(this::parseAmenityType)
                .collect(Collectors.toSet())
                : Set.of();

        // Find all active amenities for the property
        List<Amenities> activeAmenities = amenitiesRepository.findByPropertyIdAndIsActiveTrue(propertyId);

        if (!amenitiesTypes.isEmpty()) {
            if (activeAmenities.isEmpty()) {
                // No existing amenities, insert all new ones
                for (AmenitiesType amenityType : amenitiesTypes) {
                    Amenities newAmenity = new Amenities();
                    newAmenity.setPropertyId(propertyId);
                    newAmenity.setAmenitiesTypeId(amenityType);
                    newAmenity.setIsActive(true);
                    amenitiesRepository.save(newAmenity);
                }
            } else {
                // Deactivate amenities not in the new list
                for (Amenities existingAmenity : activeAmenities) {
                    if (!amenitiesTypes.contains(existingAmenity.getAmenitiesTypeId())) {
                        existingAmenity.setIsActive(false);
                        amenitiesRepository.save(existingAmenity);
                    }
                }

                // Insert new amenities not already active
                Set<AmenitiesType> existingAmenityTypes = activeAmenities.stream()
                        .map(Amenities::getAmenitiesTypeId)
                        .collect(Collectors.toSet());

                for (AmenitiesType amenityType : amenitiesTypes) {
                    if (!existingAmenityTypes.contains(amenityType)) {
                        Amenities newAmenity = new Amenities();
                        newAmenity.setPropertyId(propertyId);
                        newAmenity.setAmenitiesTypeId(amenityType);
                        newAmenity.setIsActive(true);
                        amenitiesRepository.save(newAmenity);
                    }
                }
            }
        } else {
            // If amenities are null or empty, deactivate all active amenities
            for (Amenities existingAmenity : activeAmenities) {
                existingAmenity.setIsActive(false);
                amenitiesRepository.save(existingAmenity);
            }
        }
    }

    private AmenitiesType parseAmenityType(String name) {
        try {
            return AmenitiesType.valueOf(name);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid AmenitiesType name: " + name);
        }
    }
}

package com.arealytics.core.service;

import com.arealytics.core.dto.response.PropertyDetailsDTO;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.springframework.stereotype.Service;

import com.arealytics.core.domain.empiricalProd.Location;
import com.arealytics.core.mapper.PropertyMapper;
import com.arealytics.core.repository.empiricalProd.LocationRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class LocationService {

    private final LocationRepository locationRepository;
    private final PropertyMapper propertyMapper;

    /**
     * Creates a new location entity for a property with geographical coordinates. Also handles
     * strata relationship for condo properties if applicable.
     *
     * @param propertyDTO Data transfer object containing property and location information
     * @return The saved Location entity
     * @throws IllegalArgumentException if required, fields are missing
     */
    public Location createLocation(PropertyDetailsDTO propertyDTO) {

       if (propertyDTO.getLocationFields() == null || 
    propertyDTO.getLocationFields().getLatitude() == null || 
    propertyDTO.getLocationFields().getLongitude() == null) {
    throw new IllegalArgumentException("Latitude and longitude must not be null for location creation");
}

        // STEP 2: Create location entity from DTO
        Location location = propertyMapper.toLocationEntity(propertyDTO);

        // STEP 3: Set geographical point using latitude and longitude
        GeometryFactory geometryFactory = new GeometryFactory();
        location.setLocationPoint(
                geometryFactory.createPoint(
                        new Coordinate(
                                propertyDTO.getLocationFields().getLongitude().doubleValue(),
                                propertyDTO.getLocationFields().getLatitude().doubleValue())));

        // STEP 4: Save location to a database
        Location savedLocation = locationRepository.save(location);

        // STEP 5: Return the saved location
        return savedLocation;
    }

    /**
     * Updates an existing location or creates a new one based on provided property data. Handles
     * strata relationships and location point geometry.
     *
     * @param propertyDTO The property data transfer object containing location information
     * @param existingLocationId ID of the existing location to update (null if creating new)
     * @return The saved Location entity
     * @throws IllegalArgumentException if required fields are missing
     * @throws RuntimeException if referenced location cannot be found
     */
    public Location updateLocation(PropertyDetailsDTO propertyDTO, Integer existingLocationId) {
        Location location;

        // Validate existingLocationId if updating an existing location
        if (existingLocationId == null && propertyDTO.getPropertyID() != null) {
            throw new IllegalArgumentException("Existing location ID must not be null for update.");
        }

        // SECTION: Retrieve or create location entity
        if (existingLocationId != null) {
            // Update existing location
            location = getAndUpdateExistingLocation(propertyDTO, existingLocationId);
        } else {
            // Create new location
            location = createNewLocation(propertyDTO);
        }

        // SECTION: Update geographical point data
        updateLocationPoint(location, propertyDTO);

        // Save location to repository
        Location savedLocation = locationRepository.save(location);

        return savedLocation;
    }

    /**
     * Retrieves and updates an existing location with non-null values from the DTO.
     *
     * @param propertyDTO DTO containing updated location information
     * @param existingLocationId ID of the existing location
     * @return Updated Location entity
     * @throws RuntimeException if location not found
     */
    private Location getAndUpdateExistingLocation(
            PropertyDetailsDTO propertyDTO, Integer existingLocationId) {
        // Find existing location
        Location location =
                locationRepository
                        .findById(existingLocationId)
                        .orElseThrow(
                                () ->
                                        new RuntimeException(
                                                "Location not found with ID: "
                                                        + existingLocationId));

        // Map updated fields from DTO
        Location updatedLocation = propertyMapper.toLocationEntity(propertyDTO);

        // Update non-null fields only (prevents overwriting with null values)
        if (updatedLocation.getLatitude() != null) {
            location.setLatitude(updatedLocation.getLatitude());
        }

        if (updatedLocation.getLongitude() != null) {
            location.setLongitude(updatedLocation.getLongitude());
        }

        if (updatedLocation.getZCoordinate() != null) {
            location.setZCoordinate(updatedLocation.getZCoordinate());
        }

        if(updatedLocation.getRooftopSourceID() != null){
            location.setRooftopSourceID(updatedLocation.getRooftopSourceID());
        }

        if (updatedLocation.getGisShapeID() != null) {
            location.setGisShapeID(updatedLocation.getGisShapeID());
        }

        return location;
    }

    /**
     * Creates a new Location entity from property DTO data.
     *
     * @param propertyDTO DTO containing location information
     * @return New Location entity
     * @throws IllegalArgumentException if required fields are missing
     */
    private Location createNewLocation(PropertyDetailsDTO propertyDTO) {

        // Validate required fields for new location
        if (propertyDTO.getLocationFields() == null || 
    propertyDTO.getLocationFields().getLatitude() == null || 
    propertyDTO.getLocationFields().getLongitude() == null) {
    throw new IllegalArgumentException("Latitude and longitude must not be null for new location creation");
}

        // Create new location from DTO
        return propertyMapper.toLocationEntity(propertyDTO);
    }

    /**
     * Updates the geographic point data if latitude and longitude are provided.
     *
     * @param location Location entity to update
     * @param propertyDTO DTO containing coordinate data
     */
    private void updateLocationPoint(Location location, PropertyDetailsDTO propertyDTO) {
    if (propertyDTO.getLocationFields() != null &&
        propertyDTO.getLocationFields().getLatitude() != null &&
        propertyDTO.getLocationFields().getLongitude() != null) {
            GeometryFactory geometryFactory = new GeometryFactory();

            // Create point geometry from coordinates
            // Note: In GIS systems, coordinates are typically (longitude, latitude)
            Point point =
                    geometryFactory.createPoint(
                            new Coordinate(
                                    propertyDTO.getLocationFields().getLongitude().doubleValue(),
                                    propertyDTO.getLocationFields().getLatitude().doubleValue()));

            location.setLocationPoint(point);
        }
    }
}

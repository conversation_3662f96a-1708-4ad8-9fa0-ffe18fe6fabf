package com.arealytics.core.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.arealytics.core.domain.empiricalProd.EntityModel;
import com.arealytics.core.domain.empiricalProd.ProductActivityLog;
import com.arealytics.core.domain.empiricalProd.Products;
import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.dto.request.ProductActivityLogRequestDTO;
import com.arealytics.core.mapper.ProductActivityLogMapper;
import com.arealytics.core.repository.empiricalProd.ProductActivityRepository;
import com.arealytics.core.utils.UserContext;

import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;

@Service
@AllArgsConstructor
@RequiredArgsConstructor
public class ProductActivityService {

    @Autowired
    private ProductActivityRepository productActivityRepository;

    @Autowired
    private ProductActivityLogMapper productActivityLogMapper;

    public String saveProductActivityLog(ProductActivityLogRequestDTO dto) {
        ProductActivityLog productActivityLog = productActivityLogMapper.toEntity(dto);

        Property property = new Property();
        property.setPropertyID(dto.getPropertyID());
        productActivityLog.setProperty(property);

        EntityModel entity = new EntityModel();
        Integer entityID=UserContext.getLoginEntity().getEntityId();
        entity.setEntityId(entityID);
        productActivityLog.setEntity(entity);

        Products product = new Products();
        product.setProductID(dto.getProductID());
        productActivityLog.setProduct(product);

        productActivityRepository.save(productActivityLog);
        return "Success";
    }

}

package com.arealytics.core.service;

import com.arealytics.core.constants.AuditLogMetadata;
import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.enumeration.Action;
import com.arealytics.core.enumeration.AuditEntity;
import com.arealytics.core.enumeration.ParentTable;
import com.arealytics.core.enumeration.Unit;
import com.arealytics.core.repository.empiricalProd.*;
import com.arealytics.core.utils.EnumResolver;
import org.springframework.stereotype.Service;


import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import com.arealytics.core.utils.AuditLogUtils;

@Service
public class ChangeLogService {

    private static final int UNIT_ID = Unit.METRIC.getId();

    private final ChangeLogPropertyRepository changeLogRepository;
    private final ChangeLogAddressRepository changeLogAddressRepository;
    private final ChangeLogParcelRepository changeLogParcelRepository;
    private final ChangeLogAdditionalAddressRepository changeLogAdditionalAddressRepository;
    private final ChangeLogMediaRepository changeLogMediaRepository;
    private final EntityService entityService;
    private final AuditLogUtils auditLogUtils;

    public ChangeLogService(
            ChangeLogPropertyRepository changeLogRepository,
            ChangeLogAddressRepository changeLogAddressRepository,
            ChangeLogParcelRepository changeLogParcelRepository,
            ChangeLogAdditionalAddressRepository changeLogAdditionalAddressRepository,
            ChangeLogMediaRepository changeLogMediaRepository,
            EntityService entityService,
            AuditLogUtils auditLogUtils
    ) {
        this.changeLogRepository = changeLogRepository;
        this.changeLogAddressRepository = changeLogAddressRepository;
        this.changeLogParcelRepository = changeLogParcelRepository;
        this.changeLogAdditionalAddressRepository = changeLogAdditionalAddressRepository;
        this.changeLogMediaRepository = changeLogMediaRepository;
        this.entityService = entityService;
        this.auditLogUtils = auditLogUtils;
    }

    public List<Map<String, Object>> getChangeLog(String changeLogType, Integer entityId) {
        List<Map<String, Object>> changeLogs = new ArrayList<>();
        String logType = changeLogType;
        if(changeLogType.equals(AuditEntity.ADDITIONAL_ADDRESS.getName())){
           logType = AuditEntity.ADDRESS.getName();
        }
        AuditLogMetadata.EntityMetadata metadata = AuditLogMetadata.getEntityMetadata(logType);
        String idFieldName = metadata.getIdField();

        switch (ParentTable.valueOf(changeLogType)) {
            case Property-> {
                List<ChangeLogProperty> logs = changeLogRepository.findByPropertyIdOrderByPropertyChangeLogIdDesc(entityId);
                changeLogs.addAll(mapChangeLogs(logs, entityId, idFieldName, ChangeLogProperty::getChangeLogField,
                        ChangeLogProperty::getOldValue, ChangeLogProperty::getNewValue,
                        ChangeLogProperty::getChangedDate, ChangeLogProperty::getAction,
                        ChangeLogProperty::getChangedBy, log -> log.getApplication().getApplicationDescription()));
            }
            case Address -> {
                List<ChangeLogAddress> logs = changeLogAddressRepository.findByAddressParentIdOrderByAddressChangeLogIdDesc(entityId);
                changeLogs.addAll(mapChangeLogs(logs, entityId, idFieldName, ChangeLogAddress::getChangeLogField,
                        ChangeLogAddress::getOldValue, ChangeLogAddress::getNewValue,
                        ChangeLogAddress::getChangedDate, ChangeLogAddress::getAction,
                        ChangeLogAddress::getChangedBy, log -> log.getApplication().getApplicationDescription()));
            }
            case Parcel -> {
                List<ChangeLogParcel> logs = changeLogParcelRepository.findByParcelIdOrderByParcelChangeLogIdDesc(entityId);
                changeLogs.addAll(mapChangeLogs(logs, entityId, idFieldName, ChangeLogParcel::getChangeLogField,
                        ChangeLogParcel::getOldValue, ChangeLogParcel::getNewValue,
                        ChangeLogParcel::getChangedDate, ChangeLogParcel::getAction,
                        ChangeLogParcel::getChangedBy, log -> log.getApplication().getApplicationDescription()));
            }
            case AdditionalAddress -> {
                List<ChangeLogAdditionalAddress> logs = changeLogAdditionalAddressRepository.findByAddressIdOrderByAdditionalAddressChangeLogIdDesc(entityId);
                changeLogs.addAll(mapChangeLogs(logs, entityId, idFieldName, ChangeLogAdditionalAddress::getChangeLogField,
                        ChangeLogAdditionalAddress::getOldValue, ChangeLogAdditionalAddress::getNewValue,
                        ChangeLogAdditionalAddress::getChangedDate, ChangeLogAdditionalAddress::getAction,
                        ChangeLogAdditionalAddress::getChangedBy, log -> log.getApplication().getApplicationDescription()));
            }
            case Media -> {
                List<ChangeLogMedia> logs = changeLogMediaRepository.findByMediaIdOrderByMeidaChangeLogIdDesc(entityId);
                changeLogs.addAll(mapChangeLogs(logs, entityId, idFieldName, ChangeLogMedia::getChangeLogField,
                        ChangeLogMedia::getOldValue, ChangeLogMedia::getNewValue,
                        ChangeLogMedia::getChangedDate, ChangeLogMedia::getAction,
                        ChangeLogMedia::getChangedBy, log -> log.getApplication().getApplicationDescription()));
            }
            default -> throw new IllegalArgumentException("Invalid changelog type: " + changeLogType);
        }

        return changeLogs;
    }

    private <T> List<Map<String, Object>> mapChangeLogs(
            List<T> logs,
            Integer entityId,
            String idFieldName,
            Function<T, ChangeLogFields> fieldExtractor,
            Function<T, String> oldValueExtractor,
            Function<T, String> newValueExtractor,
            Function<T, Instant> dateExtractor,
            Function<T, Action> actionExtractor,
            Function<T, Integer> changedByExtractor,
            Function<T, String> applicationExtractor
    ) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (T log : logs) {
            ChangeLogFields field = fieldExtractor.apply(log);
            result.add(auditLogUtils.createAuditLogEntry(
                    entityId,
                    idFieldName,
                    field != null ? field.getDisplayText() : null,
                    field != null ? field.getFieldName() : null,
                    oldValueExtractor.apply(log),
                    newValueExtractor.apply(log),
                    dateExtractor.apply(log),
                    resolveAction(actionExtractor.apply(log).getLabel()),
                    resolveUser(changedByExtractor.apply(log)),
                    applicationExtractor.apply(log),
                    field != null ? field.getDataTypeId() : null
            ));
        }
        return result;
    }

    private String resolveUser(Integer entityId) {
        return entityId != null ? entityService.getFullNameByEntityId(entityId) : null;
    }

    private String resolveAction(String action) {
        return EnumResolver.resolveEnumLabel(Action.class, action, "label");
    }
}

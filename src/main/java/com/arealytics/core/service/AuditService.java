package com.arealytics.core.service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.RevisionType;
import org.hibernate.envers.query.AuditEntity;
import org.javers.core.Javers;
import org.javers.core.diff.Change;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.ValueChange;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.arealytics.core.constants.AuditLogLookUpData;
import com.arealytics.core.constants.AuditLogMetadata;
import com.arealytics.core.constants.AuditLogMetadata.EnumFieldMapping;
import com.arealytics.core.domain.empiricalProd.Application;
import com.arealytics.core.domain.empiricalProd.ChangeLogFields;
import com.arealytics.core.domain.shared.CustomRevisionEntity;
import com.arealytics.core.enumeration.AuditAction;
import com.arealytics.core.enumeration.FieldDataType;
import com.arealytics.core.enumeration.ParentTable;
import com.arealytics.core.enumeration.Unit;
import com.arealytics.core.repository.empiricalProd.ApplicationRepository;
import com.arealytics.core.repository.empiricalProd.ChangeLogFieldsRepository;
import com.arealytics.core.utils.AuditLogUtils;
import com.arealytics.core.utils.EnumResolver;
import com.arealytics.core.utils.UnitConversionUtil;

import jakarta.persistence.EntityManager;

@Service
public class AuditService {

    @Autowired
    private Javers javers;

    @Autowired
    private ChangeLogFieldsRepository changeLogFieldsRepository;

    @Autowired
    private ApplicationRepository applicationRepository;

    @Autowired
    private AuditLogUtils auditLogUtils;

    @Autowired
    private EntityService entityService;

    private int unitId = Unit.METRIC.getId();

    // Retrieves ChangeLogField details by name, status, and parent table.
    public ChangeLogFields getChangeLogFieldDetailsByName(
            String fieldName, Boolean active, ParentTable tableName) {
        Optional<ChangeLogFields> changeLogField = changeLogFieldsRepository.findByFieldNameAndIsActiveAndParentTableId(
                fieldName, active, tableName);
        return changeLogField.orElse(null);
    }

    // Retrieves Application details by application ID.
    private Application getApplicationDetailsById(Integer applicationId) {
        Optional<Application> application = applicationRepository.findById(applicationId);
        return application.orElse(null);
    }

    public List<Map<String, Object>> fetchAuditLogsByEntityId(String entityType, Integer entityId){
       return fetchAuditLogsByEntityId(entityType, entityId, null);
    }

    // Fetches audit logs for a given entity and its ID.
    public List<Map<String, Object>> fetchAuditLogsByEntityId(String entityType, Integer entityId, Boolean includeAddActioninAuditLog) {
        AuditLogMetadata.EntityMetadata metadata = AuditLogMetadata.getEntityMetadata(entityType);
        Class<?> entityClass = metadata.getEntityClass();
        String idFieldName = metadata.getIdField();
        ParentTable parentTable = metadata.getParentTable();
        Map<String, String> changeLogFieldsMapping = metadata.getChangeLogFieldsMapping();
        Map<String, EnumFieldMapping> enumFieldMapping = metadata.getEnumFieldMappings();
        Map<String, String> lookupFieldMapping = metadata.getFieldToLookupKeyMappings();
        includeAddActioninAuditLog = includeAddActioninAuditLog != null ? includeAddActioninAuditLog : metadata.getIncludeAddActioninAuditLog();
        List<String> fieldsToIgnore = metadata.getFieldsToIgnore();

        if (entityClass == null) {
            throw new IllegalArgumentException("Unsupported entity type: " + entityType);
        }
        EntityManager entityManager = auditLogUtils.getEntityManager(entityType);

        List<Object[]> revisions = fetchEntityRevisions(entityManager, entityClass, entityId);

        return formatAuditLog(
                entityId,
                parentTable,
                entityClass,
                revisions,
                idFieldName,
                changeLogFieldsMapping,
                enumFieldMapping,
                lookupFieldMapping,
                includeAddActioninAuditLog,
                fieldsToIgnore);
    }

    // Fetches the revisions of an entity.
    @SuppressWarnings("unchecked")
    private <T> List<Object[]> fetchEntityRevisions(EntityManager entityManager, Class<T> entityClass,
            Integer entityId) {
        AuditReader auditReader = AuditReaderFactory.get(entityManager);
        return auditReader.createQuery()
                .forRevisionsOfEntity(entityClass, false, true)
                .add(AuditEntity.id().eq(entityId))
                .getResultList();
    }

    // Formats audit logs for entity revisions.
    public <T> List<Map<String, Object>> formatAuditLog(
            Integer entityId,
            ParentTable parentTable,
            Class<T> entityClass,
            List<Object[]> revisions,
            String idFieldName,
            Map<String, String> changeLogFieldsMapping,
            Map<String, EnumFieldMapping> enumFieldMapping,
            Map<String, String> lookupFieldMapping,
            Boolean includeAddActioninAuditLog,
            List<String> fieldsToIgnore) {

        List<Map<String, Object>> changelog = new ArrayList<>();
        T previousEntity = null;

        // Iterate through revisions and compare entity changes
        for (Object[] revision : revisions) {
            @SuppressWarnings("unchecked")
            T currentEntity = (T) revision[0];
            DefaultRevisionEntity revEntity = (DefaultRevisionEntity) revision[1];
            RevisionType revisionType = (RevisionType) revision[2];
            Instant timestamp = Instant.ofEpochMilli(revEntity.getTimestamp());
            String revisionTypeDisplayText = AuditAction.mappedValue(revisionType.name());

            Integer applicationId = null;
            Integer changedById = null;
            String applicationName = null;
            String changedBy = null;

            // Get metadata for the revision (application ID and user)
            if (revEntity instanceof CustomRevisionEntity) {
                CustomRevisionEntity customMetadata = (CustomRevisionEntity) revEntity;
                applicationId = customMetadata.getApplicationId();
                changedById = customMetadata.getChangedBy();
                changedBy = entityService.getFullNameByEntityId(changedById);
                applicationName = getApplicationName(applicationId);
            }

            // Handle ADD and DEL operations
            if (isAddOrDel(revisionTypeDisplayText) && includeAddActioninAuditLog) {
                changelog.add(createAuditLogEntryForAddDel(entityId, idFieldName, timestamp, revisionTypeDisplayText,
                        changedBy, applicationName, null));
            } else if (previousEntity != null) {
                // Compare previous and current entities for changes
                Diff diff = javers.compare(previousEntity, currentEntity);
                List<Change> changes = diff.getChanges();
                processFieldChanges(entityId, idFieldName, changes, timestamp, revisionTypeDisplayText, changedBy,
                        applicationName, changeLogFieldsMapping, parentTable, changelog, enumFieldMapping, lookupFieldMapping, fieldsToIgnore);
            }

            previousEntity = currentEntity;
        }

        // Reverse changelog to show latest changes first
        Collections.reverse(changelog);
        return changelog;
    }

    // Determines if the revision is an ADD or DEL operation.
    private boolean isAddOrDel(String revisionTypeDisplayText) {
        return AuditAction.ADD.getDisplayName().equals(revisionTypeDisplayText) || AuditAction.DEL.getDisplayName().equals(revisionTypeDisplayText);
    }

    // Creates an audit log entry for ADD or DEL operations.
    private Map<String, Object> createAuditLogEntryForAddDel(Integer entityId, String idFieldName, Instant timestamp,
            String revisionTypeDisplayText, String changedBy, String applicationName, Integer dataTypeId) {
        return auditLogUtils.createAuditLogEntry(
                entityId, idFieldName, null, null, null, null, timestamp, revisionTypeDisplayText,
                changedBy, applicationName, dataTypeId);
    }

    // Processes field changes and adds them to the changelog.
    private void processFieldChanges(Integer entityId, String idFieldName, List<Change> changes, Instant timestamp,
            String revisionTypeDisplayText, String changedBy, String applicationName,
            Map<String, String> changeLogFieldsMapping, ParentTable parentTable, List<Map<String, Object>> changelog,
            Map<String, EnumFieldMapping> enumFieldMapping, Map<String, String> lookupFieldMapping, List<String> fieldsToIgnore) {
        for (Change change : changes) {
            if (change instanceof ValueChange) {
                ValueChange valueChange = (ValueChange) change;
                String fieldName = valueChange.getPropertyName();
                Object oldValue = valueChange.getLeft();
                Object newValue = valueChange.getRight();

                if(fieldsToIgnore.contains(fieldName)){
                    continue;
                };

                // Retrieve EnumFieldMapping if it exists
                EnumFieldMapping enumDetails = enumFieldMapping.get(fieldName);

                if (enumDetails != null) {

                    Class<? extends Enum<?>> enumClass = enumDetails.getEnumClass();
                    String labelFieldName = enumDetails.getLabelFieldName();

                    oldValue = EnumResolver.resolveEnumLabel(enumClass, oldValue, labelFieldName);
                    newValue = EnumResolver.resolveEnumLabel(enumClass, newValue, labelFieldName);
                }

                String lookupkey = lookupFieldMapping.get(fieldName);
                if (lookupkey != null) {
                    oldValue = AuditLogLookUpData.resolveName(lookupkey, oldValue);
                    newValue = AuditLogLookUpData.resolveName(lookupkey, newValue);
                }



                String changeLogFieldName = changeLogFieldsMapping.getOrDefault(fieldName, fieldName);

                if (parentTable != null) {

                    // Retrieve details for the changed field
                    ChangeLogFields fieldDetails = this.getChangeLogFieldDetailsByName(changeLogFieldName, true,
                            parentTable);

                    if (fieldDetails != null) {
                        Integer dataTypeId = fieldDetails.getDataTypeId();
                        String fieldDisplayName = fieldDetails.getDisplayText();

                        // Handle data types like SIZE and LENGTH
                        if (unitId == Unit.METRIC.getId()) {
                            oldValue = handleSizeFieldConversions(dataTypeId, oldValue);
                            newValue = handleSizeFieldConversions(dataTypeId, newValue);
                        }

                        changelog.add(auditLogUtils.createAuditLogEntry(
                                entityId, idFieldName, fieldDisplayName, changeLogFieldName, oldValue, newValue,
                                timestamp,
                                revisionTypeDisplayText, changedBy,
                                applicationName, dataTypeId));
                    } else {
                        changelog.add(auditLogUtils.createAuditLogEntry(
                                entityId, idFieldName, changeLogFieldName, changeLogFieldName, oldValue, newValue,
                                timestamp,
                                revisionTypeDisplayText, changedBy,
                                applicationName, null));
                    }
                } else {
                    changelog.add(auditLogUtils.createAuditLogEntry(
                            entityId, idFieldName, changeLogFieldName, changeLogFieldName, oldValue, newValue,
                            timestamp,
                            revisionTypeDisplayText, changedBy,
                            applicationName,null));
                }
            }
        }
    }

    // Handles specific data types (e.g., SIZE and LENGTH) for field changes.
    private Object handleSizeFieldConversions(Integer dataTypeId, Object value) {
        if (dataTypeId != null && value != null) {
            if (dataTypeId.equals(FieldDataType.SIZE.getId())) {
                // Convert the value from sqfeet to sqmeters
                return UnitConversionUtil.sqftToSqm(((Number) value).doubleValue());

            } else if (dataTypeId.equals(FieldDataType.LENGTH.getId())) {
                // Convert the value from feet to meters
                return UnitConversionUtil.feetToMeters(((Number) value).doubleValue());
            }
        }
        return value;
    }

    // Retrieves application name by ID.
    private String getApplicationName(Integer applicationId) {
        if (applicationId != null) {
            Application application = getApplicationDetailsById(applicationId);
            if (application != null) {
                return application.getApplicationDescription();
            }
        }
        return null;
    }
}

package com.arealytics.core.service;

import com.arealytics.core.domain.empiricalProd.Feature;
import com.arealytics.core.enumeration.Features;
import com.arealytics.core.repository.empiricalProd.FeaturesRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class PropertyFeaturesService {

    private final FeaturesRepository featuresRepository;

    @Autowired
    public PropertyFeaturesService(FeaturesRepository featuresRepository) {
        this.featuresRepository = featuresRepository;
    }

    public void savePropertyFeatures(Integer propertyId, String featureIds) {
        Set<Features> requestedFeatures = parseFeatures(featureIds);
        List<Feature> activeFeatures = featuresRepository.findByPropertyIdAndIsActiveTrue(propertyId);

        if (requestedFeatures.isEmpty()) {
            deactivateFeatures(activeFeatures);
            return;
        }

        Set<Features> existingFeatures = activeFeatures.stream()
                .map(Feature::getFeatureId)
                .collect(Collectors.toSet());

        // Deactivate features that are no longer requested
        List<Feature> featuresToDeactivate = activeFeatures.stream()
                .filter(f -> !requestedFeatures.contains(f.getFeatureId()))
                .peek(f -> f.setIsActive(false))
                .collect(Collectors.toList());

        // Insert new features that are not already active
        List<Feature> featuresToInsert = requestedFeatures.stream()
                .filter(f -> !existingFeatures.contains(f))
                .map(f -> createFeature(propertyId, f))
                .collect(Collectors.toList());

        // Save in batches
        if (!featuresToDeactivate.isEmpty()) {
            featuresRepository.saveAll(featuresToDeactivate);
        }
        if (!featuresToInsert.isEmpty()) {
            featuresRepository.saveAll(featuresToInsert);
        }
    }

    private Set<Features> parseFeatures(String featureIds) {
        if (featureIds == null || featureIds.trim().isEmpty()) {
            return Collections.emptySet();
        }

        return Arrays.stream(featureIds.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(this::parseFeature)
                .collect(Collectors.toSet());
    }

    private Features parseFeature(String name) {
        try {
            return Features.valueOf(name);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid feature name: " + name, e);
        }
    }

    private Feature createFeature(Integer propertyId, Features feature) {
        Feature newFeature = new Feature();
        newFeature.setPropertyId(propertyId);
        newFeature.setFeatureId(feature);
        newFeature.setIsActive(true);
        return newFeature;
    }

    private void deactivateFeatures(List<Feature> features) {
        for (Feature feature : features) {
            feature.setIsActive(false);
        }
        featuresRepository.saveAll(features);
    }
}

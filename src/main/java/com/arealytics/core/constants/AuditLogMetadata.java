package com.arealytics.core.constants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.arealytics.core.domain.empiricalGIS.BuildingFootPrint;
import com.arealytics.core.domain.empiricalProd.Address;
import com.arealytics.core.domain.empiricalProd.Location;
import com.arealytics.core.domain.empiricalProd.Media;
import com.arealytics.core.domain.empiricalProd.MediaRelationship;
import com.arealytics.core.domain.empiricalProd.Parcel;
import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.enumeration.AddressType;
import com.arealytics.core.enumeration.AuditEntity;
import com.arealytics.core.enumeration.BuildSpecStatus;
import com.arealytics.core.enumeration.ClassType;
import com.arealytics.core.enumeration.CondoType;
import com.arealytics.core.enumeration.ConstructionStatus;
import com.arealytics.core.enumeration.ConstructionType;
import com.arealytics.core.enumeration.EnergyStarRating;
import com.arealytics.core.enumeration.GovernmentInterest;
import com.arealytics.core.enumeration.GreenStarRating;
import com.arealytics.core.enumeration.HVACType;
import com.arealytics.core.enumeration.MediaRelationType;
import com.arealytics.core.enumeration.MediaSource;
import com.arealytics.core.enumeration.MediaSubType;
import com.arealytics.core.enumeration.MediaType;
import com.arealytics.core.enumeration.ParentTable;
import com.arealytics.core.enumeration.Prefix;
import com.arealytics.core.enumeration.Quadrant;
import com.arealytics.core.enumeration.RoofTopSource;
import com.arealytics.core.enumeration.RoofType;
import com.arealytics.core.enumeration.SizeSource;
import com.arealytics.core.enumeration.SprinklerType;
import com.arealytics.core.enumeration.Tenancy;
import com.arealytics.core.enumeration.TypicalFloorPlate;
import com.arealytics.core.enumeration.UseType;
import com.arealytics.core.enumeration.WaterStarRating;
import com.arealytics.core.enumeration.ZoningClass;
import com.arealytics.core.enumeration.ComplexType;

import lombok.AllArgsConstructor;
import lombok.Data;

public class AuditLogMetadata {

    // Entity-specific data (class, ID field, and change log fields mapping)
    public static final Map<String, EntityMetadata> ENTITY_METADATA_MAP = new HashMap<>();

    static {
        // Parcel metadata
        Map<String, String> parcelFieldMappings = new HashMap<>();
        parcelFieldMappings.put("parcelSize", "ParcelSF");
        parcelFieldMappings.put("parcelNo", "ParcelNo");
        parcelFieldMappings.put("lot", "Lot");
        parcelFieldMappings.put("block", "Block");
        parcelFieldMappings.put("subDivision", "Subdivision");

        ENTITY_METADATA_MAP.put(AuditEntity.PARCEL.getName(), new EntityMetadata(
                Parcel.class,
                AuditEntity.PARCEL.getIdFieldName(),
                parcelFieldMappings,
                new HashMap<>(),
                new HashMap<>(),
                ParentTable.Parcel,
                true,
                new ArrayList<>()));

        // Parcel metadata

        Map<String, String> buildingFootPrintFieldMappings = new HashMap<>();
        buildingFootPrintFieldMappings.put("floors", "Floors");
        buildingFootPrintFieldMappings.put("useTypeId", "UseTypeID");
        buildingFootPrintFieldMappings.put("additionalUseTypeId", "AddlGeneralUse");
        buildingFootPrintFieldMappings.put("mainSpecificUseTypeId", "SpecificUseID");
        buildingFootPrintFieldMappings.put("additionalSpecificUseTypeId", "AddlSpecificUse");

        Map<String, String> buildingFootPrintFieldToLookupMappings = new HashMap<>();
        buildingFootPrintFieldToLookupMappings.put("mainSpecificUseTypeId", "SpecificUsesID");
        buildingFootPrintFieldToLookupMappings.put("additionalSpecificUseTypeId", "SpecificUsesID");
        buildingFootPrintFieldToLookupMappings.put("useTypeId", "UseTypeID");
        buildingFootPrintFieldToLookupMappings.put("additionalUseTypeId", "UseTypeID");

        ENTITY_METADATA_MAP.put(AuditEntity.BUILDING_FOOTPRINT.getName(), new EntityMetadata(
                BuildingFootPrint.class,
                AuditEntity.BUILDING_FOOTPRINT.getIdFieldName(),
                buildingFootPrintFieldMappings,
                new HashMap<>(),
                buildingFootPrintFieldToLookupMappings,
                ParentTable.Property,
                true,
                new ArrayList<>()));

        // Property metadata
        Map<String, String> propertyFieldMappings = new HashMap<>();

        propertyFieldMappings.put("yearBuilt", "YearBuilt");
        propertyFieldMappings.put("yearRenovated", "YearRenovated");
        propertyFieldMappings.put("floors", "Floors");
        propertyFieldMappings.put("constructionStatusID", "ConstructionStatusID");
        propertyFieldMappings.put("constructionTypeID", "ConstructionTypeID");
        propertyFieldMappings.put("hvacTypeID", "HVACTypeID");
        propertyFieldMappings.put("sprinklerTypeID", "SprinklerTypeID");
        propertyFieldMappings.put("useTypeID", "UseTypeID");
        propertyFieldMappings.put("specificUseID", "SpecificUseID");
        propertyFieldMappings.put("isADAAccessible", "IsADAAccessible");
        propertyFieldMappings.put("isVented", "IsVented");
        propertyFieldMappings.put("isOwnerOccupied", "IsOwnerOccupied");
        propertyFieldMappings.put("tenancyTypeID", "TenancyTypeID");
        propertyFieldMappings.put("classTypeID", "ClassTypeID");
        propertyFieldMappings.put("buildingWebsite", "BuildingWebsite");
        propertyFieldMappings.put("buildingComments", "BuildingComments");
        propertyFieldMappings.put("governmentInterestID", "GovernmentInterestID");
        propertyFieldMappings.put("noOfOfficeFloors", "NoOfOfficeFloor");
        propertyFieldMappings.put("craneServed", "CraneServed");
        propertyFieldMappings.put("roofTypeID", "RoofTypeID");
        propertyFieldMappings.put("hasSolar", "HasSolar");
        propertyFieldMappings.put("trafficCount", "TrafficCount");
        propertyFieldMappings.put("earthquakeZoneID", "EarthquakeZoneID");
        propertyFieldMappings.put("energyStarRatingID", "EnergyStarRatingID");
        propertyFieldMappings.put("waterStarRatingID", "WaterStarRatingID");
        propertyFieldMappings.put("greenStarRatingID", "GreenStarRatingID");
        propertyFieldMappings.put("occupiedPercentage", "OccupiedPercentage");
        propertyFieldMappings.put("currentTitle", "CurrentTitle");
        propertyFieldMappings.put("tiAllowance", "TIAllowance");
        propertyFieldMappings.put("gresbScore", "GRESBScore");
        propertyFieldMappings.put("gresbScoreMin", "GRESBScoreMin");
        propertyFieldMappings.put("gresbScoreMax", "GRESBScoreMax");
        propertyFieldMappings.put("actualCompletion", "ActualCompletion");
        propertyFieldMappings.put("titleReferenceDate", "TitleReferenceDate");
        propertyFieldMappings.put("landUse", "LandUse");
        propertyFieldMappings.put("lastReviewedBy", "LastReviewedBy");
        propertyFieldMappings.put("lastReviewedDate", "LastReviewedDate");
        propertyFieldMappings.put("constructionStartDate", "ConstructionStartDate");
        propertyFieldMappings.put("estCompletion", "EstCompletionDate");
        propertyFieldMappings.put("bookValue", "BookValue");
        propertyFieldMappings.put("bookValueDate", "BookValueDate");
        propertyFieldMappings.put("marketId", "MarketId");
        propertyFieldMappings.put("subMarketID", "SubMarketID");
        propertyFieldMappings.put("zoning", "Zoning");
        propertyFieldMappings.put("zoningClassID", "ZoningClass");
        propertyFieldMappings.put("zoningCode", "ZoningCode");
        propertyFieldMappings.put("surroundingLandUse", "SurroundingLandUse");
        propertyFieldMappings.put("amenities", "Amenities");
        propertyFieldMappings.put("parkingSpaces", "ParkingSpaces");
        propertyFieldMappings.put("parkingRatio", "ParkingRatio");
        propertyFieldMappings.put("passengerElevators", "PassengerElevators");
        propertyFieldMappings.put("parkingElevators", "ParkingElevators");
        propertyFieldMappings.put("freighElevators", "FreighElevators");
        propertyFieldMappings.put("dockHigh", "DockHigh");
        propertyFieldMappings.put("gradeLevelIn", "GradeLevelDriveIn");
        propertyFieldMappings.put("truckWell", "Truckwell");
        propertyFieldMappings.put("phase", "Phase");
        propertyFieldMappings.put("volts", "Volts");
        propertyFieldMappings.put("amps", "Amps");
        propertyFieldMappings.put("powerComments", "PowerComments");
        propertyFieldMappings.put("bayWidth", "BayWidth");
        propertyFieldMappings.put("bayDepth", "BayDepth");
        propertyFieldMappings.put("includeInAnalytics", "IncludeinAnalytics");
        propertyFieldMappings.put("railServed", "RailServed");
        propertyFieldMappings.put("isFloodPlain", "IsFloodPlain");
        propertyFieldMappings.put("buildSpecStatusID", "BuildSpecStatusID");
        propertyFieldMappings.put("hasYardFenced", "HasYardFenced");
        propertyFieldMappings.put("hasYardUnfenced", "HasYardUnfenced");
        propertyFieldMappings.put("yardPaved", "YardPaved");
        propertyFieldMappings.put("reservedParkingSpaces", "ReservedParkingSpaces");
        propertyFieldMappings.put("reservedParkingSpacesRatePerMonth", "ReservedParkingSpacesRatePerMonth");
        propertyFieldMappings.put("hasReservedParkingSpaces", "HasReservedParkingSpaces");
        propertyFieldMappings.put("depth", "Depth");
        propertyFieldMappings.put("width", "Width");
        propertyFieldMappings.put("internalComments", "InternalComments");
        propertyFieldMappings.put("noOfAnchor", "Anchors");
        propertyFieldMappings.put("hasSprinkler", "HasSprinkler");
        propertyFieldMappings.put("hasPortAccess", "HasPortAccess");
        propertyFieldMappings.put("hasYard", "HasYard");
        propertyFieldMappings.put("unreservedParkingSpaces", "UnreservedParkingSpaces");
        propertyFieldMappings.put("unreservedParkingSpacesRatePerMonth", "UnreservedParkingSpacesRatePerMonth");
        propertyFieldMappings.put("hasUnreservedParkingSpaces", "HasUnreservedParkingSpaces");
        propertyFieldMappings.put("propertyComments", "PropertyComments");
        propertyFieldMappings.put("utilityComments", "UtilityComments");
        propertyFieldMappings.put("noOfUnits", "NoOfUnits");
        propertyFieldMappings.put("totalAnchor", "TotalAnchorSF");
        propertyFieldMappings.put("hvac", "HVAC");
        propertyFieldMappings.put("lifts", "Lifts");
        propertyFieldMappings.put("liftsCount", "LiftsCount");
        propertyFieldMappings.put("powerType", "PowerType");
        propertyFieldMappings.put("vacancy", "Vacancy");
        propertyFieldMappings.put("clearHeightMin", "ClearHeightMin");
        propertyFieldMappings.put("clearHeightMax", "ClearHeightMax");
        propertyFieldMappings.put("retailFrontage", "RetailFrontage");
        propertyFieldMappings.put("hardstandArea", "HardstandArea");
        propertyFieldMappings.put("hardstandAreaSourceID", "HardstandAreaSourceID");
        propertyFieldMappings.put("contributedGBASizeSourceID", "ContributedGBASizeSourceID");
        propertyFieldMappings.put("glasSizeSourceID", "GLASizeSourceID");
        propertyFieldMappings.put("glarSizeSourceID", "GLARSizeSourceID");
        propertyFieldMappings.put("mezzanine", "Mezzanine");
        propertyFieldMappings.put("awnings", "Awnings");
        propertyFieldMappings.put("awningsCount", "AwningsCount");
        propertyFieldMappings.put("officeSize", "OfficeSF");
        propertyFieldMappings.put("buildingSizeSF", "BuildingSF");
        propertyFieldMappings.put("lotSizeSF", "LotSizeSF");
        propertyFieldMappings.put("contributedGBASizeSF", "ContributedGBA_SF");
        propertyFieldMappings.put("glaSizeSF", "GLA_SF");
        propertyFieldMappings.put("glarSizeSF", "GLAR_SF");
        propertyFieldMappings.put("mezzanineSizeSF", "Mezzanine_Size_SF");
        propertyFieldMappings.put("awningsSizeSF", "Awnings_Size_SF");
        propertyFieldMappings.put("lotSizeAC", "LotSizeAc");
        propertyFieldMappings.put("lotSizeSourceID", "LotSizeSourceID");
        propertyFieldMappings.put("NRASizeSourceID", "NRASizeSourceId");
        propertyFieldMappings.put("smallestFloor", "SmallestFloor");
        propertyFieldMappings.put("largestFloor", "LargestFloor");
        propertyFieldMappings.put("nla", "NLA_SF");
        propertyFieldMappings.put("nlaac", "NLAac");
        propertyFieldMappings.put("researchTypeID", "PropertyResearchType");
        propertyFieldMappings.put("isSkipped", "IsSkipped");
        propertyFieldMappings.put("isMultiplePolygonsNeeded", "IsMultiplePolygonsNeeded");
        propertyFieldMappings.put("needsResearchComments", "NeedsResearchComments");

        Map<String, EnumFieldMapping> propertyEnumMappings = new HashMap<>();
        propertyEnumMappings.put("constructionStatusID", new EnumFieldMapping(ConstructionStatus.class, "label"));
        propertyEnumMappings.put("constructionTypeID", new EnumFieldMapping(ConstructionType.class, "label"));
        propertyEnumMappings.put("hvacTypeID", new EnumFieldMapping(HVACType.class, "label"));
        propertyEnumMappings.put("sprinklerTypeID", new EnumFieldMapping(SprinklerType.class, "label"));
        propertyEnumMappings.put("condoTypeID", new EnumFieldMapping(CondoType.class, "label"));
        propertyEnumMappings.put("useTypeID  ", new EnumFieldMapping(UseType.class, "label"));
        propertyEnumMappings.put("tenancyTypeID", new EnumFieldMapping(Tenancy.class, "label"));
        propertyEnumMappings.put("classTypeID", new EnumFieldMapping(ClassType.class, "label"));
        propertyEnumMappings.put("governmentInterestID", new EnumFieldMapping(GovernmentInterest.class, "label"));
        propertyEnumMappings.put("roofTypeID", new EnumFieldMapping(RoofType.class, "label"));
        propertyEnumMappings.put("energyStarRatingID", new EnumFieldMapping(EnergyStarRating.class, "label"));
        propertyEnumMappings.put("waterStarRatingID", new EnumFieldMapping(WaterStarRating.class, "label"));
        propertyEnumMappings.put("greenStarRatingID", new EnumFieldMapping(GreenStarRating.class, "label"));
        propertyEnumMappings.put("zoningClassID", new EnumFieldMapping(ZoningClass.class, "label"));
        propertyEnumMappings.put("officeAC", new EnumFieldMapping(HVACType.class, "label"));
        propertyEnumMappings.put("potentialZoningID", new EnumFieldMapping(ZoningClass.class, "label"));
        propertyEnumMappings.put("buildSpecStatusID", new EnumFieldMapping(BuildSpecStatus.class, "label"));
        propertyEnumMappings.put("sizeSourceID", new EnumFieldMapping(SizeSource.class, "label"));
        propertyEnumMappings.put("hardstandAreaSourceID", new EnumFieldMapping(SizeSource.class, "label"));
        propertyEnumMappings.put("contributedGBASizeSourceID", new EnumFieldMapping(SizeSource.class, "label"));
        propertyEnumMappings.put("glasSizeSourceID", new EnumFieldMapping(SizeSource.class, "label"));
        propertyEnumMappings.put("glarSizeSourceID", new EnumFieldMapping(SizeSource.class, "label"));
        propertyEnumMappings.put("lotSizeSourceID", new EnumFieldMapping(SizeSource.class, "label"));
        propertyEnumMappings.put("NRASizeSourceID", new EnumFieldMapping(SizeSource.class, "label"));
        propertyEnumMappings.put("typicalFloorSizeSourceId", new EnumFieldMapping(TypicalFloorPlate.class, "label"));

        Map<String, String> propertyFieldToLookupMappings = new HashMap<>();
        propertyFieldToLookupMappings.put("subMarketID", "SubMarketID");
        propertyFieldToLookupMappings.put("marketId", "MarketID");
        propertyFieldToLookupMappings.put("useTypeID", "UseTypeID");

        List<String> propertyFieldsToIgnore = new ArrayList<>();
        propertyFieldsToIgnore.add("specificUseID");
        propertyFieldsToIgnore.add("researchTypeID");

        ENTITY_METADATA_MAP.put(AuditEntity.PROPERTY.getName(), new EntityMetadata(
                Property.class,
                AuditEntity.PROPERTY.getIdFieldName(),
                propertyFieldMappings,
                propertyEnumMappings,
                propertyFieldToLookupMappings,
                ParentTable.Property,
                true,
                propertyFieldsToIgnore));

        // Address metadata
        Map<String, EnumFieldMapping> addressEnumMappings = new HashMap<>();
        addressEnumMappings.put("addressTypeId", new EnumFieldMapping(AddressType.class, "label"));
        addressEnumMappings.put("prefixId", new EnumFieldMapping(Prefix.class, "label"));
        addressEnumMappings.put("prefix2Id", new EnumFieldMapping(Prefix.class, "label"));
        addressEnumMappings.put("quadrantId", new EnumFieldMapping(Quadrant.class, "label"));
        addressEnumMappings.put("partOfCenterComplex", new EnumFieldMapping(ComplexType.class, "label"));

        // Parcel metadata
        Map<String, String> addressFieldMappings = new HashMap<>();
        addressFieldMappings.put("addressTypeId", "AddressType");
        addressFieldMappings.put("stateId", "State");
        addressFieldMappings.put("cityId", "City");
        addressFieldMappings.put("addressStreetName", "AddressStreetName");
        addressFieldMappings.put("suffixId", "StreetSuffix1");
        addressFieldMappings.put("suffix2Id", "StreetSuffix2");
        addressFieldMappings.put("countyId", "County");
        addressFieldMappings.put("streetNumberMin", "StreetNumberMin");
        addressFieldMappings.put("streetNumberMax", "StreetNumberMax");
        addressFieldMappings.put("floorNumber", "FloorNumber");
        addressFieldMappings.put("prefixId", "StreetPrefix1");
        addressFieldMappings.put("prefix2Id", "StreetPrefix2");
        addressFieldMappings.put("quadrantId", "Quadrant");
        addressFieldMappings.put("partOfCenterComplex", "PartOfComplex");
        addressFieldMappings.put("complexName", "Complex");
        addressFieldMappings.put("primaryStreet", "PrimaryStreet");
        addressFieldMappings.put("primaryAccess", "PrimaryAccess");
        addressFieldMappings.put("primaryTrafficCount", "PrimaryTrafficCount");
        addressFieldMappings.put("primaryTrafficCountDate", "PrimaryTrafficCountDate");
        addressFieldMappings.put("primaryFrontage", "PrimaryFrontage");
        addressFieldMappings.put("secondaryStreet", "SecondaryStreet");
        addressFieldMappings.put("secondaryAccess", "SecondaryAccess");
        addressFieldMappings.put("secondaryTrafficCount", "SecondaryTrafficCount");
        addressFieldMappings.put("secondaryTrafficCountDate", "SecTrafficCntDate");
        addressFieldMappings.put("secondaryFrontage", "SecondaryFrontage");
        addressFieldMappings.put("eastWestSt", "EastWestStreet");
        addressFieldMappings.put("northSouthSt", "NorthSouthStreet");
        addressFieldMappings.put("countryId", "CountryID");

        Map<String, String> addressFieldToLookupMappings = new HashMap<>();
        addressFieldToLookupMappings.put("stateId", "StateID");
        addressFieldToLookupMappings.put("cityId", "CityID");
        addressFieldToLookupMappings.put("suffixId", "StreetSuffix");
        addressFieldToLookupMappings.put("suffix2Id", "StreetSuffix");
        addressFieldToLookupMappings.put("countyId", "CouncilID");
        addressFieldToLookupMappings.put("marketId", "MarketID");
        addressFieldToLookupMappings.put("countryId", "CountryID");

        // Address metadata
        ENTITY_METADATA_MAP.put(AuditEntity.ADDRESS.getName(), new EntityMetadata(
                Address.class,
                AuditEntity.ADDRESS.getIdFieldName(),
                addressFieldMappings,
                addressEnumMappings,
                addressFieldToLookupMappings,
                ParentTable.Address,
                true,
                new ArrayList<>()));

        // Location metadata

        Map<String, String> locationFieldMappings = new HashMap<>();
        locationFieldMappings.put("latitude", "Latitude");
        locationFieldMappings.put("longitude", "Longitude");
        locationFieldMappings.put("rooftopSourceID", "RooftopSourceID");

        Map<String, EnumFieldMapping> locationEnumMappings = new HashMap<>();

        locationEnumMappings.put("rooftopSourceID", new EnumFieldMapping(RoofTopSource.class, "label"));

        ENTITY_METADATA_MAP.put("Location", new EntityMetadata(
                Location.class,
                "LocationID",
                locationFieldMappings,
                locationEnumMappings,
                new HashMap<>(),
                ParentTable.Address,
                false,
                new ArrayList<>()));

        // MediaRelationShip metadata
        Map<String, String> MediaRelationshipFieldMappings = new HashMap<>();
        MediaRelationshipFieldMappings.put("mediaTypeId", "MediaTypeID");
        MediaRelationshipFieldMappings.put("mediaSubTypeId", "MediaSubTypeID");
        MediaRelationshipFieldMappings.put("isDefault", "IsDefault");

        Map<String, EnumFieldMapping> MediaRelationshipEnumMappings = new HashMap<>();

        MediaRelationshipEnumMappings.put("mediaRelationTypeId",
                        new EnumFieldMapping(MediaRelationType.class, "label"));
        MediaRelationshipEnumMappings.put("mediaTypeId", new EnumFieldMapping(MediaType.class, "name"));
        MediaRelationshipEnumMappings.put("mediaSubTypeId", new EnumFieldMapping(MediaSubType.class, "name"));

        ENTITY_METADATA_MAP.put(AuditEntity.MEDIA_RELATIONSHIP.getName(), new EntityMetadata(
                        MediaRelationship.class,
                        AuditEntity.MEDIA_RELATIONSHIP.getIdFieldName(),
                        MediaRelationshipFieldMappings,
                        MediaRelationshipEnumMappings,
                        new HashMap<>(),
                        ParentTable.Media,
                        false,
                        new ArrayList<>()));

        // Media metadata
        Map<String, String> MediaFieldMappings = new HashMap<>();
        MediaFieldMappings.put("mediaName", "MediaName");
        MediaFieldMappings.put("description", "Description");
        MediaFieldMappings.put("isOwnMedia", "IsOwnMedia");
        MediaFieldMappings.put("mediaSourceId", "MediaSourceID");
        MediaFieldMappings.put("sourceComments", "SourceComments");

        Map<String, EnumFieldMapping> MediaEnumMappings = new HashMap<>();

        MediaEnumMappings.put("mediaSourceId", new EnumFieldMapping(MediaSource.class, "name"));

        ENTITY_METADATA_MAP.put(AuditEntity.MEDIA.getName(), new EntityMetadata(
                        Media.class,
                        AuditEntity.MEDIA.getIdFieldName(),
                        MediaFieldMappings,
                        MediaEnumMappings,
                        new HashMap<>(),
                        ParentTable.Media,
                        true,
                        new ArrayList<>()));
}

    // Get the metadata for a given entity type
    public static EntityMetadata getEntityMetadata(String entityType) {
        return ENTITY_METADATA_MAP.get(entityType);
    }

    // EntityMetadata class to hold the relevant data
    @Data
    @AllArgsConstructor
    public static class EntityMetadata {
        private final Class<?> entityClass;
        private final String idField;
        private final Map<String, String> changeLogFieldsMapping;
        private final Map<String, EnumFieldMapping> enumFieldMappings;
        private final Map<String, String> fieldToLookupKeyMappings;
        private final ParentTable parentTable;
        private final Boolean includeAddActioninAuditLog;
        private final List<String> fieldsToIgnore;
    }

    // EnumFieldMapping class to map enum metadata
    @Data
    @AllArgsConstructor
    public static class EnumFieldMapping {
        private final Class<? extends Enum<?>> enumClass;
        private final String labelFieldName;
    }
}

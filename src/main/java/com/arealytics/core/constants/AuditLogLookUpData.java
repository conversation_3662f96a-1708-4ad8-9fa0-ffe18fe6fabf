package com.arealytics.core.constants;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.arealytics.core.domain.empiricalProd.Country;
import com.arealytics.core.dto.response.CountryDTO;
import com.arealytics.core.mapper.CountryMapper;
import com.arealytics.core.repository.empiricalProd.*;
import com.arealytics.core.service.LookupService;
import com.arealytics.core.specification.MarketSpecification;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class AuditLogLookUpData {

    private final CityRepository cityRepository;
    private final CountyRepository countyRepository;
    private final SuffixRepository suffixRepository;
    private final StateRepository stateRepository;
    private final SubMarketRepository subMarketRepository;
    private final MarketRepository marketRepository;
    private final CountryRepository countryRepository;
    private final CountryMapper countryMapper;
    private final SpecificUsesRepository specificUsesRepository;
    private final UseTypeRepository useTypeRepository;

    @Autowired
    private LookupService lookupService;

    private final Integer countryId = AppConstants.DEFAULT_COUNTRY_ID;

    private static final Map<String, Object> lookupData = new HashMap<>();

    // Populate lookupData during class initialization
    @PostConstruct
    private void initializeLookupData() {
        // City data
        lookupData.put(
                "CityID",
                lookupService.mapRepositoryData(
                        cityRepository.findByIsActiveTrueOrderByCityNameAsc(),
                        city ->
                                Map.of(
                                        "id", city.getCityId(),
                                        "name", city.getCityName())));

        // County/Council data
        lookupData.put(
                "CouncilID",
                lookupService.mapRepositoryData(
                        countyRepository.findByIsActiveTrueOrderByCountyNameAsc(),
                        county ->
                                Map.of(
                                        "id", county.getCountyId(),
                                        "name", county.getCountyName())));

        // Country
        Country country = countryRepository.findByCountryIdAndIsActiveTrue(countryId);
        CountryDTO countryDTO = countryMapper.toDto(country);
        lookupData.put("CountryID", List.of(countryDTO));

        // Street suffix data
        List<Map<String, Object>> suffixData =
                lookupService.mapRepositoryData(
                        suffixRepository.findByIsActive(true),
                        suffix ->
                                Map.of(
                                        "id", suffix.getSuffixId(),
                                        "name", suffix.getSuffix()));
        lookupData.put("StreetSuffix", suffixData);

        // State data
        lookupData.put(
                "StateID",
                lookupService.mapRepositoryData(
                        stateRepository.findByIsActive(true),
                        state ->
                                Map.of(
                                        "id", state.getStateId(),
                                        "name", state.getStateName())));

        // Submarket data
        lookupData.put(
                "SubMarketID",
                lookupService.mapRepositoryData(
                        subMarketRepository.findByIsActive(true),
                        subMarket ->
                                Map.of(
                                        "id", subMarket.getSubMarketId(),
                                        "name", subMarket.getSubMarketName())));
        // Market data
        lookupData.put(
                "MarketID",
                lookupService.mapRepositoryData(
                        marketRepository.findAll(MarketSpecification.isActive()),
                        market ->
                                Map.of(
                                        "id", market.getMarketId(),
                                        "name", market.getMarketName())));
        // Specific Uses data
        lookupData.put(
                "SpecificUsesID",
                lookupService.mapRepositoryData(
                        specificUsesRepository.findByIsActive(true),
                        use ->
                                Map.of(
                                        "id", use.getSpecificUsesId(),
                                        "name", use.getSpecificUsesName()
                                        )));

        // UseType data
        lookupData.put(
                        "UseTypeID",
                        lookupService.mapRepositoryData(
                                        useTypeRepository.findByIsActiveTrue(),
                                        use -> Map.of(
                                                        "id", use.getUseTypeId(),
                                                        "name", use.getUseTypeName())));
    }

    /** Resolves a name based on field name and ID */
    public static Object resolveName(String lookupKey, Object id) {
        if (lookupKey == null || id == null) {
            return id;
        }

        Object data = lookupData.get(lookupKey);

        if (data instanceof List<?>) {
            for (Object item : (List<?>) data) {
                if (item instanceof Map<?, ?>) {
                    Map<?, ?> itemMap = (Map<?, ?>) item;
                    if ((int) id == (int) itemMap.get("id")) {
                        return (String) itemMap.get("name");
                    }
                }
            }
        }

        return id;
    }
}

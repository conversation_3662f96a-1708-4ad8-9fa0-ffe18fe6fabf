package com.arealytics.core.constants;

public final class LoginErrorMessages {

    private LoginErrorMessages() {}

    public static final String ACCOUNT_LOCKED_COMMERCIAL_AU =
            "Sorry! Your account is locked. Please contact CommercialAU Customer Support to continue.";

    public static final String NOT_SUBSCRIBED_COMMERCIAL_AU =
            "Sorry! You are not subscribed to use the system. Please contact CommercialAU Customer Support to continue.";

    public static final String NOT_ALLOWED_INTERNAL_SYSTEM =
            "Sorry! You are not allowed to use the internal system. Please contact Arealytics Customer Support to continue.";

    public static final String ACCOUNT_LOCKED_AREALYTICS =
            "Sorry! Your account is locked. Please contact Arealytics Customer Support to continue.";

    public static final String ACCOUNT_INACTIVE =
            "Sorry! Your account is inactive. Please contact Arealytics Customer Support to continue.";

    public static final String COMPANY_NOT_SUBSCRIBED =
            "Sorry! Your company is not subscribed to use the system. Please contact Arealytics Customer Support to continue.";

    public static final String NOT_SUBSCRIBED_AREALYTICS =
            "Sorry! You are not subscribed to use the system. Please contact Arealytics Customer Support to continue.";

    public static final String INVALID_USERNAME_PASSWORD =
            "Invalid Username/Password";
}

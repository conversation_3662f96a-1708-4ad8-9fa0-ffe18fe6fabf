package com.arealytics.core.controller;

import java.util.List;

import com.arealytics.core.dto.response.PropertyResearchResponseDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.arealytics.core.constants.ApiResponseConstants;
import com.arealytics.core.dto.response.ApiResponse;
import com.arealytics.core.service.PropertyResearchStatusService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/researchStatus")
@RequiredArgsConstructor
@Tag(name = "Research", description = "Operations related to Research Status")
public class ResearchController {
  private final PropertyResearchStatusService propertyResearchStatusService;

  /**
   * Fetches the research status history for a given property ID.
   *
   * @param propertyId The ID of the property for which to fetch the research
   *                   status history.
   * @return A ResponseEntity containing the API response with the research status
   *         history.
   */
  @Operation(summary = "Fetch Research Status History", description = "This API retrieves the research status history for a given property ID.")
  @GetMapping("/{propertyId}/changelog")
  public ResponseEntity<ApiResponse<List<PropertyResearchResponseDTO>>> getResearchStatusHistory(
      @PathVariable Integer propertyId) {
    List<PropertyResearchResponseDTO> researchStatusHistory = propertyResearchStatusService
        .getResearchStatusHistory(propertyId);
    ApiResponse<List<PropertyResearchResponseDTO>> response = new ApiResponse<>(ApiResponseConstants.IS_ERROR,
        ApiResponseConstants.SUCCESS_MESSAGE, researchStatusHistory, HttpStatus.OK.value());
    return ResponseEntity.ok(response);
  }
}

package com.arealytics.core.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.arealytics.core.constants.ApiResponseConstants;
import com.arealytics.core.dto.response.ApiResponse;
import com.arealytics.core.dto.response.NotesResponseDTO;
import com.arealytics.core.enumeration.ParentTable;
import com.arealytics.core.enumeration.StatusCode;
import com.arealytics.core.service.NotesService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Null;
import lombok.RequiredArgsConstructor;
import net.ttddyy.dsproxy.QueryCount;
import net.ttddyy.dsproxy.QueryCountHolder;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.arealytics.core.dto.request.NotesRequestDTO;

@RestController
@RequestMapping("/notes")
@RequiredArgsConstructor
@Tag(name = "Notes", description = "Operations related to notes")
public class NotesController {
    private final NotesService notesService;

    @Operation(summary = "Get Notes by Parent ID and Parent Table", description = "Retrieves a list of notes associated with the given parent ID and parent table type.")
    @GetMapping("/{parentId}/{parentTableId}")
    public ResponseEntity<ApiResponse<List<NotesResponseDTO>>> getNotes(@PathVariable Integer parentId,
            @PathVariable  ParentTable parentTableId) {
        List<NotesResponseDTO> notesDetails = notesService.getNotes(parentId, parentTableId);
        ApiResponse<List<NotesResponseDTO>> response = new ApiResponse<>(
                ApiResponseConstants.IS_ERROR,
                ApiResponseConstants.SUCCESS_MESSAGE,
                notesDetails,
                StatusCode.SUCCESS.getStatus().value());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Save a new note", description = "Creates and saves a new note based on the provided request data.")
    @PostMapping("/")
    public ResponseEntity<ApiResponse<NotesResponseDTO>> saveNotes(@RequestBody NotesRequestDTO notesRequestDTO) {
        NotesResponseDTO notesDetails = notesService.saveNotes(notesRequestDTO);
        ApiResponse<NotesResponseDTO> response = new ApiResponse<>(
                ApiResponseConstants.IS_ERROR,
                ApiResponseConstants.SUCCESS_MESSAGE,
                notesDetails,
                StatusCode.SUCCESS.getStatus().value());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Update an existing note", description = "Updates the details of an existing note identified by note ID using the provided request payload.")
    @PutMapping("/{noteId}")
    public ResponseEntity<ApiResponse<NotesResponseDTO>> updatesNotes(@PathVariable Integer noteId,
            @Valid @RequestBody NotesRequestDTO request) {
        NotesResponseDTO notesDetails = notesService.updateNotes(noteId, request);
        ApiResponse<NotesResponseDTO> response = new ApiResponse<>(
                ApiResponseConstants.IS_ERROR,
                ApiResponseConstants.SUCCESS_MESSAGE,
                notesDetails,
                StatusCode.SUCCESS.getStatus().value());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Delete a note by ID", description = "Deletes the note corresponding to the given note ID. This is a soft delete operation. Any media linked to the note will also be soft deleted.")
    @DeleteMapping("/{noteId}")
    public ResponseEntity<ApiResponse<Null>> deleteNotes(@PathVariable Integer noteId) {
        notesService.deleteNotes(noteId);
        ApiResponse<Null> response = new ApiResponse<>(
                ApiResponseConstants.IS_ERROR,
                ApiResponseConstants.SUCCESS_MESSAGE,
                null,
                StatusCode.SUCCESS.getStatus().value());
        return ResponseEntity.ok(response);
    }

}

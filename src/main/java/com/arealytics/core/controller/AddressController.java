package com.arealytics.core.controller;

import com.arealytics.core.constants.ApiResponseConstants;
import com.arealytics.core.domain.empiricalProd.City;
import com.arealytics.core.dto.request.CityRequestDTO;
import com.arealytics.core.dto.response.ApiResponse;
import com.arealytics.core.enumeration.StatusCode;
import com.arealytics.core.service.CityService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/address")
public class AddressController {

    @Autowired
    private CityService cityService;


    @Operation(
            summary = "Creates or Retrieve City",
            description = "Creates a city with the specified name and state ID. If a city with the given name and state ID already exists, it is returned. Otherwise, a new city is created with default values and returned."
    )
    @PostMapping("/city")
    public ResponseEntity<ApiResponse<City>> createCity(@RequestBody CityRequestDTO cityRequest) {
        City city = cityService.createCity(cityRequest);
        ApiResponse<City> response = new ApiResponse<>(
                ApiResponseConstants.IS_ERROR,
                ApiResponseConstants.SUCCESS_MESSAGE,
                city,
                StatusCode.SUCCESS.getStatus().value()
        );
        return ResponseEntity.ok(response);
    }
}

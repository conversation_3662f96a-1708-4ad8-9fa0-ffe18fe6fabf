package com.arealytics.core.controller;

import com.arealytics.core.constants.ApiResponseConstants;
import com.arealytics.core.dto.request.MarketSearchCriteriaDTO;
import com.arealytics.core.dto.response.ApiResponse;
import com.arealytics.core.dto.response.MarketInfoDTO;
import com.arealytics.core.enumeration.StatusCode;
import com.arealytics.core.service.LookupService;
import com.arealytics.core.service.MarketService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/lookup")
@RequiredArgsConstructor
@Tag(name = "Lookup", description = "APIs related to Lookup data")
public class LookupController {

    private final LookupService lookupService;
    private final MarketService marketService;

    @GetMapping("/property")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAllPropertyLookups() {
        Map<String, Object> lookups = lookupService.getAllPropertyLookups();
        ApiResponse<Map<String, Object>> response = new ApiResponse<>(
                false, "Success", lookups, HttpStatus.OK.value());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Retrieve All Listing Lookups", description = "Returns a list of all available listing lookups.")
    @GetMapping("/listings")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAllListingLookups() {
        Map<String, Object> listingLookups = lookupService.getAllListingLookups();
        ApiResponse<Map<String, Object>> response = new ApiResponse<>(
                false, "Success", listingLookups, HttpStatus.OK.value());
        return ResponseEntity.ok(response);
    }
    
    @Operation(summary = "Retrieve All Tenant Lookups", description = "Returns a list of all available tenant lookups.")
    @GetMapping("/tenants")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAllTenantLookups() {
        Map<String, Object> tenantLookups = lookupService.getAlltenantLookups();
        ApiResponse<Map<String, Object>> response = new ApiResponse<>(
                false, "Success", tenantLookups, HttpStatus.OK.value());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Retrieve All Sales Lookups", description = "Returns a list of all available sales lookups.")
    @GetMapping("/sales")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAllSaleLookups() {
    Map<String, Object> lookups = lookupService.getAllSaleLookups();
        ApiResponse<Map<String, Object>> response = new ApiResponse<>(false, "Success", lookups, HttpStatus.OK.value());
        return ResponseEntity.ok(response);
    }
    
    @Operation(summary = "Retrieve All Lease Lookups", description = "Returns a list of all available lease lookups")
    @GetMapping("/lease")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAllLeaseLookups() {
        Map<String, Object> lookups = lookupService.getAllLeaseLookups();
        ApiResponse<Map<String, Object>> response = new ApiResponse<>(false, "Success", lookups, HttpStatus.OK.value());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Filter Markets", description = "Returns a list of markets filtered by optional inputs: StateId, MetroId, and UseTypeId. Returns MarketID, MarketName, UseTypeName, and UseTypeID.")
    @PostMapping("/markets/filter")
    public ResponseEntity<ApiResponse<List<MarketInfoDTO>>> filterMarkets(
            @RequestBody MarketSearchCriteriaDTO requestDTO) {

        List<MarketInfoDTO> result = marketService.getMarkets(requestDTO);

        return ResponseEntity.ok(
                new ApiResponse<>(
                        false, 
                        ApiResponseConstants.SUCCESS_MESSAGE,
                        result,
                        StatusCode.SUCCESS.getStatus().value()));
    }

}

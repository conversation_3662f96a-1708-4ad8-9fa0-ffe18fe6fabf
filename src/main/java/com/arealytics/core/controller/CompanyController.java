package com.arealytics.core.controller;

import com.arealytics.core.constants.ApiResponseConstants;
import com.arealytics.core.dto.request.AgentSearchRequestDTO;
import com.arealytics.core.dto.response.AgentSearchResponseDTO;
import com.arealytics.core.dto.response.ApiResponse;
import com.arealytics.core.dto.response.CompanySearchResponseDTO;
import com.arealytics.core.enumeration.StatusCode;
import com.arealytics.core.service.CompanyRelationshipService;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import io.swagger.v3.oas.annotations.Operation;

@RestController
@RequestMapping("/company")
@RequiredArgsConstructor
@Tag(name = "Company", description = "Company related operations")
public class CompanyController {
  final private CompanyRelationshipService companyRelationshipService;

  @Operation(summary = "Get Companies", description = "Fetches a list of companies based on optional filters like companyId, branchId, and searchText.")
  @GetMapping("/get-companies")
  public ResponseEntity<ApiResponse<List<CompanySearchResponseDTO>>> getCompanies(
      @RequestParam(name = "companyId", required = false) Integer companyId,
      @RequestParam(name = "branchId", required = false) Integer branchId,
      @RequestParam(name = "searchText", required = false) String text) {

    List<CompanySearchResponseDTO> companies = companyRelationshipService.getCompanies(companyId, branchId, text);
    ApiResponse<List<CompanySearchResponseDTO>> response = new ApiResponse<>(
        ApiResponseConstants.IS_ERROR,
        ApiResponseConstants.SUCCESS_MESSAGE,
        companies,
        StatusCode.SUCCESS.getStatus().value());

    return ResponseEntity.ok(response);
  }

  @Operation(summary = "Get Agents", description = "Fetches a list of agents associated with a company. Optionally filter by whether the agent is a listing contact.")
  @PostMapping("/get-agents")
  public ResponseEntity<ApiResponse<List<AgentSearchResponseDTO>>> getAgents(@RequestBody AgentSearchRequestDTO agentSearchRequestDTO) {
    List<AgentSearchResponseDTO> agents = companyRelationshipService.getAgents(agentSearchRequestDTO);
    ApiResponse<List<AgentSearchResponseDTO>> response = new ApiResponse<>(
        ApiResponseConstants.IS_ERROR,
        ApiResponseConstants.SUCCESS_MESSAGE,
        agents,
        StatusCode.SUCCESS.getStatus().value());

    return ResponseEntity.ok(response);
  }
}

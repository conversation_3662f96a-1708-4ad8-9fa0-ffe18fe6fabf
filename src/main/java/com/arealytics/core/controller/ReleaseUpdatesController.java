package com.arealytics.core.controller;

import com.arealytics.core.constants.ApiResponseConstants;
import com.arealytics.core.dto.response.ApiResponse;
import com.arealytics.core.dto.response.ReleaseUpdatesResponseDTO;
import com.arealytics.core.service.ReleaseUpdatesService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/release-updates")
@Tag(name = "Release Updates", description = "Operations related to release updates")
public class ReleaseUpdatesController {
  private final ReleaseUpdatesService releaseUpdatesService;

  @Operation(summary = "Get Release Updates", description = "Fetches the latest release updates for a specific application.")
  @GetMapping("")
  public ResponseEntity<ApiResponse<ReleaseUpdatesResponseDTO>> getReleaseUpdates(
      @RequestParam(name = "applicationId") Integer applicationId) {
    ReleaseUpdatesResponseDTO updates = releaseUpdatesService.getReleaseUpdates(applicationId);
    ApiResponse<ReleaseUpdatesResponseDTO> response = new ApiResponse<>(ApiResponseConstants.IS_ERROR,
        ApiResponseConstants.SUCCESS_MESSAGE, updates, HttpStatus.OK.value());
    return ResponseEntity.ok(response);
  }
}

package com.arealytics.core.controller;

import com.arealytics.core.constants.ApiResponseConstants;
import com.arealytics.core.dto.request.UserPreferenceRequestDTO;
import com.arealytics.core.dto.response.ApiResponse;

import com.arealytics.core.dto.response.UserPreferenceResponseDTO;
import com.arealytics.core.dto.response.UsersDTO;
import com.arealytics.core.service.UsersService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import com.arealytics.core.enumeration.UserPreferenceScreen;
import com.arealytics.core.enumeration.UserPreferenceType;

@RestController
@RequiredArgsConstructor
@RequestMapping("/users")
@Tag(name = "Arealytics Users", description = "Operations related to Arealytics users")
public class UsersController {

    private final UsersService usersService;

    @Operation(summary = "Get Arealytics Users", description = "Fetches the list of users associated with Arealytics")
    @GetMapping("/arealytics")
    public ResponseEntity<ApiResponse<List<UsersDTO>>> getArealyticsUsers() {
        final Integer AREALYTICS_COMPANY_ID = 2;
        List<UsersDTO> users = usersService.getUsersByCompanyID(AREALYTICS_COMPANY_ID);
        ApiResponse<List<UsersDTO>> response = new ApiResponse<>(
                ApiResponseConstants.IS_ERROR,
                ApiResponseConstants.SUCCESS_MESSAGE,
                users,
                HttpStatus.OK.value()
        );
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get User Preferences by Type and Screen", description = "Fetches user preferences based on type and screen")
    @GetMapping("/preferences/{type}/{screen}")
    public ResponseEntity<ApiResponse<UserPreferenceResponseDTO>> getUserPreferecesByTypeAndScreen(
        @PathVariable UserPreferenceType type, @PathVariable UserPreferenceScreen screen) {
      UserPreferenceResponseDTO preferences = usersService.getUserPreferecesByTypeAndScreen(type, screen);
      ApiResponse<UserPreferenceResponseDTO> response = new ApiResponse<>(
          ApiResponseConstants.IS_ERROR,
          ApiResponseConstants.SUCCESS_MESSAGE,
          preferences,
          HttpStatus.OK.value());
      return ResponseEntity.ok(response);
    }

    @Operation(summary = "Create User Preferences", description = "Creates user preferences based on the provided request")
    @PostMapping("/preferences")
    public ResponseEntity<ApiResponse<UserPreferenceResponseDTO>> createUserPreferences(
        @RequestBody UserPreferenceRequestDTO request) {
      UserPreferenceResponseDTO savedPreferences = usersService.createUserPreference(request);
      ApiResponse<UserPreferenceResponseDTO> response = new ApiResponse<>(
          ApiResponseConstants.IS_ERROR,
          ApiResponseConstants.SUCCESS_MESSAGE,
          savedPreferences,
          HttpStatus.OK.value());
      return ResponseEntity.ok(response);
    }

    @Operation(summary = "Update User Preferences", description = "Updates user preferences based on the provided request and user preference ID")
    @PutMapping("/preferences/{userPreferenceId}")
    public ResponseEntity<ApiResponse<UserPreferenceResponseDTO>> updateUserPreferences(
        @RequestBody UserPreferenceRequestDTO request, @PathVariable Integer userPreferenceId) {
      UserPreferenceResponseDTO savedPreferences = usersService.updateUserPreference(request, userPreferenceId);
      ApiResponse<UserPreferenceResponseDTO> response = new ApiResponse<>(
          ApiResponseConstants.IS_ERROR,
          ApiResponseConstants.SUCCESS_MESSAGE,
          savedPreferences,
          HttpStatus.OK.value());
      return ResponseEntity.ok(response);
    }
}

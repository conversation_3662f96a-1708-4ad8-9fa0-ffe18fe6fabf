package com.arealytics.core.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.arealytics.core.constants.ApiResponseConstants;
import com.arealytics.core.dto.response.AcknowledgeStatusResponseDTO;
import com.arealytics.core.dto.response.ApiResponse;
import com.arealytics.core.dto.response.GeneralSettingsDTO;
import com.arealytics.core.enumeration.StatusCode;
import com.arealytics.core.service.AcknowledgeService;
import com.arealytics.core.service.GeneralSettingsService;

import lombok.RequiredArgsConstructor;

@RestController
@Tag(name = "Settings", description = "Operations related to Settings")
@RequiredArgsConstructor
public class SettingsController {

        private final GeneralSettingsService generalSettingsService;
        private final AcknowledgeService acknowledgeService;

        @Operation(summary = "Get Lease Status Settings", description = "Retrieves lease status settings including SettingsID, SettingsGroup, Key, and FilterJSON which contains filter details like MetroID, StateID, and default items.")
        @GetMapping(value = "/generalsettings/{settingsKey}/{settingsGroup}", produces = "application/json")
        public ResponseEntity<ApiResponse<GeneralSettingsDTO>> getActiveSettings(@PathVariable String settingsKey,
                        @PathVariable String settingsGroup) {
                GeneralSettingsDTO settings = generalSettingsService.getActiveSettingsByKeyAndGroup(settingsKey,
                                settingsGroup);
                ApiResponse<GeneralSettingsDTO> response = new ApiResponse<>(
                                ApiResponseConstants.IS_ERROR,
                                ApiResponseConstants.SUCCESS_MESSAGE,
                                settings,
                                StatusCode.SUCCESS.getStatus().value());
                return ResponseEntity.ok(response);
        }

        @Operation(summary = "Get hide acknowledgment status", description = "Provides information on whether the acknowledgment should be shown or hidden.")
        @GetMapping("/acknowledgement-status/{entityId}")
        public ResponseEntity<ApiResponse<AcknowledgeStatusResponseDTO>> getAcknowledgeStatus(
                        @PathVariable Integer entityId) {

                AcknowledgeStatusResponseDTO acknowledgeResponse = acknowledgeService
                                .getAcknowledgementStatus(entityId);

                ApiResponse<AcknowledgeStatusResponseDTO> response = new ApiResponse<>(
                                false,
                                ApiResponseConstants.SUCCESS_MESSAGE,
                                acknowledgeResponse,
                                StatusCode.SUCCESS.getStatus().value());

                return ResponseEntity.ok(response);
        }
}

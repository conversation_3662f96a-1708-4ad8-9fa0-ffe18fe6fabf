package com.arealytics.core.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.arealytics.core.constants.ApiResponseConstants;
import com.arealytics.core.dto.response.ApiResponse;
import com.arealytics.core.service.ChangeLogService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("changelogs")
@Tag(name = "ChangeLogs", description = "Fetch Changelogs")
public class ChangelogController {

    @Autowired
    private ChangeLogService changeLogService;

     @Operation(
            summary = "Get Change Logs",
            description = "Fetches change log history for the specified entityId and change log type."
    )
    @GetMapping("/{changeLogType}/{entityId}")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>>getChangeLog(
            @PathVariable String changeLogType, @PathVariable Integer entityId) {

        List<Map<String, Object>> changelog  = changeLogService.getChangeLog(changeLogType, entityId);

        ApiResponse<List<Map<String, Object>>> response = new ApiResponse<>(
                ApiResponseConstants.IS_ERROR,
                ApiResponseConstants.SUCCESS_MESSAGE,
                changelog,
                HttpStatus.OK.value()
                );
        return ResponseEntity.ok(response);
    }
}

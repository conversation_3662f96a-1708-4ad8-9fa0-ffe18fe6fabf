package com.arealytics.core.controller;

import java.util.List;

import com.arealytics.core.constants.ApiResponseConstants;
import com.arealytics.core.dto.request.ImageUploadRequestDTO;
import com.arealytics.core.dto.response.ImageUploadDTO;
import com.arealytics.core.service.S3Service;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.arealytics.core.dto.request.MediaDeleteRequestDTO;
import com.arealytics.core.dto.request.MediaRequestDTO;
import com.arealytics.core.dto.response.ApiResponse;
import com.arealytics.core.dto.response.MediaDTO;
import com.arealytics.core.enumeration.MediaRelationType;
import com.arealytics.core.service.MediaService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Null;

@RestController
@RequestMapping("/media")
@Tag(name = "Media", description = "Operations related to media")
public class MediaController {
  private final MediaService mediaService;
  private final S3Service s3Service;

  @Autowired
  public MediaController(MediaService mediaService, S3Service s3Service) {
    this.mediaService = mediaService;
    this.s3Service = s3Service;
  }

  @Operation(summary = "Fetch Media by Relation ID", description = "This API retrieves media records based on the given `mediaRelationId` and"
      + " `relationId`. It supports fetching media for various types of relations"
      + " such as entities, properties, etc. The response includes additional"
      + " information about the media, such as edit permissions and building size"
      + " for property-related media.")
  @GetMapping("/{mediaRelationTypeId}/{relationId}")
  public ResponseEntity<ApiResponse<List<MediaDTO>>> getMediaByRelationId(
      @Parameter(description = "Media Relation Type", example = "PROPERTY", schema = @Schema(implementation = MediaRelationType.class)) @PathVariable MediaRelationType mediaRelationTypeId,
      @PathVariable Integer relationId) {
    List<MediaDTO> mediaDTO = mediaService.getMediaByRelationId(mediaRelationTypeId, relationId);
    ApiResponse<List<MediaDTO>> response = new ApiResponse<>(ApiResponseConstants.IS_ERROR,
        ApiResponseConstants.SUCCESS_MESSAGE, mediaDTO, HttpStatus.OK.value());
    return ResponseEntity.ok(response);
  }

  @Operation(summary = "Fetch Media by Property ID", description = "Retrieves media records linked to the specified property's child properties. "
      + "Media associated with the master (parent) property is not included in the response.")
  @GetMapping("/linked-properties-media/{propertyId}")
  public ResponseEntity<ApiResponse<List<MediaDTO>>> getLinkedPropertyMedia(@PathVariable Integer propertyId) {
    List<MediaDTO> mediaDTO = mediaService.getLinkedPropertyMedia(propertyId);
    ApiResponse<List<MediaDTO>> response = new ApiResponse<>(ApiResponseConstants.IS_ERROR,
        ApiResponseConstants.SUCCESS_MESSAGE, mediaDTO, HttpStatus.OK.value());
    return ResponseEntity.ok(response);
  }

  @Operation(summary = "Create New Media Record", description = "This API allows the creation of a new media record based on the provided `MediaRequestDTO`. "
      + "It supports associating media with entities like properties or other relation types. "
      + "If marked as default, it automatically updates related defaults and may cascade updates to parent entities "
      + "such as a master property in a STRATA relationship.")
  @PostMapping("/")
  public ResponseEntity<ApiResponse<MediaDTO>> createMedia(
      @Valid @RequestBody MediaRequestDTO mediaRequestDTO) {
    MediaDTO mediaDTO = mediaService.createMedia(mediaRequestDTO);
    ApiResponse<MediaDTO> response = new ApiResponse<>(ApiResponseConstants.IS_ERROR,
        ApiResponseConstants.SUCCESS_MESSAGE, mediaDTO, HttpStatus.OK.value());
    return ResponseEntity.ok(response);
  }

  @Operation(summary = "Update Existing Media Record", description = "This API updates an existing media record identified by `mediaId` using the provided `MediaRequestDTO`. "
      + "It ensures related property data is updated if the media is marked as default. "
      + "For STRATA properties, it conditionally updates the corresponding master property's media record as well.")
  @PutMapping("/{mediaId}")
  public ResponseEntity<ApiResponse<MediaDTO>> updateMedia(@PathVariable Integer mediaId,
      @Valid @RequestBody MediaRequestDTO mediaRequestDTO) {
    MediaDTO mediaDTO = mediaService.updateMedia(mediaRequestDTO, mediaId);
    ApiResponse<MediaDTO> response = new ApiResponse<>(ApiResponseConstants.IS_ERROR,
        ApiResponseConstants.SUCCESS_MESSAGE, mediaDTO, HttpStatus.OK.value());
    return ResponseEntity.ok(response);
  }

  @Operation(summary = "Delete Existing Media Record", description = "This API deletes an existing media record identified by `mediaRelationshipId`. ")
  @DeleteMapping("/delete")
  public ResponseEntity<ApiResponse<Null>> deleteMedia(
      @Valid @RequestBody MediaDeleteRequestDTO mediaDeleteRequestDTO) {
    mediaService.deleteMedia(mediaDeleteRequestDTO);
    ApiResponse<Null> response = new ApiResponse<>(ApiResponseConstants.IS_ERROR,
        ApiResponseConstants.SUCCESS_MESSAGE, null, HttpStatus.OK.value());
    return ResponseEntity.ok(response);
  }

  @Operation(summary = "Upload Image For Media Record", description = "This API upload an image file to s3 bucket ")
  @PostMapping("/image-upload")
  public ResponseEntity<ApiResponse<ImageUploadDTO>> uploadImage(@Valid @RequestBody ImageUploadRequestDTO imageUploadRequestDTO) {
    ImageUploadDTO imageUploadDto = s3Service.upload(imageUploadRequestDTO);
    ApiResponse<ImageUploadDTO> response = new ApiResponse<>(ApiResponseConstants.IS_ERROR,
            ApiResponseConstants.SUCCESS_MESSAGE, imageUploadDto, HttpStatus.OK.value());
    return ResponseEntity.ok(response);
  }
}

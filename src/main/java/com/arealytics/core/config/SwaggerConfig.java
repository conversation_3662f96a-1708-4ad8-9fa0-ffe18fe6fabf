package com.arealytics.core.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.hibernate.cfg.Environment;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.arealytics.core.enumeration.AuditEntity;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.servers.Server;

import java.util.Arrays;
import java.util.List;

@Configuration
public class SwaggerConfig {
  @Value("${swagger.server.url}")
  private String serverUrl;

  @Value("${swagger.server.description}")
  private String serverDescription;

  @Bean
  public OpenAPI customOpenAPI() {
    Server activeServer = new Server().url(serverUrl).description(serverDescription);
    return new OpenAPI()
        .components(
            new Components()
                .addSecuritySchemes(
                    "bearer-jwt",
                    new SecurityScheme()
                        .type(SecurityScheme.Type.HTTP)
                        .scheme("bearer")
                        .bearerFormat("JWT")
                        .description(
                            "Enter the JWT token obtained after logging"
                                + " in. The token can be found in"
                                + " localStorage after"
                                + " authentication.")))
        .info(
            new Info()
                .title("Phoenix API Documentation")
                .version("1.0")
                .description("API documentation for the Phoenix Application"))
        .servers(List.of(activeServer))
        .servers(List.of(activeServer))
        .addSecurityItem(new SecurityRequirement().addList("bearer-jwt"));
  }

  @Bean
  public OpenApiCustomizer addAuditEntityEnumSchema() {
    return openApi -> {
      // Dynamically get all enum values
      List<String> enumDisplayNames = Arrays.stream(AuditEntity.values())
          .map(AuditEntity::getName)
          .toList(); // Collect display names

      // Create the schema
      Schema<String> auditEntityTypeEnumSchema = new Schema<>();
      auditEntityTypeEnumSchema.setType("string");
      auditEntityTypeEnumSchema.setDescription("Audit Entity Types Enum");
      auditEntityTypeEnumSchema.setEnum(enumDisplayNames);

      // Add schema to components
      openApi.getComponents().addSchemas("AuditLogEntityTypes", auditEntityTypeEnumSchema);
    };
  }

}

package com.arealytics.core.config;

import com.arealytics.core.domain.empiricalProd.EntityModel;
import com.arealytics.core.dto.request.UserInfoDTO;
import com.arealytics.core.security.SecurityUtils;
import com.arealytics.core.service.EntityService;
import com.arealytics.core.utils.UserContext;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Component
public class UserContextFilter extends OncePerRequestFilter {

  private final SecurityUtils securityUtils;
  private final EntityService entityService;

  public UserContextFilter(SecurityUtils securityUtils, EntityService entityService) {
    this.securityUtils = securityUtils;
    this.entityService = entityService;
  }

  @Override
  protected void doFilterInternal(HttpServletRequest request,
      HttpServletResponse response,
      FilterChain filterChain) throws ServletException, IOException {
    try {
      UserInfoDTO userInfoDTO = securityUtils.getUserInfo();
      if (userInfoDTO != null && userInfoDTO.getLoginEntityId() != null) {
        Integer entityId = Integer.parseInt(userInfoDTO.getLoginEntityId().toString());
        EntityModel currentEntity = entityService.getEntityById(entityId);
        UserContext.setLoginEntity(currentEntity);
      }
      filterChain.doFilter(request, response);
    } finally {
      UserContext.clear();
    }
  }
}

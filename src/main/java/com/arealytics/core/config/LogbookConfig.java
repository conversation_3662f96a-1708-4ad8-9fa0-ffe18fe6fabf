package com.arealytics.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.zalando.logbook.*;
//import org.zalando.logbook.core.DefaultHttpLogWriter;
import org.zalando.logbook.core.DefaultSink;
import org.zalando.logbook.json.JsonHttpLogFormatter;

@Configuration
public class LogbookConfig {
    @Value("${logbook.custom.response-logging-enabled:false}")
    private boolean isResponseLoggingEnabled;

    @Bean
    public Logbook logbook() {
        Sink sink = new DefaultSink(new JsonHttpLogFormatter(), new CustomHttpLogWriter(isResponseLoggingEnabled));

        return Logbook.builder().sink(sink).build();
    }
}

package com.arealytics.core.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.zalando.logbook.Correlation;
import org.zalando.logbook.HttpLogWriter;
import org.zalando.logbook.Logbook;
import org.zalando.logbook.Precorrelation;

public class CustomHttpLogWriter implements HttpLogWriter {

    private static final Logger LOGGER = LoggerFactory.getLogger(Logbook.class);
    private final boolean isResponseLoggingEnabled;

    public CustomHttpLogWriter(boolean isResponseLoggingEnabled) {
        System.out.println(isResponseLoggingEnabled);

        this.isResponseLoggingEnabled = isResponseLoggingEnabled;
    }

    @Override
    public boolean isActive() {
        return true;
    }

    @Override
    public void write(Precorrelation precorrelation, String request) {
        LOGGER.trace(request);
    }

    @Override
    public void write(Correlation correlation, String response) {
        if (isResponseLoggingEnabled) {
            LOGGER.trace(response);
        }
    }
}

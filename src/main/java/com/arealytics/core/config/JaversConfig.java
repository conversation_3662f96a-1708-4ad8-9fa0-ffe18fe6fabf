package com.arealytics.core.config;

import org.javers.core.Javers;
import org.javers.core.JaversBuilder;
import org.locationtech.jts.geom.Geometry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.arealytics.core.utils.GeometryComparator;

@Configuration
public class JaversConfig {

    @Bean
    public Javers javers() {
        return JaversBuilder.javers()
                .registerValue(Geometry.class, new GeometryComparator())
                .build();
    }
}

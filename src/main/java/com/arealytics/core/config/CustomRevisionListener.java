package com.arealytics.core.config;

import org.hibernate.envers.RevisionListener;

import com.arealytics.core.constants.AppConstants;
import com.arealytics.core.domain.empiricalProd.EntityModel;
import com.arealytics.core.domain.shared.CustomRevisionEntity;
import com.arealytics.core.utils.UserContext;

public class CustomRevisionListener implements RevisionListener {
    @Override
    public void newRevision(Object revisionEntity) {
        CustomRevisionEntity revision = (CustomRevisionEntity) revisionEntity;
        EntityModel entity = UserContext.getLoginEntity();
        if (entity != null) {
            revision.setChangedBy(entity.getEntityId());
        }
        revision.setApplicationId(AppConstants.DEFAULT_APPLICATION_ID);
    }
}

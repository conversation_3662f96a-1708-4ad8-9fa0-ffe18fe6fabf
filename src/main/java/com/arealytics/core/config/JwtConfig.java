package com.arealytics.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.jose.jws.MacAlgorithm;
import org.springframework.security.oauth2.jwt.*;

import javax.crypto.spec.SecretKeySpec;

@Configuration
public class JwtConfig {

  @Value("${JWT_SECRET_KEY}")
  private String secret_key;

  @Bean
  public JwtDecoder jwtDecoder() {
    return NimbusJwtDecoder
        .withSecretKey(new SecretKeySpec(secret_key.getBytes(), "HmacSHA256"))
        .macAlgorithm(MacAlgorithm.HS256)
        .build();
  }
}

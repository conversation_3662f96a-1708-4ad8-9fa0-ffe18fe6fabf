package com.arealytics.core.config;

import java.time.Duration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext.SerializationPair;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;


@Configuration
public class RedisConfig {

        @Value("${redis.host}")
        private String redisHost;
    
        @Value("${redis.port}")
        private int redisPort;
    
        @Value("${redis.password:}")
        private String redisPassword; // Default to empty string if password is not set

        private static final int TIME_TO_LIVE = 1; // Time to live in days

    @Bean
    public LettuceConnectionFactory lettuceConnectionFactory() {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(redisHost);
        config.setPort(redisPort);
        config.setPassword(redisPassword);
        LettuceConnectionFactory factory = new LettuceConnectionFactory(config);
        factory.afterPropertiesSet();
        return factory;
    }

    @Bean
    public RedisCacheConfiguration cacheConfiguration(ObjectMapper mapper) {
        // Customize the ObjectMapper to ignore unknown properties during deserialization
        ObjectMapper myMapper = mapper.copy()
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        GenericJackson2JsonRedisSerializer redisSerializer = new GenericJackson2JsonRedisSerializer(myMapper);

        return RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofDays(TIME_TO_LIVE))// Set time-to-live (TTL) for cache entries to 1 day
                .serializeValuesWith(SerializationPair.fromSerializer(redisSerializer));
    }
}

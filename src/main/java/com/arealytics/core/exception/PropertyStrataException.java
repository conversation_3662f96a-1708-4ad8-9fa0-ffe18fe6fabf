package com.arealytics.core.exception;

/**
 * Custom exception for Property Strata operations.
 * Provides specific error handling for strata relationship operations.
 */

public class PropertyStrataException extends RuntimeException{
    /**
     * Creates a new PropertyStrataException with the specified message.
     *
     * @param message The error message
     */
    public PropertyStrataException(String message) {
        super(message);
    }

    /**
     * Creates a new PropertyStrataException with the specified message and cause.
     *
     * @param message The error message
     * @param cause The cause of the exception
     */
    public PropertyStrataException(String message, Throwable cause) {
        super(message, cause);
    }
}

package com.arealytics.core.exception;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import com.arealytics.core.dto.response.ErrorResponseDTO;
import com.arealytics.core.enumeration.StatusCode;

import jakarta.servlet.http.HttpServletRequest;

@RestControllerAdvice
public class GlobalExceptionHandler {
  private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

  private ResponseEntity<ErrorResponseDTO> buildErrorResponse(
      ApiResponseHandler exception, HttpServletRequest request) {

    ErrorResponseDTO errorResponse = new ErrorResponseDTO(
        LocalDateTime.now(),
        exception.getStatusCode(),
        HttpStatus.valueOf(exception.getStatusCode()).getReasonPhrase(),
        exception.getMessage(),
        request.getRequestURI());

    return ResponseEntity.status(exception.getStatusCode()).body(errorResponse);
  }

  @ExceptionHandler(ApiResponseHandler.class)
  public ResponseEntity<ErrorResponseDTO> handleApiException(
      ApiResponseHandler exception, HttpServletRequest request) {
    return buildErrorResponse(exception, request);
  }

  @ExceptionHandler(IllegalArgumentException.class)
  public ResponseEntity<ErrorResponseDTO> handleBadRequest(
      IllegalArgumentException exception, HttpServletRequest request) {
    return buildErrorResponse(
        new ApiResponseHandler(
            StatusCode.BAD_REQUEST.getStatus().value(), exception.getMessage()),
        request);
  }

  @ExceptionHandler(UnauthorizedAccessException.class)
  public ResponseEntity<ErrorResponseDTO> handleUnauthorized(
      UnauthorizedAccessException exception, HttpServletRequest request) {
    return buildErrorResponse(exception, request);
  }

  @ExceptionHandler(ResourceNotFoundException.class)
  public ResponseEntity<ErrorResponseDTO> handleNotFound(
      ResourceNotFoundException exception, HttpServletRequest request) {
    return buildErrorResponse(exception, request);
  }

  @ExceptionHandler(Exception.class)
  public ResponseEntity<ErrorResponseDTO> handleGenericException(
      Exception exception, HttpServletRequest request) {
    // Log the full exception for debugging
    logger.error("Unhandled exception occurred at {}: {}", request.getRequestURI(), exception.getMessage(), exception);

    // Include the actual exception message for debugging
    String errorMessage = exception.getMessage() != null ?
        exception.getMessage() : "An unexpected error occurred.";

    return buildErrorResponse(
        new ApiResponseHandler(
            StatusCode.INTERNAL_SERVER_ERROR.getStatus().value(),
            errorMessage),
        request);
  }

  @ExceptionHandler(MethodArgumentNotValidException.class)
  public ResponseEntity<ErrorResponseDTO> handleValidationExceptions(
      MethodArgumentNotValidException ex, HttpServletRequest request) {
    List<String> errors = new ArrayList<>();

    ex.getBindingResult()
        .getFieldErrors()
        .forEach(error -> errors.add(error.getDefaultMessage()));

    return buildErrorResponse(
        new ApiResponseHandler(
            StatusCode.BAD_REQUEST.getStatus().value(),
            errors.stream().map(String::toString).collect(Collectors.joining(", "))),
        request);
  }

  @ExceptionHandler(HttpMessageNotReadableException.class)
  public ResponseEntity<ErrorResponseDTO> handleInvalidJson(
      HttpMessageNotReadableException ex, HttpServletRequest request) {

    String msg = ex.getMessage();
    String fieldError = "Malformed JSON or unknown field in request body";

    if (msg != null && msg.contains("Unrecognized field")) {
      try {
        String fieldPart = msg.substring(msg.indexOf("Unrecognized field") + 20);
        String fieldName = fieldPart.substring(0, fieldPart.indexOf("\"", 1) + 1);
        fieldError = "Unrecognized field: " + fieldName;
      } catch (Exception parseEx) {
        // fallback to default message
      }
    }
    return buildErrorResponse(
        new ApiResponseHandler(StatusCode.BAD_REQUEST.getStatus().value(), fieldError),
        request);
  }

  @ExceptionHandler(MethodArgumentTypeMismatchException.class)
  public ResponseEntity<ErrorResponseDTO> handleMethodArgumentTypeMismatch(
      MethodArgumentTypeMismatchException ex, HttpServletRequest request) {

    String errorMessage = String.format(
        "Invalid value '%s' for parameter '%s'. Expected type: %s",
        ex.getValue(), ex.getName(), ex.getRequiredType().getSimpleName());

    return buildErrorResponse(
        new ApiResponseHandler(StatusCode.BAD_REQUEST.getStatus().value(), errorMessage),
        request);
  }

    @ExceptionHandler(PropertyStrataException.class)
  public ResponseEntity<ErrorResponseDTO> handleBadRequest(
      PropertyStrataException exception, HttpServletRequest request) {
    return buildErrorResponse(
        new ApiResponseHandler(
            StatusCode.BAD_REQUEST.getStatus().value(), exception.getMessage()),
        request);
  }

  @ExceptionHandler(EnumLabelMappingException.class)
  public ResponseEntity<ErrorResponseDTO> handleEnumLabelMappingException(EnumLabelMappingException exception, HttpServletRequest request) {
    return buildErrorResponse(
        new ApiResponseHandler(
            StatusCode.BAD_REQUEST.getStatus().value(), exception.getMessage()),
        request);
  }
  
  @ExceptionHandler(PropertyRollupException.class)
  public ResponseEntity<ErrorResponseDTO> handlePropertyRollupException(
          PropertyRollupException exception, HttpServletRequest request) {
    return buildErrorResponse(
            new ApiResponseHandler(
                    StatusCode.BAD_REQUEST.getStatus().value(), exception.getMessage()),
            request);
  }
}

package com.arealytics.core.helpers;

import com.arealytics.core.dto.response.BuildingFootPrintsDTO;
import com.arealytics.core.dto.response.ChildFreeHoldsAdditionalUsesDTO;
import com.arealytics.core.dto.response.MediaDTO;
import com.arealytics.core.dto.response.PropertyDetailsSizeResponseDTO;
import com.arealytics.core.dto.response.PropertyMasterRollupObject;
import com.arealytics.core.enumeration.MediaRelationType;
import com.arealytics.core.enumeration.MediaSubType;
import com.arealytics.core.enumeration.MediaType;
import com.arealytics.core.exception.PropertyRollupException;
import com.arealytics.core.service.BuildingFootPrintService;
import com.arealytics.core.service.MediaService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Helper class responsible for aggregating and rolling up data from multiple child freehold properties
 * into a single master property object. It performs aggregation of numeric fields, evaluation of boolean flags,
 * collection of unique string values, and determination of the main photo and default usage.
 */
@Component
public class MasterRollupHelper {
    private final MediaService mediaService;
    private final BuildingFootPrintService buildingFootPrintService;

    /**
     * Constructor for MasterRollupHelper.
     * @param mediaService MediaService used to fetch media details lazily.
     */
    public MasterRollupHelper(@Lazy MediaService mediaService, @Lazy BuildingFootPrintService buildingFootPrintService) {
        this.mediaService = mediaService;
        this.buildingFootPrintService = buildingFootPrintService;
    }

    /**
     * Main entry method to roll up child property details into a single master property rollup object.
     *
     * @param masterProperty The master property record.
     * @param children       The list of child property records to aggregate from.
     * @return Populated PropertyMasterRollupObject containing aggregated data.
     */
    public PropertyMasterRollupObject rollupChildFreeHoldDataToMasterFreeHold(PropertyDetailsSizeResponseDTO masterProperty, List<PropertyDetailsSizeResponseDTO> children, List<Integer> childPropertyIds) {
        PropertyMasterRollupObject rollupObject = new PropertyMasterRollupObject();
        sumAggregateFields(masterProperty, children, rollupObject);
        updateYesNoFields(masterProperty, children, rollupObject);
        listUniqueValues(masterProperty, children, rollupObject);
        aggregateRangeFields(children, rollupObject);
        rollupDefaultPropertyUseAndUseTypeID(rollupObject, children, "buildingSizeSF", "propertyUse");
        setMainPhotoUrl(masterProperty, rollupObject);
        getAdditionalUsesFromBuildingFootprints(masterProperty, children, rollupObject);
        return rollupObject;
    }


    public void getAdditionalUsesFromBuildingFootprints(
            PropertyDetailsSizeResponseDTO masterProperty,
            List<PropertyDetailsSizeResponseDTO> children,
            PropertyMasterRollupObject rollupObject
    ) {
        List<Integer> childPropertyIds = children.stream()
                .map(PropertyDetailsSizeResponseDTO::getPropertyID)
                .collect(Collectors.toList());

        List<BuildingFootPrintsDTO> buildingFootprints = buildingFootPrintService.fetchBuildingFootPrintsByPropertyIds(childPropertyIds);

        Map<Integer, Integer> propertyIdToIndexMap =
                IntStream.range(0, children.size())
                        .boxed()
                        .collect(Collectors.toMap(
                                i -> children.get(i).getPropertyID(),
                                i -> i
                        ));


        // Step 2: Update the unit and child Property useTypeID in BuildingFootPrintsDTO list
        for (BuildingFootPrintsDTO building : buildingFootprints) {
            Integer index = propertyIdToIndexMap.get(building.getPropertyId());
            if(index != null) {
                PropertyDetailsSizeResponseDTO child = children.get(index);
                building.setCondoUnit(child.getCondoUnit());
                building.setPropertyUseTypeID(child.getUseTypeID());
            }
        }

        List<ChildFreeHoldsAdditionalUsesDTO> additionalUses = new ArrayList<>();
        if (buildingFootprints == null || buildingFootprints.isEmpty()) {
            return;
        }

        for (BuildingFootPrintsDTO buildingFootprint : buildingFootprints) {
            Object propertyMinFloor = parseFloor(buildingFootprint.getPropertyMinFloor());
            Object propertyMaxFloor = parseFloor(buildingFootprint.getPropertyMaxFloor());

            if (!equalsNullable(buildingFootprint.getUseTypeId(), buildingFootprint.getPropertyUseTypeID())
                    && !equalsNullable(buildingFootprint.getUseTypeId(), masterProperty.getUseTypeID())) {
                addAdditionalUse(
                        additionalUses,
                        buildingFootprint.getUseTypeId(),
                        buildingFootprint.getUseTypeName(),
                        buildingFootprint.getMainSpecificUseTypeId(),
                        buildingFootprint.getMainSpecificUseTypeName(),
                        buildingFootprint,
                        propertyMinFloor,
                        propertyMaxFloor,
                        buildingFootprint.getCondoUnit()
                );
            }

            if (buildingFootprint.getAdditionalUseTypeId() != null) {
                addAdditionalUse(
                        additionalUses,
                        buildingFootprint.getAdditionalUseTypeId(),
                        buildingFootprint.getAdditionalUseTypeName(),
                        buildingFootprint.getAdditionalSpecificUseTypeId(),
                        buildingFootprint.getAdditionalSpecificUseTypeName(),
                        buildingFootprint,
                        propertyMinFloor,
                        propertyMaxFloor,
                        buildingFootprint.getCondoUnit()
                );
            }
        }
        rollupObject.setChildFreeHoldsAdditionalUses(additionalUses);
    }

    private void addAdditionalUse(List<ChildFreeHoldsAdditionalUsesDTO> additionalUses,
                                  Integer useTypeID,
                                  String useTypeName,
                                  Integer specificUsesID,
                                  String specificUsesName,
                                  BuildingFootPrintsDTO buildingFootprint,
                                  Object minFloor,
                                  Object maxFloor,
                                  String unit) {

        String sectionName = createSectionName(minFloor, maxFloor, useTypeName, unit);

        ChildFreeHoldsAdditionalUsesDTO use = new ChildFreeHoldsAdditionalUsesDTO();
        use.setSection(sectionName);
        use.setUseTypeID(useTypeID);
        use.setUseTypeName(useTypeName);
        use.setSpecificUsesID(specificUsesID);
        use.setSpecificUsesName(specificUsesName);
        use.setFloors(buildingFootprint.getFloors());
        use.setFloorSizeSF(buildingFootprint.getSizeInSF());
        use.setFloorSizeSM(buildingFootprint.getSizeInSM());
        use.setMinFloor(minFloor);
        use.setMaxFloor(maxFloor);

        additionalUses.add(use);
    }

    private boolean equalsNullable(Integer a, Integer b) {
        return a != null && a.equals(b);
    }

    /**
     * Sums or concatenates values from child properties for numeric or string fields.
     *
     * @param masterProperty The master property.
     * @param children       List of child properties.
     * @param rollupObject   Rollup object to update.
     */
    private  void sumAggregateFields(PropertyDetailsSizeResponseDTO masterProperty, List<PropertyDetailsSizeResponseDTO> children, PropertyMasterRollupObject rollupObject) {
        Map<String, Object> tempAggregatedValues = new HashMap<>();

        for (PropertyDetailsSizeResponseDTO child : children) {
            for (Map.Entry<String, String> entry : RollupKeys.AGGREGATE_SUM_KEYS.entrySet()) {
                String jsonKey = entry.getKey();
                String javaField = entry.getValue();

                try {
                    Method getter = PropertyDetailsSizeResponseDTO.class.getMethod("get" + capitalize(javaField));
                    Object value = getter.invoke(child);

                    BigDecimal numericValue = null;

                    if (value instanceof Number) {
                        numericValue = new BigDecimal(value.toString());
                    } else if (value instanceof String) {
                        String strVal = ((String) value).trim();
                        if (!strVal.isEmpty()) {
                            try {
                                numericValue = new BigDecimal(strVal);
                            } catch (NumberFormatException e) {
                                // not numeric, fallback to concat
                            }
                        }
                    }

                    if (numericValue != null) {
                        tempAggregatedValues.merge(jsonKey, numericValue, (oldVal, newVal) ->
                                ((BigDecimal) oldVal).add((BigDecimal) newVal));
                    } else if (value != null) {
                        String strVal = value.toString().trim();
                        if (!strVal.isEmpty()) {
                            tempAggregatedValues.merge(jsonKey, strVal, (oldVal, newVal) ->
                                    oldVal + " " + newVal);
                        }
                    }

                } catch (Exception e) {
                    throw new PropertyRollupException("Failed to aggregate field '" + javaField + "'", e);
                }
            }
        }

        for (Map.Entry<String, String> entry : RollupKeys.AGGREGATE_SUM_KEYS.entrySet()) {
            String jsonKey = entry.getKey();
            String javaField = entry.getValue();
            Object value = tempAggregatedValues.get(jsonKey);

            if (value == null) continue;

            try {
                String setterName = "set" + capitalize(javaField);
                Method setter = null;

                for (Method method : PropertyMasterRollupObject.class.getMethods()) {
                    if (method.getName().equals(setterName) && method.getParameterCount() == 1) {
                        setter = method;
                        break;
                    }
                }

                if (setter == null) {
                    throw new PropertyRollupException("Setter not found for field: " + javaField);
                }

                Class<?> paramType = setter.getParameterTypes()[0];

                if (value instanceof BigDecimal) {
                    BigDecimal bd = ((BigDecimal) value).setScale(2, RoundingMode.HALF_UP);

                    if (bd.stripTrailingZeros().scale() <= 0) {
                        if (paramType == Integer.class || paramType == int.class) {
                            setter.invoke(rollupObject, bd.intValue());
                        } else if (paramType == Long.class || paramType == long.class) {
                            setter.invoke(rollupObject, bd.longValue());
                        } else if (paramType == BigDecimal.class) {
                            setter.invoke(rollupObject, bd);
                        } else {
                            throw new PropertyRollupException("Unsupported numeric type for field: " + javaField);
                        }
                    } else {
                        if (paramType == BigDecimal.class) {
                            setter.invoke(rollupObject, bd);
                        } else if (paramType == Double.class || paramType == double.class) {
                            setter.invoke(rollupObject, bd.doubleValue());
                        } else {
                            throw new PropertyRollupException("Setter parameter mismatch for fractional value on field: " + javaField);
                        }
                    }
                } else {
                    setter.invoke(rollupObject, value);
                }

            } catch (PropertyRollupException e) {
                throw e; // rethrow our custom exception
            } catch (Exception e) {
                throw new PropertyRollupException("Failed to set aggregated field '" + javaField + "' with value: " + value, e);
            }
        }
    }

    /**
     * Aggregates boolean fields (Yes/No flags) across child properties.
     *
     * @param masterFreehold The master property.
     * @param childFreeholds The list of child properties.
     * @param rollupObject   Rollup object to update.
     */
    public  void updateYesNoFields(PropertyDetailsSizeResponseDTO masterFreehold, List<PropertyDetailsSizeResponseDTO> childFreeholds, PropertyMasterRollupObject rollupObject) {
        RollupKeys.YES_NO_KEYS.forEach((jsonKey, javaField) -> {
            try {
                String getterName = "get" + capitalize(javaField);
                Method childGetter = PropertyDetailsSizeResponseDTO.class.getMethod(getterName);

                // Extract non-null Boolean values from children
                List<Boolean> childValues = childFreeholds.stream()
                        .map(child -> {
                            try {
                                return (Boolean) childGetter.invoke(child);
                            } catch (Exception e) {
                                return null; // ignore broken getter calls
                            }
                        })
                        .filter(val -> val != null)
                        .collect(Collectors.toList());

                boolean hasAnyTrue = childValues.stream().anyMatch(Boolean::booleanValue);
                boolean areAllFalseOrNull = !childValues.isEmpty() && childValues.stream().allMatch(val -> !val);
                String setterName = "set" + capitalize(javaField);
                Method setter = null;

                for (Method method : PropertyMasterRollupObject.class.getMethods()) {
                    if (method.getName().equals(setterName) && method.getParameterCount() == 1 && method.getParameterTypes()[0] == Boolean.class) {
                        setter = method;
                        break;
                    }
                }

                if (setter == null) {
                    throw new PropertyRollupException("Setter not found for Boolean field: " + javaField);
                }

                if (hasAnyTrue) {
                    setter.invoke(rollupObject, true);
                } else if (areAllFalseOrNull) {
                    setter.invoke(rollupObject, false);
                } else {
                    setter.invoke(rollupObject, (Boolean) null);
                }

            } catch (PropertyRollupException e) {
                throw e;
            } catch (Exception e) {
                throw new PropertyRollupException("Failed to update Boolean field: " + javaField, e);
            }
        });
    }

    /**
     * Lists unique string values for selected fields by aggregating from child properties.
     *
     * @param masterFreehold The master property.
     * @param childFreeholds The list of child properties.
     * @param rollupObject   Rollup object to update.
     */
    public void listUniqueValues(PropertyDetailsSizeResponseDTO masterFreehold,
                                 List<PropertyDetailsSizeResponseDTO> childFreeholds,
                                 PropertyMasterRollupObject rollupObject) {

        RollupKeys.UNIQUE_KEYS.forEach((jsonKey, javaField) -> {
            Set<String> uniqueValues = new LinkedHashSet<>();

            try {
                String capitalizedField = capitalize(javaField);

                // Getter from PropertyDetailsSizeResponseDTO
                Method getter = PropertyDetailsSizeResponseDTO.class.getMethod("get" + capitalizedField);

                // Use field type to find the setter in PropertyMasterRollupObject
                Field field = PropertyMasterRollupObject.class.getDeclaredField(javaField);
                Class<?> fieldType = field.getType();
                String setterName = "set" + capitalizedField;
                Method setter = PropertyMasterRollupObject.class.getMethod(setterName, fieldType);

                // Build set of unique stringified values
                for (PropertyDetailsSizeResponseDTO child : childFreeholds) {
                    Object value = getter.invoke(child);
                    if (value == null) continue;

                    if (value instanceof String strVal) {
                        if (strVal.contains(",")) {
                            Arrays.stream(strVal.split(","))
                                    .map(String::trim)
                                    .filter(s -> !s.isEmpty())
                                    .forEach(uniqueValues::add);
                        } else {
                            uniqueValues.add(strVal.trim());
                        }
                    } else if (value instanceof Number) {
                        uniqueValues.add(value.toString());
                    } else if (value.getClass().isEnum()) {
                        uniqueValues.add(((Enum<?>) value).name());
                    } else {
                        uniqueValues.add(value.toString().trim());
                    }
                }

                // Set the final value in rollupObject using setter
                if (!uniqueValues.isEmpty()) {
                    String joined = String.join(", ", uniqueValues);
                    setter.invoke(rollupObject, joined);
                } else {
                    setter.invoke(rollupObject, (String) null);
                }

            } catch (PropertyRollupException e) {
                throw e;
            } catch (Exception e) {
                e.printStackTrace();
                throw new PropertyRollupException("Failed processing unique field: " + javaField, e);
            }
        });
    }

    /**
     * Aggregates range fields like min/max values across children.
     *
     * @param childFreeholds The list of child properties.
     * @param rollupObject   Rollup object to update.
     */
    public  void aggregateRangeFields(
            List<PropertyDetailsSizeResponseDTO> childFreeholds,
            PropertyMasterRollupObject rollupObject
    ) {
        for (Map.Entry<String, String> entry : RollupKeys.RANGE_KEYS.entrySet()) {
            String jsonKey = entry.getKey();      // e.g., "ClearHeightMin"
            String javaField = entry.getValue();  // e.g., "clearHeightMin"

            try {
                List<Object> values = new ArrayList<>();
                Method getter = PropertyDetailsSizeResponseDTO.class.getMethod("get" + capitalize(javaField));

                for (PropertyDetailsSizeResponseDTO child : childFreeholds) {
                    Object value = getter.invoke(child);
                    if (value != null && (value instanceof Number || value instanceof Date || value instanceof String)) {
                        if (value instanceof String str) {
                            try {
                                if (str.matches("\\d{4}-\\d{2}-\\d{2}")) {
                                    values.add(java.sql.Date.valueOf(str));
                                } else {
                                    values.add(Double.parseDouble(str));
                                }
                            } catch (Exception ignored) {
                            }
                        } else {
                            values.add(value);
                        }
                    }
                }

                if (values.isEmpty()) {
                    setRollupValue(rollupObject, javaField, null);
                    continue;
                }

                boolean isDateField = values.get(0) instanceof Date;
                String result;

                if (isDateField) {
                    List<Long> timestamps = values.stream()
                            .map(v -> ((Date) v).getTime())
                            .collect(Collectors.toList());

                    Date minDate = new Date(Collections.min(timestamps));
                    Date maxDate = new Date(Collections.max(timestamps));

                    result = formatRange(jsonKey, minDate, maxDate);
                } else {
                    List<Double> doubles = values.stream()
                            .map(v -> ((Number) v).doubleValue())
                            .collect(Collectors.toList());

                    double min = Math.round(Collections.min(doubles) * 100.0) / 100.0;
                    double max = Math.round(Collections.max(doubles) * 100.0) / 100.0;

                    result = formatRange(jsonKey, min, max);
                }

                setRollupValue(rollupObject, javaField, result);

            } catch (Exception e) {
                throw new PropertyRollupException("Failed processing range field: " + jsonKey, e);
            }
        }
    }

    /**
     * Sets the main photo URL on rollup object based on availability in master or its children.
     *
     * @param masterFreehold The master property.
     * @param rollupObject   Rollup object to update.
     */
    public void setMainPhotoUrl(PropertyDetailsSizeResponseDTO masterFreehold, PropertyMasterRollupObject rollupObject) {
        Integer masterPropertyId = masterFreehold.getPropertyID();
        if (masterPropertyId == null) return;

        String mainPhotoUrl = masterFreehold.getMainPhotoUrl();
        List<MediaDTO> childMedias = new ArrayList<>();
        Integer defaultMediaIndex = null;

        if (mainPhotoUrl == null || mainPhotoUrl.contains("NoPhoto.jpg")) {
            Object rawResponse = mediaService.getMediaByRelationId(MediaRelationType.PROPERTY, masterPropertyId);

            List<MediaDTO> medias = new ArrayList<>();
            if (rawResponse instanceof List<?>) {
                ObjectMapper mapper = new ObjectMapper();
                mapper.registerModule(new JavaTimeModule());
                medias = ((List<?>) rawResponse).stream()
                        .map(obj -> mapper.convertValue(obj, MediaDTO.class))
                        .collect(Collectors.toList());
            }

            if (!medias.isEmpty()) {
                childMedias = medias.stream()
                        .filter(media -> !Objects.equals(media.getRelationID(), masterPropertyId))
                        .collect(Collectors.toList());

                List<Integer> result = getLargestChildFreeholdBuildingSizeIndex(childMedias, "buildingSizeSF", true);
                defaultMediaIndex = result.size() > 2 ? result.get(2) : null;

                if (defaultMediaIndex != null && defaultMediaIndex >= 0 && defaultMediaIndex < childMedias.size()) {
                    rollupObject.setMainPhotoUrl(childMedias.get(defaultMediaIndex).getPath());
                    return;
                }
            }

            rollupObject.setMainPhotoUrl(masterFreehold.getMainPhotoUrl());
        } else {
            rollupObject.setMainPhotoUrl(masterFreehold.getMainPhotoUrl());
        }
    }



    /**
     * Identifies the largest building by size and determines the default media index.
     *
     * @param childs            List of child media DTOs.
     * @param key               Field key for size (e.g., "buildingSizeSF").
     * @param isDefaultRequired Whether to determine a default image.
     * @return List of size, largest index, and default index.
     */
    public  <T> List<Integer> getLargestChildFreeholdBuildingSizeIndex(List<T> childs, String key, boolean isDefaultRequired) {
        Integer largestMediaIndex = null;
        Integer defaultMediaIndex = null;
        double largestBuildingSize = -1;

        for (int i = 0; i < childs.size(); i++) {
            T child = childs.get(i);
            Object rawValue = getFieldValue(child, key);
            Object mediaId = getFieldValue(child, "mediaId");

            double size = (rawValue instanceof Number) ? ((Number) rawValue).doubleValue() : -1;

            if (size > largestBuildingSize) {
                largestBuildingSize = size;
                largestMediaIndex = i;
            }
        }

        if (isDefaultRequired && largestBuildingSize > 0) {
            double finalSize = largestBuildingSize;

            List<T> largestChildMedias = childs.stream()
                    .filter(child -> {
                        Object sizeVal = getFieldValue(child, key);
                        double val = (sizeVal instanceof Number) ? ((Number) sizeVal).doubleValue() : -1;
                        return val == finalSize;
                    })
                    .collect(Collectors.toList());

            largestChildMedias.forEach(child -> {
                Object mid = getFieldValue(child, "mediaId");
                Object isDefault = getFieldValue(child, "isDefault");
            });

            Optional<T> defaultMediaOpt = largestChildMedias.stream()
                    .filter(child -> {
                        Object isDefaultVal = getFieldValue(child, "isDefault");
                        return (isDefaultVal instanceof Boolean && (Boolean) isDefaultVal)
                                || (isDefaultVal instanceof Number && ((Number) isDefaultVal).intValue() == 1);
                    })
                    .findFirst();


            if (defaultMediaOpt.isPresent()) {
                T defaultMedia = defaultMediaOpt.get();
                int mediaId = toInt(getFieldValue(defaultMedia, "mediaId"));
                defaultMediaIndex = findIndexBymediaId(childs, mediaId);
            } else {
                Integer defaultmediaId = determineDefaultMedia(largestChildMedias);
                defaultMediaIndex = (defaultmediaId != null) ? findIndexBymediaId(childs, defaultmediaId) : null;
            }
        }

        return Arrays.asList((int) largestBuildingSize, largestMediaIndex, defaultMediaIndex);
    }


    /**
     * Finds the index of a media in the original list of child media objects using its mediaId.
     *
     * @param childs  The list of media objects to search within.
     * @param mediaId The mediaId to find in the list.
     * @return The index of the media with the given mediaId, or null if not found.
     */

    private  <T> Integer findIndexBymediaId(List<T> childs, int mediaId) {
        for (int i = 0; i < childs.size(); i++) {
            if (toInt(getFieldValue(childs.get(i), "mediaId")) == mediaId) {
                return i;
            }
        }
        return null;
    }

    /**
     * Capitalizes the first character of a string.
     * <p>
     * This utility is typically used to convert a field name into a method-compatible format,
     * such as when dynamically constructing getter or setter method names via reflection.
     * For example, a field name like {@code "propertyUse"} becomes {@code "PropertyUse"},
     * allowing reflection to call methods like {@code getPropertyUse()} or {@code setPropertyUse(...)}.
     * </p>
     *
     * @param str The input string to capitalize.
     * @return The string with its first character capitalized, or the original string if null or empty.
     */
    private  String capitalize(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    /**
     * Infers the expected setter method parameter type based on the runtime type of the given value.
     * <p>
     * This method is useful when invoking setters via reflection, as it helps determine
     * the correct parameter class type that should be passed to the method.
     * </p>
     *
     * @param value The value whose type is to be inferred.
     * @return The corresponding Java Class of the value's type.
     */
    private  Class<?> getSetterType(Object value) {
        if (value instanceof BigDecimal) return BigDecimal.class;
        if (value instanceof Integer) return Integer.class;
        if (value instanceof Long) return Long.class;
        if (value instanceof Double) return Double.class;
        if (value instanceof String) return String.class;
        if (value instanceof Boolean) return Boolean.class;
        return value.getClass(); // fallback
    }

    /**
     * Formats a range string from two Date objects based on the key's naming convention.
     * <p>
     * If the key contains "Min", "Max", or similar suffixes, it returns only the min or max date,
     * otherwise it returns a combined range in the format "minDate - maxDate".
     * </p>
     *
     * @param key The field key used to determine formatting behavior.
     * @param min The minimum Date value.
     * @param max The maximum Date value.
     * @return A formatted date range string or a single date based on the key.
     */
    private  String formatRange(String key, Date min, Date max) {
        String minStr = min.toInstant().toString();
        String maxStr = max.toInstant().toString();

        if (key.contains("Min") || key.contains("MinM") || key.contains("Smallest")) {
            return minStr;
        } else if (key.contains("Max") || key.contains("MaxM") || key.contains("Largest")) {
            return maxStr;
        } else if (min.equals(max)) {
            return minStr;
        } else {
            return minStr + " - " + maxStr;
        }
    }

    /**
     * Formats a numeric range string based on the key's naming pattern.
     * <p>
     * If the key indicates a min/max field, only the respective value is returned;
     * otherwise a full range in the form "min - max" is returned.
     * </p>
     *
     * @param key The field key used to decide formatting rules.
     * @param min The minimum numeric value.
     * @param max The maximum numeric value.
     * @return A formatted string representing the numeric range or single value.
     */
    private  String formatRange(String key, double min, double max) {
        if (key.contains("Min") || key.contains("MinM") || key.contains("Smallest")) {
            return String.valueOf(min);
        } else if (key.contains("Max") || key.contains("MaxM") || key.contains("Largest")) {
            return String.valueOf(max);
        } else if (Double.compare(min, max) == 0) {
            return String.valueOf(min);
        } else {
            return min + " - " + max;
        }
    }

    /**
     * Dynamically sets a string value on the specified field of the rollup object using reflection.
     * <p>
     * This is typically used to set derived string values (e.g., range or unique value aggregation results)
     * when the setter method is not known at compile-time.
     * </p>
     *
     * @param rollupObject The object to update.
     * @param javaField    The field name to set (used to construct the setter method name).
     * @param value        The string value to assign to the field.
     * @throws PropertyRollupException if reflection fails to set the value.
     */
    private  void setRollupValue(PropertyMasterRollupObject rollupObject, String javaField, String value) {
        try {
            String setterName = "set" + capitalize(javaField);

            for (Method method : PropertyMasterRollupObject.class.getMethods()) {
                if (method.getName().equals(setterName)
                        && method.getParameterCount() == 1
                        && method.getParameterTypes()[0] == String.class) {
                    method.invoke(rollupObject, value);
                    return;
                }
            }

            throw new PropertyRollupException("Setter not found for field: " + javaField);

        } catch (Exception e) {
            throw new PropertyRollupException("Failed to set range field on rollup object: " + javaField, e);
        }
    }

    /**
     * Determines the most appropriate default media from a list of media items.
     * <p>
     * Priority is given based on media type and subtype in the following order:
     * 1. Building Image + Front
     * 2. Building Image + Main Photo
     * 3. Any Building Image
     * 4. Aerial Imagery
     * 5. Fallback to the first media in the list, if none of the above matches.
     * </p>
     *
     * @param medias List of media objects to evaluate.
     * @return The mediaId of the selected default media, or null if the list is empty.
     */
    private  <T> Integer determineDefaultMedia(List<T> medias) {
        return medias.stream()
                .filter(m -> getFieldValue(m, "mediaTypeId") == MediaType.BUILDING_IMAGE
                        && getFieldValue(m, "mediaSubTypeId") == MediaSubType.FRONT)
                .findFirst()
                .map(m -> toInt(getFieldValue(m, "mediaId")))
                .orElseGet(() ->
                        medias.stream()
                                .filter(m -> getFieldValue(m, "mediaTypeId") == MediaType.BUILDING_IMAGE
                                        && getFieldValue(m, "mediaSubTypeId") == MediaSubType.MAIN_PHOTO)
                                .findFirst()
                                .map(m -> toInt(getFieldValue(m, "mediaId")))
                                .orElseGet(() ->
                                        medias.stream()
                                                .filter(m -> getFieldValue(m, "mediaTypeId") == MediaType.BUILDING_IMAGE)
                                                .findFirst()
                                                .map(m -> toInt(getFieldValue(m, "mediaId")))
                                                .orElseGet(() ->
                                                        medias.stream()
                                                                .filter(m -> getFieldValue(m, "mediaTypeId") == MediaType.AERIAL_IMAGERY)
                                                                .findFirst()
                                                                .map(m -> toInt(getFieldValue(m, "mediaId")))
                                                                .orElseGet(() ->
                                                                        medias.isEmpty() ? null : toInt(getFieldValue(medias.get(0), "mediaId"))
                                                                )
                                                )
                                )
                );
    }

    /**
     * Assigns default property use and useTypeID based on the child with largest building size.
     *
     * @param rollupObject   Rollup object to update.
     * @param childFreehold  List of child properties.
     * @param sizeKey        Field name for size.
     * @param propertyUseKey Field name for use description.
     */
    public  <T> void rollupDefaultPropertyUseAndUseTypeID(
            PropertyMasterRollupObject rollupObject,
            List<T> childFreehold,
            String sizeKey,
            String propertyUseKey
    ) {
        List<Integer> result = getLargestChildFreeholdBuildingSizeIndex(childFreehold, sizeKey, false);
        Integer largestMediaIndex = result.get(1);

        if (largestMediaIndex != null && largestMediaIndex >= 0 && largestMediaIndex < childFreehold.size()) {
            T largestChild = childFreehold.get(largestMediaIndex);

            // Fetch values from the child
            Object propertyUseValue = getFieldValue(largestChild, propertyUseKey);
            Object useTypeIDValue = getFieldValue(largestChild, "useTypeID");

            // Set in rollupObject explicitly
            if (propertyUseValue instanceof String) {
                rollupObject.setPropertyUse((String) propertyUseValue);
            }

            if (useTypeIDValue instanceof Number) {
                rollupObject.setUseTypeID(((Number) useTypeIDValue).intValue());
            }
        }
    }

    /**
     * Safely converts an object to an integer.
     * <p>
     * Supports {@link Number} types and numeric strings. If conversion fails or the value
     * is not convertible, returns 0 as a fallback.
     * </p>
     *
     * @param obj The object to convert.
     * @return The integer representation of the object, or 0 if conversion is not possible.
     */
    private  int toInt(Object obj) {
        if (obj instanceof Number) return ((Number) obj).intValue();
        if (obj instanceof String) {
            try {
                return Integer.parseInt((String) obj);
            } catch (NumberFormatException ignored) {}
        }
        return 0;
    }

    /**
     * Dynamically retrieves the value of a field from an object using its getter method.
     * <p>
     * This method assumes standard JavaBean naming conventions (e.g., "getFieldName").
     * If the getter does not exist or invocation fails, it returns {@code null}.
     * </p>
     *
     * @param obj       The target object from which the value should be retrieved.
     * @param fieldName The name of the field (not the method).
     * @return The value returned by the getter method, or {@code null} if not accessible.
     */
    private  Object getFieldValue(Object obj, String fieldName) {
        try {
            String methodName = "get" + capitalize(fieldName);
            Method method = obj.getClass().getMethod(methodName);
            return method.invoke(obj);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Dynamically sets a field's value on an object using its setter method via reflection.
     * <p>
     * This method locates the appropriate setter based on the field name and its declared type.
     * It assumes standard JavaBean conventions (e.g., "setFieldName"). If the setter cannot be
     * found or invoked, it throws a {@link PropertyRollupException}.
     * </p>
     *
     * @param obj       The target object on which the field is to be set.
     * @param fieldName The name of the field to be updated.
     * @param value     The value to set on the field.
     * @throws PropertyRollupException if the setter method or field access fails.
     */
    private  void setFieldValue(Object obj, String fieldName, Object value) {
        try {
            String methodName = "set" + capitalize(fieldName);
            var fieldType = obj.getClass().getDeclaredField(fieldName).getType();
            var method = obj.getClass().getMethod(methodName, fieldType);
            method.invoke(obj, value);
        } catch (Exception e) {
            throw new PropertyRollupException("Failed to set field '" + fieldName + "' on object of type "
                    + obj.getClass().getSimpleName(), e);
        }
    }

    /**
     * Creates a section name string based on floor range and unit.
     *
     * @param minFloor     Minimum floor.
     * @param maxFloor     Maximum floor.
     * @param useTypeName  Type name to be abbreviated.
     * @param unit         Building unit/label.
     * @return Constructed section name.
     */
    public  String createSectionName(Object minFloor, Object maxFloor, String useTypeName, String unit) {
        String label = (useTypeName != null && !useTypeName.isEmpty())
                ? "(" + Character.toUpperCase(useTypeName.charAt(0)) + ")"
                : "";

        StringBuilder sectionName = new StringBuilder();

        if (unit != null && !unit.isEmpty()) {
            sectionName.append("Building - ").append(unit).append(" ");
        }

        sectionName.append(minFloor).append("-").append(maxFloor).append(label);

        return sectionName.toString();
    }

    public Object parseFloor(Object floor) {
        if (floor == null) return null;

        if (floor instanceof Number) {
            int num = ((Number) floor).intValue();
            return num == 1 ? "G" : num - 1;
        }

        if (floor instanceof String) {
            try {
                int num = Integer.parseInt((String) floor);
                return num == 1 ? "G" : num - 1;
            } catch (NumberFormatException e) {
                return floor;
            }
        }

        return null;
    }

    /**
     * Aggregates media objects from child freehold properties into the master freehold media list,
     * ensuring a default image is assigned if the master does not already have one.
     * <p>
     * This method ensures that:
     * <ul>
     *   <li>If no default image exists in the master media, the largest child media is used to set the default.</li>
     *   <li>If a default exists in the master, all child media are explicitly marked as non-default.</li>
     *   <li>The final list returned contains media from both master and child freeholds.</li>
     * </ul>
     * </p>
     *
     * @param masterFreeholdMedia List of media objects belonging to the master freehold.
     * @param childFreeholdMedia  List of media objects belonging to the child freeholds.
     * @param <T>                 The generic media object type that contains fields like `mediaId`, `buildingSizeSF`, and `isDefault`.
     * @return A combined list of media from master and child freeholds, with default image correctly assigned.
     */
    public  <T> List<T> aggregateChildFreeholdMediaToMasterFreeholdWithDefaultImage(
            List<T> masterFreeholdMedia,
            List<T> childFreeholdMedia
    ) {
        // Step 1: Check if masterFreeholdMedia has any default media (isDefault == true)
        boolean isMasterDefaultMediaExists = masterFreeholdMedia.stream()
                .anyMatch(media -> Boolean.TRUE.equals(getFieldValue(media, "isDefault")));

        if (!isMasterDefaultMediaExists) {
            // Step 2: Find default media index from children
            List<Integer> result = getLargestChildFreeholdBuildingSizeIndex(
                    childFreeholdMedia, "buildingSizeSF", true);
            Integer defaultMediaIndex = result.size() > 2 ? result.get(2) : null;

            if (defaultMediaIndex != null && defaultMediaIndex >= 0 && defaultMediaIndex < childFreeholdMedia.size()) {
                setFieldValue(childFreeholdMedia.get(defaultMediaIndex), "isDefault", Boolean.TRUE);
            }
        } else {
            // Step 3: Set all child media isDefault = false
            for (T media : childFreeholdMedia) {
                setFieldValue(media, "isDefault", Boolean.FALSE);
            }
        }

        // Step 4: Merge and return combined list
        List<T> combined = new ArrayList<>();
        combined.addAll(masterFreeholdMedia);
        combined.addAll(childFreeholdMedia);

        return combined;
    }

}

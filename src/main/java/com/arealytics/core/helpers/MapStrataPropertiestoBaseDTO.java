package com.arealytics.core.helpers;

import com.arealytics.core.dto.querydsl.PropertySearchQuerydslDTO;
import com.arealytics.core.dto.response.PropertySearchAllResponseDTO;
import com.arealytics.core.dto.response.PropertySearchResponseBaseDTO;

public class MapStrataPropertiestoBaseDTO {
    public static PropertySearchResponseBaseDTO mapToBaseDTO(PropertySearchQuerydslDTO strata) {
        PropertySearchResponseBaseDTO response = new PropertySearchResponseBaseDTO();

        response.setPropertyID(strata.getPropertyID());
        response.setPropertyName(strata.getPropertyName());
        response.setUseTypeID(strata.getUseTypeID());
        response.setUseTypeName(strata.getUseTypeName());
        response.setSpecificUseID(strata.getSpecificUseID());
        response.setSpecificUseName(strata.getSpecificUseName());
        response.setCityName(strata.getCityName());
        response.setStateName(strata.getAddress() != null ? strata.getAddress().getStateName() : null);
        response.setModifiedDate(strata.getModifiedDate());
        response.setCondoTypeID(strata.getCondoTypeID());
        response.setBuildingSizeSF(strata.getBuildingSizeSF());
        response.setAddress(strata.getAddress());
        response.setLocation(strata.getLocation());

        // === All Properties SubDTO ===
        PropertySearchAllResponseDTO propertySearchResultDto = new PropertySearchAllResponseDTO();
        propertySearchResultDto.setIsActive(strata.getIsActive());
        propertySearchResultDto.setYearBuilt(strata.getYearBuilt());
        propertySearchResultDto.setFloors(strata.getFloors());
        propertySearchResultDto.setCondoUnit(strata.getCondoUnit());
        propertySearchResultDto.setComments(strata.getBuildingComments());
        propertySearchResultDto.setLastReviewedBy(strata.getLastReviewedBy());
        propertySearchResultDto.setLastReviewedDate(strata.getLastReviewedDate());
        propertySearchResultDto.setLotSizeSF(strata.getLotSizeSF());
        propertySearchResultDto.setContributedGBASizeSF(strata.getContributedGBASizeSF());
        propertySearchResultDto.setResearchTypeID(strata.getResearchTypeID());
        propertySearchResultDto.setResearchTypeName(strata.getResearchTypeName());
        propertySearchResultDto.setIsSkipped(strata.getIsSkipped());
        propertySearchResultDto.setMainPhotoUrl(strata.getMainPhotoURL());
        propertySearchResultDto.setHasNoBuildingFootprints(strata.getHasNoBuildingFootprints());
        propertySearchResultDto.setHasNoExistingParcelInTileLayer(strata.getHasNoExistingParcelInTileLayer());
        propertySearchResultDto.setMasterPropertyID(strata.getMasterPropertyId());
        propertySearchResultDto.setCreatedDate(strata.getCreatedDate());
        propertySearchResultDto.setModifiedDate(strata.getModifiedDate());
        propertySearchResultDto.setCreatedBy(strata.getCreatedBy());
        propertySearchResultDto.setModifiedBy(strata.getModifiedBy());
        propertySearchResultDto.setAuditStatus(strata.getAuditStatus());

        // Link the sub-dto to the main response
        response.setAllPropertiesDetails(propertySearchResultDto);

        return response;
    }
}

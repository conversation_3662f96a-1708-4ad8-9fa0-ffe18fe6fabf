package com.arealytics.core.helpers;

import java.util.List;
import java.util.Map;

public class RollupKeys {

    public static final Map<String, String> AGGREGATE_SUM_KEYS = Map.ofEntries(
            Map.entry("AwningsCount", "awningsCount"),
            Map.entry("BuildingSF", "buildingSizeSF"),
            Map.entry("BuildingSizeSM", "buildingSizeSM"),
            Map.entry("DockHigh", "dockHigh"),
            Map.entry("FreighElevators", "freighElevators"),
            Map.entry("GradeLevelDriveIn", "gradeLevelIn"),
            Map.entry("Anchors", "noOfAnchor"),
            Map.entry("NoOfAnchor", "noOfAnchors"),
            Map.entry("ParkingElevators", "parkingElevators"),
            Map.entry("ParkingRatio", "parkingRatio"),
            Map.entry("ParkingSpaces", "parkingSpaces"),
            Map.entry("PassengerElevators", "passengerElevators"),
            Map.entry("ReservedParkingSpaces", "reservedParkingSpaces"),
            Map.entry("RetailFrontage", "retailFrontage"),
            Map.entry("TotalAnchorSF", "totalAnchorSF"),
            Map.entry("TrafficCount", "trafficCount"),
            Map.entry("Truckwell", "truckwell"),
            Map.entry("UnreservedParkingSpaces", "unreservedParkingSpaces"),
            Map.entry("LiftsCount", "liftsCount"),
            Map.entry("NoOfUnits", "noOfUnits")
    );


    public static final Map<String, String> RANGE_KEYS = Map.ofEntries(
            Map.entry("ActualCompletion", "actualCompletion"),
            Map.entry("Awnings_Size_SF", "awningsSizeSF"),
            Map.entry("Awnings_Size_SM", "awningsSizeSM"),
            Map.entry("BookValue", "bookValue"),
            Map.entry("BookValueDate", "bookValueDate"),
            Map.entry("ClearHeightMax", "clearHeightMax"),
            Map.entry("ClearHeightMaxM", "clearHeightMaxM"),
            Map.entry("ClearHeightMin", "clearHeightMin"),
            Map.entry("ClearHeightMinM", "clearHeightMinM"),
            Map.entry("ContributedGBA_SF", "contributedGBASizeSF"),
            Map.entry("ContributedGBA_SM", "contributedGBASM"),
            Map.entry("ConstructionStartDate", "constructionStartDate"),
            Map.entry("EstCompletionDate", "estCompletion"),
            Map.entry("GLA_SF", "glaSizeSF"),
            Map.entry("GLA_SM", "glaSM"),
            Map.entry("GLAR_SF", "glarSizeSF"),
            Map.entry("GLAR_SM", "glarSM"),
            Map.entry("GRESBScoreMax", "gresbScoreMax"),
            Map.entry("GRESBScoreMin", "gresbScoreMin"),
            Map.entry("HardstandArea", "hardstandArea"),
            Map.entry("LargestFloor", "largestFloor"),
            Map.entry("LargestFloorSM", "largestFloorSM"),
            Map.entry("Mezzanine_Size_SF", "mezzanineSizeSF"),
            Map.entry("Mezzanine_Size_SM", "mezzanineSizeSM"),
            Map.entry("NoOfOfficeFloors", "noOfOfficeFloors"),
            Map.entry("OfficeSF", "officeSize"),
            Map.entry("OfficeSM", "officeSM"),
            Map.entry("ReservedParkingSpacesRatePerMonth", "reservedParkingSpacesRatePerMonth"),
            Map.entry("SmallestFloor", "smallestFloor"),
            Map.entry("SmallestFloorSM", "smallestFloorSM"),
            Map.entry("TIAllowance", "tiAllowance"),
            Map.entry("TitleReferenceDate", "titleReferenceDate"),
            Map.entry("TypicalFloorSize", "typicalFloorSize"),
            Map.entry("TypicalFloorSizeSM", "typicalFloorSizeSM"),
            Map.entry("UnreservedParkingSpacesRatePerMonth", "unreservedParkingSpacesRatePerMonth"),
            Map.entry("Vacancy", "vacancy"),
            Map.entry("Floors", "floors")
    );


    public static final Map<String, String> YES_NO_KEYS = Map.ofEntries(
            Map.entry("Awnings", "awnings"),
            Map.entry("HVAC", "hvac"),
            Map.entry("HasPortAccess", "hasPortAccess"),
            Map.entry("HasResCoveredParking", "hasResCoveredParking"),
            Map.entry("HasSolar", "hasSolar"),
            Map.entry("HasUnreservedParkingSpaces", "hasUnreservedParkingSpaces"),
            Map.entry("HasYard", "hasYard"),
            Map.entry("HasYardFenced", "hasYardFenced"),
            Map.entry("HasYardUnfenced", "hasYardUnfenced"),
            Map.entry("IncludeinAnalytics", "includeInAnalytics"),
            Map.entry("IsADAAccessible", "isADAAccessible"),
            Map.entry("IsOwnerOccupied", "isOwnerOccupied"),
            Map.entry("IsVented", "isVented"),
            Map.entry("Lifts", "lifts"),
            Map.entry("Mezzanine", "mezzanine"),
            Map.entry("RailServed", "railServed"),
            Map.entry("YardPaved", "yardPaved"),
            Map.entry("HasReservedParkingSpaces", "hasReservedParkingSpaces"),
            Map.entry("HasSprinkler", "hasSprinkler"),
            Map.entry("IsCraneServed", "craneServed")
    );



    public static final Map<String, String> UNIQUE_KEYS = Map.ofEntries(
            Map.entry("Amps", "amps"),
            Map.entry("AmenitiesType", "amenitiesType"),
            Map.entry("BldgSizeSourceName", "bldgSizeSourceName"),
            Map.entry("BuildingWebsite", "buildingWebsite"),
            Map.entry("ClassTypeName", "classTypeName"),
            Map.entry("classTypeID", "classTypeID"),
            Map.entry("CondoTypeName", "condoTypeName"),
            Map.entry("ConstructionStatusID", "constructionStatusID"),
            Map.entry("ConstructionStatusName", "constructionStatusName"),
            Map.entry("ConstructionTypeID", "constructionTypeID"),
            Map.entry("ConstructionTypeName", "constructionTypeName"),
            Map.entry("contributedGBASizeSourceID", "contributedGBASizeSourceID"),
            Map.entry("EnergyStarRatingName", "energyStarRatingName"),
            Map.entry("energyStarRatingID", "energyStarRatingID"),
            Map.entry("FeatureIDs", "featureIDs"),
            Map.entry("glasSizeSourceID", "glasSizeSourceID"),
            Map.entry("glarSizeSourceID", "glarSizeSourceID"),
            Map.entry("GreenStarRatingName", "greenStarRatingName"),
            Map.entry("greenStarRatingID", "greenStarRatingID"),
            Map.entry("governmentInterestID", "governmentInterestID"),
            Map.entry("HvacTypeName", "hvacTypeName"),
            Map.entry("hvacTypeID", "hvacTypeID"),
            Map.entry("OfficeHVAC", "officeHvac"),
            Map.entry("powerType", "powerType"),
            Map.entry("PowerTypeName", "powerTypeName"),
            Map.entry("sizeSourceID", "sizeSourceID"),
            Map.entry("SpecificUseName", "specificUseName"),
            Map.entry("sprinklerTypeID", "sprinklerTypeID"),
            Map.entry("SprinklerTypeName", "sprinklerTypeName"),
            Map.entry("TenancyName", "tenancyName"),
            Map.entry("tenancyTypeID", "tenancyTypeID"),
            Map.entry("Volts", "volts"),
            Map.entry("WaterStarRatingName", "waterStarRatingName"),
            Map.entry("waterStarRatingID", "waterStarRatingID"),
            Map.entry("YearBuilt", "yearBuilt"),
            Map.entry("YearRenovated", "yearRenovated"),
            Map.entry("ZoningCode", "zoningCode"),
            Map.entry("buildSpecStatusID", "buildSpecStatusID"),
            Map.entry("RoofTypeName", "roofTypeName"),
            Map.entry("currentTitle", "currentTitle"),
            Map.entry("GovernmentInterestName", "governmentInterestName"),
            Map.entry("BuildSpecStatusName", "buildSpecStatusName"),
            Map.entry("phase", "phase"),
            Map.entry("typicalFloorSizeSourceID", "typicalFloorSizeSourceId"),
            Map.entry("hardstandAreaSourceID", "hardstandAreaSourceID"),
            Map.entry("HardstandAreaSourceName", "hardstandAreaSourceName"),
            Map.entry("NRASizeSourceID", "NRASizeSourceID"),
            Map.entry("NRASizeSourceName", "NRASizeSourceName"),
            Map.entry("lotSizeSourceID", "lotSizeSourceID"),
            Map.entry("LotSizeSourceName", "lotSizeSourceName")
    );

}

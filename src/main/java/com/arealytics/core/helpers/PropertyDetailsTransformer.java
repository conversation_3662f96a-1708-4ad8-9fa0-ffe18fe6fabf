package com.arealytics.core.helpers;

import com.arealytics.core.dto.querydsl.PropertyDetailsQuerydslDTO;
import com.arealytics.core.dto.response.PropertyDetailsSizeResponseDTO;
import com.arealytics.core.enumeration.AmenitiesType;
import com.arealytics.core.enumeration.Features;
import com.arealytics.core.utils.EnumUtil;
import com.arealytics.core.utils.PropertyEnumLabelsUtil;
import com.arealytics.core.utils.UnitConversionUtil;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class PropertyDetailsTransformer {
    public static PropertyDetailsSizeResponseDTO convert(PropertyDetailsQuerydslDTO propertyDetails) {
        Map<String, String> enumFieldMap = new HashMap<>();
        enumFieldMap.put("ClassTypeID", "ClassTypeName");
        enumFieldMap.put("TenancyTypeID", "TenancyName");
        enumFieldMap.put("CondoTypeID", "CondoTypeName");
        enumFieldMap.put("SizeSourceID", "BldgSizeSourceName");
        enumFieldMap.put("EnergyStarRatingID", "EnergyStarRatingName");
        enumFieldMap.put("WaterStarRatingID", "WaterStarRatingName");
        enumFieldMap.put("GreenStarRatingID", "GreenStarRatingName");
        enumFieldMap.put("ConstructionTypeID", "ConstructionTypeName");
        enumFieldMap.put("ConstructionStatusID", "ConstructionStatusName");
        enumFieldMap.put("HvacTypeID", "HvacTypeName");
        enumFieldMap.put("SprinklerTypeID", "SprinklerTypeName");
        enumFieldMap.put("PowerType", "PowerTypeName");
        enumFieldMap.put("BuildSpecStatusID", "BuildSpecStatusName");
        enumFieldMap.put("RoofTypeID", "RoofTypeName");
        enumFieldMap.put("GovernmentInterestID", "GovernmentInterestName");
        enumFieldMap.put("HardstandAreaSourceID", "HardstandAreaSourceName");
        enumFieldMap.put("NRASizeSourceID", "NRASizeSourceName");
        enumFieldMap.put("ContributedGBASizeSourceID", "ContributedGBASizeSourceName");
        enumFieldMap.put("GlasSizeSourceID", "GLASizeSourceName");
        enumFieldMap.put("GlarSizeSourceID", "GLARSizeSourceName");
        enumFieldMap.put("LotSizeSourceID", "LotSizeSourceName");

        PropertyDetailsSizeResponseDTO response = new PropertyDetailsSizeResponseDTO();
        response = PropertyEnumLabelsUtil.mapEnumLabels(propertyDetails, enumFieldMap);
        BeanUtils.copyProperties(propertyDetails, response);

        if (propertyDetails.getLotSizeSF() != null) {
            BigDecimal lotSizeSM = BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getLotSizeSF().doubleValue()));
            response.setLotSizeSM(lotSizeSM);
            response.setLotSizeSMFormatted(lotSizeSM.setScale(0, RoundingMode.HALF_UP).toPlainString());
        }
        if (propertyDetails.getLotSizeAC() != null) {
            response.setLotSizeACSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getLotSizeAC().doubleValue())));
        }
        if (propertyDetails.getBuildingSizeSF() != null) {
            BigDecimal buildingSizeSM = BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getBuildingSizeSF().doubleValue()));
            response.setBuildingSizeSM(buildingSizeSM);
            response.setBuildingSizeSMFormatted(buildingSizeSM.setScale(0, RoundingMode.HALF_UP).toPlainString());
        }
        if (propertyDetails.getContributedGBASizeSF() != null) {
            response.setContributedGBASM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getContributedGBASizeSF().doubleValue())));
        }
        if (propertyDetails.getGlaSizeSF() != null) {
            response.setGlaSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getGlaSizeSF().doubleValue())));
        }
        if (propertyDetails.getGlarSizeSF() != null) {
            response.setGlarSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getGlarSizeSF().doubleValue())));
        }
        if (propertyDetails.getMezzanineSizeSF() != null) {
            response.setMezzanineSizeSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getMezzanineSizeSF().doubleValue())));
        }
        if (propertyDetails.getAwningsSizeSF() != null) {
            response.setAwningsSizeSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getAwningsSizeSF().doubleValue())));
        }
        if (propertyDetails.getRetailSize() != null) {
            response.setRetailSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getRetailSize().doubleValue())));
        }
        if (propertyDetails.getOfficeSize() != null) {
            response.setOfficeSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getOfficeSize().doubleValue())));
        }
        if (propertyDetails.getNla() != null) {
            response.setNlaSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getNla().doubleValue())));
        }
        if (propertyDetails.getClearHeightMin() != null) {
            response.setClearHeightMinM(BigDecimal.valueOf(UnitConversionUtil.feetToMeters(propertyDetails.getClearHeightMin().doubleValue())));
        }
        if (propertyDetails.getClearHeightMax() != null) {
            response.setClearHeightMaxM(BigDecimal.valueOf(UnitConversionUtil.feetToMeters(propertyDetails.getClearHeightMax().doubleValue())));
        }
        if (propertyDetails.getTypicalFloorSize() != null) {
            response.setTypicalFloorSizeSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getTypicalFloorSize().doubleValue())));
        }
        if (propertyDetails.getDepth() != null) {
            response.setDepthM(BigDecimal.valueOf(UnitConversionUtil.feetToMeters(propertyDetails.getDepth().doubleValue())));
        }
        if (propertyDetails.getWidth() != null) {
            response.setWidthM(BigDecimal.valueOf(UnitConversionUtil.feetToMeters(propertyDetails.getWidth().doubleValue())));
        }
        if (propertyDetails.getBayWidth() != null) {
            response.setBayWidth(BigDecimal.valueOf(UnitConversionUtil.feetToMeters(propertyDetails.getBayWidth().doubleValue())));
        }
        if (propertyDetails.getBayDepth() != null) {
            response.setBayDepth(BigDecimal.valueOf(UnitConversionUtil.feetToMeters(propertyDetails.getBayDepth().doubleValue())));
        }
        if (propertyDetails.getSmallestFloor() != null) {
            response.setSmallestFloorSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getSmallestFloor().doubleValue())));
        }
        if (propertyDetails.getLargestFloor() != null) {
            response.setLargestFloorSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getLargestFloor().doubleValue())));
        }
        if (propertyDetails.getTotalAnchor() != null) {
            response.setTotalAnchorSF(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getTotalAnchor().doubleValue())));
        }
        if (propertyDetails.getRetailFrontage() != null) {
            response.setRetailFrontageM(BigDecimal.valueOf(UnitConversionUtil.feetToMeters(propertyDetails.getRetailFrontage().doubleValue())));
        }
        if (propertyDetails.getFeatureIDs() != null) {
            response.setFeatureIDs(EnumUtil.getEnumValuesFromIds(propertyDetails.getFeatureIDs(), Features.class, false));
        }
        if (propertyDetails.getAmenitiesType() != null) {
            response.setAmenitiesType(EnumUtil.getEnumValuesFromIds(propertyDetails.getAmenitiesType(), AmenitiesType.class, false));
        }
        if(propertyDetails.getHardstandArea() != null){
            response.setHardstandAreaSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getHardstandArea().doubleValue())));
        }
        return response;
    }

    public static List<PropertyDetailsSizeResponseDTO> convertList(List<PropertyDetailsQuerydslDTO> propertyDetailsList) {
        return propertyDetailsList.stream()
                .map(PropertyDetailsTransformer::convert)
                .collect(Collectors.toList());
    }
}

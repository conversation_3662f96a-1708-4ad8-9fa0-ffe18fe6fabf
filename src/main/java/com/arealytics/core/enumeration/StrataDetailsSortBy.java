package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

@Getter
@RequiredArgsConstructor
public enum StrataDetailsSortBy {
  STRATA_TYPE("StrataType"),
  ADDRESS("Address"),
  STRATA_UNIT("StrataUnit"),
  BUILDING_SF("BuildingSF"),
  LOT_SIZE_SF("LotSizeSF"),
  BUILDING_SIZE_SM("BuildingSizeSM"),
  LOT_SIZE_SM("LotSizeSM"),
  LAST_SALE_DATE("LastSaleDate"),
  LAST_SALE_PRICE("LastSalePrice"),
  PARCEL_NUMBERS("ParcelNumbers"),
  PROPERTY_ID("PropertyID");

  private final String label;

  public String getLabel() {
    return label;
  }

  public static StrataDetailsSortBy fromLabel(String label) {
    return Arrays.stream(values())
        .filter(e -> e.label.equalsIgnoreCase(label))
        .findFirst()
        .orElse(null);
  }
}

package com.arealytics.core.enumeration;

public enum Quadrant implements BaseEnum {
    NW(1, "NW"),
    NE(2, "NE"),
    SW(3, "SW"),
    SE(4, "SE");

    private final int id;
    private final String label;

    Quadrant(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static Quadrant fromId(int id) {
        for (Quadrant q : values()) {
            if (q.id == id) {
                return q;
            }
        }
        throw new IllegalArgumentException("Invalid Quadrant ID: " + id);
    }
}

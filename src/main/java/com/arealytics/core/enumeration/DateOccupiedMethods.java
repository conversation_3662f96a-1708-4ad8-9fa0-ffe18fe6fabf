package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum DateOccupiedMethods implements BaseEnum {
  ACTUAL(1, "Actual"),
  LISTING(2, "Listing"),
  PRESS_RELEASE_AND_THIRD_PARTY(5, "Press Release Date and Third Party");

  private final int id;
  private final String label;

  public static DateOccupiedMethods fromId(int id) {
    for (DateOccupiedMethods source : values()) {
      if (source.id == id) {
        return source;
      }
    }
    throw new IllegalArgumentException("Invalid DateOccupiedMethods id: " + id);
  }
}

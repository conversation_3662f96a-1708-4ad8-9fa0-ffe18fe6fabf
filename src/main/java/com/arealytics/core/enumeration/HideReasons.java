package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum HideReasons implements BaseEnum {
  CANNOT_VERIFY(1, "Cannot Verify"),
  OUT_OF_BUSINESS(2, "Out of Business"),
  <PERSON><PERSON><PERSON>(3, "Other"),
  SUBSIDIARY(4, "Subsidiary"),
  EXPIRED_LEASE(5, "Expired Lease"),
  OUT_OF_METRO(6, "Current Address - Out of Metro"),
  COWORKING_TENANT(7, "Co-working Tenant"),
  WITHIN_EXCLUSION_ZONE(8, "Within exclusion zone"),
  HIDING_COMPANIES(9, "Hiding companies"),
  TSA_GROUPING_ACTIVITY(10, "TSA Grouping activity"),
  SPEEDWAY_GROUPING_ACTIVITY(11, "Lease Transaction Speedway Grouping Activity");

  private final int id;
  private final String label;

  public static HideReasons fromId(int id) {
    for (HideReasons reason : values()) {
      if (reason.id == id) {
        return reason;
      }
    }
    throw new IllegalArgumentException("Invalid HideReasons id: " + id);
  }
}

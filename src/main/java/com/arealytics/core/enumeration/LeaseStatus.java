package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum LeaseStatus implements BaseEnum {
  HISTORIC(1, "Historic"),
  ACTIVE(2, "Active"),
  PRE_COMMIT(3, "Pre Commit");

  private final int id;
  private final String label;

  public static LeaseStatus fromId(int id) {
    for (LeaseStatus status : values()) {
      if (status.id == id) {
        return status;
      }
    }
    throw new IllegalArgumentException("Invalid LeaseStatus id: " + id);
  }
}

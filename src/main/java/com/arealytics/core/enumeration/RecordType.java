package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@AllArgsConstructor
public enum RecordType implements BaseEnum {
  LEASE(1, "Lease"),
  SALE(2, "Sale");

  private final int id;
  private final String label;

  public static RecordType fromId(int id) {
    for (RecordType mode : values()) {
      if (mode.id == id) {
        return mode;
      }
    }
    throw new IllegalArgumentException("No ListingMode with id " + id);
  }
}

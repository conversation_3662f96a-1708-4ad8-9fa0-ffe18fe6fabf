package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PossessionType implements BaseEnum {
  THIRTY_DAYS(1, "30 Days"),
  SIXTY_DAYS(2, "60 Days"),
  NINETY_DAYS(3, "90 Days"),
  CLOSE_OF_ESCROW(4, "Close Of Escrow"),
  COMPLETION(5, "Completion"),
  DATE(6, "Date"),
  NOW(7, "Now");

  private final int id;
  private final String label;

  public static PossessionType fromId(int id) {
    for (PossessionType type : values()) {
      if (type.id == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Unknown PossessionType id: " + id);
  }
}

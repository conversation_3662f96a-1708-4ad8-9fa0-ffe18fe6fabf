package com.arealytics.core.enumeration;

public enum ConstructionType implements BaseEnum {
    BRICK(1, "Brick"),
    BRICK_BLOCK(2, "Brick/Block"),
    CONCRETE_POURED(3, "Concrete (Poured)"),
    CONCRETE_BLOCK(4, "Concrete Block"),
    CURTAIN_WALL_MISC_TYPE(5, "Curtain Wall (Misc Type)"),
    FRAME_AND_STUCCO(6, "Frame and Stucco"),
    GLASS(7, "Glass"),
    MASONRY(8, "Masonry"),
    METAL_GENERAL_CASE(9, "Metal (General Case)"),
    METAL_STEEL(23, "Metal/Steel"),
    MIXED(10, "Mixed"),
    OTHER(11, "Other"),
    PRECAST(12, "Precast"),
    PREFAB_LIGHT_STEEL(13, "Prefab Light Steel"),
    REINFORCED_CONCRETE(14, "Reinforced Concrete"),
    STEEL_FRAME(15, "Steel Frame"),
    STEEL_FRAME_GLASS_CURTAIN_WALL(16, "Steel Frame/Glass Curtain Wall"),
    STEEL_FRAME_MASONRY_FACADE(17, "Steel Frame/Masonry Facade"),
    STEEL_FRAME_METAL_SIDING(18, "Steel Frame/Metal Siding"),
    STEEL_FRAME_VINYL_SIDING(19, "Steel Frame/Vinyl Siding"),
    STEEL_FRAMED_GLASS_CURTAIN(24, "Steel Framed Glass Curtain"),
    TILT_UP(20, "Tilt-up"),
    WOOD_FRAME(21, "Wood Frame"),
    WOOD_FRAME_BRICK_FACADE(22, "Wood Frame/Brick Facade");

    private final int id;
    private final String label;

    ConstructionType(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static ConstructionType fromId(int id) {
        for (ConstructionType type : values()) {
            if (type.id == id) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid ConstructionType ID: " + id);
    }
}

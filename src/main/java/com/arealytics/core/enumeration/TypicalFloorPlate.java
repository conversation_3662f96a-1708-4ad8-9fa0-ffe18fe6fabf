package com.arealytics.core.enumeration;


public enum TypicalFloorPlate implements BaseEnum{
    BROKER(1, "Broker"),
    OWNER(2, "Owner"),
    PROPERTYMANAGER(3, "PropertyManager");

    private final int id;
    private final String label;


    TypicalFloorPlate(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static TypicalFloorPlate fromId(int id) {
        for (TypicalFloorPlate typicalFloorPlate : values()) {
            if (typicalFloorPlate.id == id) {
                return typicalFloorPlate;
            }
        }
        throw new IllegalArgumentException("Invalid TypicalFloorPlate ID: " + id);
    }
}

package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum Conveyed implements BaseEnum {
  BUILDING_ONLY(1, "Building Only"),
  LAND_ONLY(2, "Land Only"),
  BUILDING_AND_LAND(3, "Building and Land");

  private final int id;
  private final String label;

  public static Conveyed fromId(int id) {
    for (Conveyed type : values()) {
      if (type.getId() == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid Conveyed ID: " + id);
  }
}

package com.arealytics.core.enumeration;

public enum AuditAction {
    ADD("Add"),
    MOD("Update"),
    DEL("Delete");

    private final String displayName;

    AuditAction(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

   
    public static String mappedValue(String action) {
        for (AuditAction type : AuditAction.values()) {
            if (type.name().equalsIgnoreCase(action)) {
                return type.getDisplayName();
            }
        }
        return action; // Fallback if no match is found
    }
}


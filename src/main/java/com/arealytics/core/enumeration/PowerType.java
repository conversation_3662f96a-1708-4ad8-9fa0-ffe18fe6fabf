package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PowerType implements BaseEnum {
    HIGH_VOLTAGE_POWER_SUPP(1, "HighVoltagePowerSupp"),
    SOLAR_POWER_SYSTEM_INST(2, "SolarPowerSystemInst");

    private final Integer id;
    private final String label;


    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static PowerType fromId(int id) {
        for (PowerType powerType : values()) {
            if (powerType.id == id) {
                return powerType;
            }
        }
        throw new IllegalArgumentException("Invalid powerType ID: " + id);
    }
}

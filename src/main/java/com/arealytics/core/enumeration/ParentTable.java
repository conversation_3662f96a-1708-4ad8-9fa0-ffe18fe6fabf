package com.arealytics.core.enumeration;

public enum ParentTable implements BaseEnum {
    Property(1, "Property"),
    Listing(2, "Listing"),
    Suite(3, "Suite"),
    Parcel(4, "Parcel"),
    Company(5, "Company"),
    Branch(6, "Branch"),
    ContactRole(7, "ContactRole"),
    Person(8, "Person"),
    <PERSON>(10, "Sale"),
    <PERSON><PERSON>(11, "Lease"),
    Address(12, "Address"),
    AdditionalUse(13, "AdditionalUse"),
    AdditionalAddress(14, "AdditionalAddress"),
    PropertyAllocation(15, "PropertyAllocation"),
    SalePriceConfirmation(16, "SalePriceConfirmation"),
    SaleLoanInfo(17, "SaleLoanInfo"),
    SellerContact(18, "SellerContact"),
    BuyerContact(19, "BuyerContact"),
    LevelSpacesBreakdown(20, "LevelSpacesBreakdown"),
    ExtensionsAndOptions(21, "ExtensionsAndOptions"),
    ReviewsAndRentEscalations(22, "ReviewsAndRentEscalations"),
    Media(23, "Media"),
    KeyContact(24, "KeyContact"),
    TenantVerification(25, "TenantVerification"),
    Tenant(26, "Tenant"),
    DashBoard(27, "DashBoard"),
    MarketBrief(28, "MarketBrief"),
    BranchDataFeed(29, "BranchDataFeed"),
    PropertyOwner(30, "PropertyOwner"),
    ProductUpdate(31, "ProductUpdate"),
    ListingVerification(32, "ListingVerification"),
    SuiteVerification(33, "SuiteVerification"),
    LeaseVerification(34, "LeaseVerification"),
    SaleVerification(35, "SaleVerification");

    private final int id;
    private final String label;

    ParentTable(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static ParentTable fromId(int id) {
        for (ParentTable pt : values()) {
            if (pt.id == id) {
                return pt;
            }
        }
        throw new IllegalArgumentException("Invalid ParentTable ID: " + id);
    }
}

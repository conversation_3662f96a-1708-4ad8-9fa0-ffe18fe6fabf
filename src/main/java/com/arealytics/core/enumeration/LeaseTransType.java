package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum LeaseTransType implements BaseEnum {
  ORIGINAL(1, "Original"),
  RENEWAL(2, "Renewal"),
  PRE_COMMIT(4, "Pre-Commit"),
  VARIATION(5, "Variation"),
  TRANSFER(6, "Transfer"),
  PARTIAL_SURRENDER(7, "Partial Surrender"),
  CHANGE_OF_TENANT_NAME(8, "Change of Tenant Name"),
  LEASEHOLD(9, "Leasehold");

  private final int id;
  private final String label;

  public static LeaseTransType fromId(int id) {
    for (LeaseTransType type : values()) {
      if (type.id == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid LeaseTransType id: " + id);
  }
}

package com.arealytics.core.enumeration;

public enum Tenancy implements BaseEnum {
    SINGLE(1, "Single"),
    MULTIPLE(2, "Multiple");

    private final int id;
    private final String label;

    Tenancy(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static Tenancy fromId(int id) {
        for (Tenancy tenancy : values()) {
            if (tenancy.id == id) {
                return tenancy;
            }
        }
        throw new IllegalArgumentException("Invalid Tenancy ID: " + id);
    }
}

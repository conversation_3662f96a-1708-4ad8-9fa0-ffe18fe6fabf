package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum FaceRentPerSqmMethods implements BaseEnum {
  ACTUAL_LEASE(1, "Actual Lease"),
  LISTING(2, "Listing"),
  THIRD_PARTY(3, "Third party"),
  MARKET_METHOD(6, "Market method");

  private final int id;
  private final String label;

  public static FaceRentPerSqmMethods fromId(int id) {
    for (FaceRentPerSqmMethods source : values()) {
      if (source.id == id) {
        return source;
      }
    }
    throw new IllegalArgumentException("Invalid FaceRentPerSqmMethods id: " + id);
  }
}

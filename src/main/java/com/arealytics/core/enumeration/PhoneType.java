package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum PhoneType implements BaseEnum {
  HOME(1, "Home"),
  OFFICE(2, "Office"),
  MOBILE(3, "Mobile"),
  <PERSON>OR<PERSON>(4, "Work"),
  FAX(5, "Fax"),
  PAGER(6, "Pager"),
  DIRECT_WORK_PHONE(7, "Direct Work Phone"),
  EMAIL(8, "Email");

  private final int id;
  private final String label;

  public static PhoneType fromId(int id) {
    for (PhoneType type : values()) {
      if (type.getId() == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid PhoneType ID: " + id);
  }
}

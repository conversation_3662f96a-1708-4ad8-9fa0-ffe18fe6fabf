package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ListingType implements BaseEnum {
  DIRECT(1, "Direct"),
  SUBLEASE(2, "Sublease"),
  EXCLUSIVE(3, "Exclusive"),
  PORTFOLIO_SALE(4, "Portfolio Sale"),
  CO_WORKING(5, "Co-Working");

  private final int id;
  private final String label;

  public static ListingType fromId(int id) {
    for (ListingType type : values()) {
      if (type.id == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("No ListingType with id " + id);
  }
}

package com.arealytics.core.enumeration;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum TSATenantResearchStatus {
    GROUPING_COMPLETED("Grouping Completed"),
    GROUPING_INPROGRESS("Grouping InProgress"),
    NOT_STARTED("Not Started"),
    MERGE_INPROGRESS("Merge InProgress"),
    MERGE_COMPLETED("Merge Completed"),
    STACKING_INPROGRESS("Stacking InProgress"),
    STACKING_COMPLETED("Stacking Completed");

    private final String value;

    TSATenantResearchStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    // Serialize as enum name
    @JsonValue
    public String toJson() {
        return this.name();
    }

    // Deserialize from DB value
    @JsonCreator
    public static TSATenantResearchStatus fromValue(String value) {
        for (TSATenantResearchStatus status : values()) {
            if (status.value.equalsIgnoreCase(value) || status.name().equalsIgnoreCase(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown enum value: " + value);
    }
}

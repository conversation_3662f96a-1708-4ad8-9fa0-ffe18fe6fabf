package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SuiteStatus implements BaseEnum {
  AVAILABLE(1, "Available"),
  OFF_THE_MARKET(2, "Off the Market"),
  LEASED(3, "Leased"),
  DELETED(4, "Deleted"),
  LEASE_PENDING(5, "Lease Pending"),
  MARKET_BRIEF(6, "Market Brief"),
  NEEDS_RESEARCH(7, "Needs Research"),
  EXPIRED(8, "Expired"),
  HIDDEN(9, "Hidden"),
  PENDING(10, "Pending");

  private final int id;
  private final String label;

  public static SuiteStatus fromId(int id) {
    for (SuiteStatus type : values()) {
      if (type.id == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Unknown SuiteStatus id: " + id);
  }
}

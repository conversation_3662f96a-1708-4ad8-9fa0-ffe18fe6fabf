package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum LeaseTermsMethods implements BaseEnum {
  ACTUAL(1, "Actual"),
  THIRD_PARTY(4, "Third-party"),
  MARKET_METHOD(5, "Market method");

  private final int id;
  private final String label;

  public static LeaseTermsMethods fromId(int id) {
    for (LeaseTermsMethods method : values()) {
      if (method.id == id) {
        return method;
      }
    }
    throw new IllegalArgumentException("Invalid LeaseTermsMethods id: " + id);
  }
}

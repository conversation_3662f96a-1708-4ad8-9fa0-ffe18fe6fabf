package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ArmsLengthType implements BaseEnum {
  YES(1, "Yes"),
  NO(2, "No"),
  UNKNOWN(3, "Unknown");

  private final int id;
  private final String label;

  public static ArmsLengthType fromId(int id) {
    for (ArmsLengthType value : values()) {
      if (value.getId() == id) {
        return value;
      }
    }
    throw new IllegalArgumentException("Invalid ArmsLengthType ID: " + id);
  }
}

package com.arealytics.core.enumeration;

public enum HVACType implements BaseEnum {
    CHILLED_WATER(1, "Chilled Water"),
    ELECTRIC_AIR_CONDITIONING(2, "Electric Air Conditioning"),
    ELECTRIC_BASE_BOARD(3, "Electric Base Board"),
    FORCED_AIR_UNITS(6, "Forced Air Units"),
    FORCED_WARM_COLD_AIR_CONDITIONING(7, "Forced Warm/Cold Air Conditioning"),
    HEATING_VENTILATION_AIR_CONDITIONING(10, "Heating Ventilation Air Conditioning"),
    MIXED_TYPES(13, "Mixed Types"),
    NONE(14, "None"),
    OTHER_TYPES(16, "Other Types"),
    REFRIGERATED_AIR_CONDITIONING(18, "Refrigerated Air Conditioning"),
    REZNOR(19, "<PERSON>znor"),
    SPACE_HEATERS(20, "Space Heaters"),
    SUSPENDED_GAS(21, "Suspended Gas"),
    UNKNOWN(22, "Unknown");

    private final int id;
    private final String label;

    HVACType(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static HVACType fromId(int id) {
        for (HVACType type : values()) {
            if (type.id == id) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid HVACType id: " + id);
    }
}

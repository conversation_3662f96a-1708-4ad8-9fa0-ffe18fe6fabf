package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum IncomeClass implements BaseEnum {
  NET_OPERATING_INCOME(1, "Net Operating Income"),
  SCHEDULED_GROSS(2, "Scheduled Gross"),
  SCHEDULED_NET(3, "Scheduled Net"),
  EFFECTIVE_GROSS(4, "Effective Gross");

  private final int id;
  private final String label;

  public static IncomeClass fromId(int id) {
    for (IncomeClass type : values()) {
      if (type.getId() == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid IncomeClass ID: " + id);
  }
}

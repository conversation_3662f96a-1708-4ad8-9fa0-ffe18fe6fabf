package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum NOIMethod implements BaseEnum {
  STATED(1, "Stated"),
  CALCULATED(2, "Calculated");

  private final int id;
  private final String label;

  public static NOIMethod fromId(int id) {
    for (NOIMethod source : values()) {
      if (source.getId() == id) {
        return source;
      }
    }
    throw new IllegalArgumentException("Invalid NOIMethod ID: " + id);
  }
}

package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum MediaSource implements BaseEnum {
  EMPIRICAL_PHOTOGRAPHY(1, "Empirical Photography"),
  STREETVIEW(2, "Streetview"),
  OWNER_WEBSITE(3, "Owner Website"),
  BUILDING_WEBSITE(4, "Building Website"),
  BROKERS_WEBSITE(5, "B<PERSON><PERSON>'s Website"),
  BROKERS_EMAIL(6, "<PERSON><PERSON><PERSON>'s Email"),
  BROCHURE(7, "Brochure"),
  BROKERS_PDF(8, "<PERSON>roke<PERSON>'s PDF"),
  THIRD_PARTY_WEBSITE(9, "Third Party Website");

  private final int id;
  private final String label;

  public static MediaSource fromId(int id) {
    for (MediaSource type : values()) {
      if (type.id == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid MediaSourceType id: " + id);
  }
}

package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum RightsIncludedType implements BaseEnum {
  FREEHOLD(1, "Freehold"),
  LEASEHOLD(2, "Leasehold"),
  OTHER(3, "Other");

  private final int id;
  private final String label;

  public static RightsIncludedType fromId(int id) {
    for (RightsIncludedType type : values()) {
      if (type.getId() == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid RightsIncludedType ID: " + id);
  }
}

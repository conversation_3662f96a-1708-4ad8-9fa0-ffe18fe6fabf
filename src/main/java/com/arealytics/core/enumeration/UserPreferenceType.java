package com.arealytics.core.enumeration;

import java.util.Arrays;
import java.util.Optional;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UserPreferenceType {
  DOWNLOAD("download"),
  CUSTOMIZE("Customize"),
  MAIL_PREFERENCES("MailPreferences");

  private final String label;

  public String getLabel() {
    return label;
  }

  public static Optional<UserPreferenceType> fromLabel(String label) {
    return Arrays.stream(UserPreferenceType.values())
        .filter(screen -> screen.label.equalsIgnoreCase(label))
        .findFirst();
  }
}

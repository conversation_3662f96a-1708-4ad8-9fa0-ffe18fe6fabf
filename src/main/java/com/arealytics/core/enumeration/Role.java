package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum Role implements BaseEnum {
  CEO(1, "CEO"),
  INTERNAL(2, "Internal"),
  LISTING_AGENT(4, "Listing Agent"),
  BRANCH_SUPER_USER(5, "Branch Super User"),
  METRO_SUPER_USER(6, "Metro Super User"),
  COMPANY_SUPER_USER(7, "Company Super User"),
  LEASE_EXCHANGE(8, "Lease Exchange"),
  DATA_FEED(9, "Data Feed"),
  RESEARCH_ANALYST(10, "Research Analyst"),
  CUSTOMER_SUPPORT(11, "Customer Support"),
  ADMINISTRATION(12, "Administration"),
  AUDITOR(13, "Auditor");

  private final int id;
  private final String label;

  public static Role fromId(int id) {
    for (Role role : values()) {
      if (role.id == id) {
        return role;
      }
    }
    throw new IllegalArgumentException("No role with id " + id);
  }
}

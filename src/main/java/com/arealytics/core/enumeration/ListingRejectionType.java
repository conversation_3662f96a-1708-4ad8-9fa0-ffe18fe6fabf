package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ListingRejectionType implements BaseEnum{
    RESIDENTIAL(1, "Residential"),
    CAR_PARKING(2, "Car parking"),
    FARM_LAND(3, "Farm land"),
    OTHER_NON_COMMERCIAL(4, "Other non-commercial"),
    NO_LONGER_PUBLISHED(5, "No longer published listing"),
    UNABLE_TO_CONFIRM_LOCATION(6, "Unable to confirm location"),
    OTHER(7, "Other");

    private final int id;
    private final String label;

    public static ListingRejectionType fromId(int id) {
        for (ListingRejectionType type : values()) {
            if (type.id == id)
                return type;
        }
        throw new IllegalArgumentException("Invalid ListingRejectionType id: " + id);
    }
}

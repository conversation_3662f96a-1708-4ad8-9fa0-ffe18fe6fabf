package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum CashFlowType implements BaseEnum {
  ACTUAL_AT_TIME_OF_SALE(1, "Actual at Time Of Sale"),
  NOT_APPLICABLE_OR_AVAILABLE(2, "Not Applicable/Not Available"),
  PREVIOUS_CALENDAR_YEAR(3, "Previous Calendar Year"),
  PROFORMA(4, "Proforma"),
  TRAILING_TWELVE_MONTHS(5, "Trailing 12 Months");

  private final int id;
  private final String label;

  public static CashFlowType fromId(int id) {
    for (CashFlowType type : values()) {
      if (type.getId() == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid CashFlowType ID: " + id);
  }
}

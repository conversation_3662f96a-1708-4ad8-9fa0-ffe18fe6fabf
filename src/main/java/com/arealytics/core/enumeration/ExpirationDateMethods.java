package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ExpirationDateMethods implements BaseEnum {
  ACTUAL(1, "Actual"),
  LISTING(2, "Listing"),
  MARKET_METHOD(5, "Market method"),
  LEASE_TERM_AND_THIRD_PARTY(6, "From Lease Term and Third Party");

  private final int id;
  private final String label;

  public static ExpirationDateMethods fromId(int id) {
    for (ExpirationDateMethods approach : values()) {
      if (approach.id == id) {
        return approach;
      }
    }
    throw new IllegalArgumentException("Invalid ExpirationDateMethods id: " + id);
  }
}

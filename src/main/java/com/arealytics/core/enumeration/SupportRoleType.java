package com.arealytics.core.enumeration;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SupportRoleType implements BaseEnum{
    RESEARCHER(1, "Researcher"),
    SALES_REP(2, "Sales Rep"),
    SUPPORT_AGENT(3, "Support Agent");

    private final int id;
    private final String label;

    public static SupportRoleType fromId(int id) {
        for (SupportRoleType type : values()) {
            if (type.id == id)
                return type;
        }
        throw new IllegalArgumentException("Invalid SupportRoleType id: " + id);
    }
}

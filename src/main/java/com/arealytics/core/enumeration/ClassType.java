package com.arealytics.core.enumeration;

public enum ClassType implements BaseEnum {
    A(1, "A", 5),
    B(2, "B", 5),
    C(3, "C", 5),
    PRE(4, "Pre", 5),
    PRIME(5, "Prime", 3),
    SECONDARY(6, "Secondary", 3);

    private final int id;
    private final String label;
    private final int UseTypeID;

    ClassType(int id, String label, int UseTypeID) {
        this.id = id;
        this.label = label;
        this.UseTypeID = UseTypeID;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public int getUseTypeID() {
        return UseTypeID;
    }

    public static ClassType fromId(int id) {
        for (ClassType type : values()) {
            if (type.id == id) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid ClassType ID: " + id);
    }
}

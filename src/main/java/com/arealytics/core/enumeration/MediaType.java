package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum MediaType implements BaseEnum {
  ARTIST_DRAWING(1, "Artist Drawing"),
  FLYER_BROCHURE(2, "Flyer/Brochure"),
  BUILDING_IMAGE(3, "Building Image"),
  STATISTICS_ANALYTICS(4, "Statistics/Analytics"),
  LEGAL_DOCS(5, "Legal Docs"),
  AERIAL_IMAGERY(6, "Aerial Imagery"),
  PROPERTY_MANAGER(7, "Property Manager"),
  LAND_LOT_IMAGE(8, "Land/Lot Image"),
  NEWS_PUBLICATION(9, "News Publication"),
  INTERIOR(10, "Interior"),
  OBLIQUE_AERIAL(11, "Oblique Aerial"),
  TITLE(12, "Title"),
  LISTING_SIGN(13, "Listing Sign"),
  SUITE_FLOOR_PLAN(14, "Suite Floor Plan"),
  TENANT_ROSTER(15, "Tenant Roster"),
  <PERSON><PERSON>ER_MEDIA(16, "Other Media"),
  SITE_PLAN(17, "Site Plan"),
  PROPERTY_FLOOR_PLAN(18, "Property Floor Plan"),
  SIGNAGE(19, "Signage"),
  REGISTERED_LEASE(20, "Registered Lease"),
  REGISTERED_SUBLEASE(21, "Registered Sublease"),
  PARCEL_PLAT(22, "Parcel/Plat"),
  LEASE_FOLIO(23, "Lease Folio"),
  MAP_IMAGE(24, "Map Image");

  private final int id;
  private final String label;

  public static MediaType fromId(int id) {
    for (MediaType type : values()) {
      if (type.getId() == id)
        return type;
    }
    throw new IllegalArgumentException("Invalid MediaType ID: " + id);
  }
}

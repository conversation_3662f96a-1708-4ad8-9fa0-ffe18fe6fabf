package com.arealytics.core.enumeration;

public enum AmenitiesType implements  BaseEnum{
    BATHROOM_AMENITIES(1, "Bathroom Amenities"),
    BOOKABLE_PARKING_BAYS(2, "Bookable Parking Bays"),
    CAR_SHARE_SERVICES(3, "Car Share Services"),
    CHILDCARE_CENTER(4, "Childcare Center"),
    CLOSE_BY_CAFE(5, "Close By Cafe"),
    CLOSE_BY_RESTAURANTS_BARS(6, "Close By Restaurants/Bars"),
    CLOSE_BY_RETAIL(7, "Close By Retail"),
    CLOSE_BY_TRANSPORT_LINKS(8, "Close By Transport Links"),
    COMMUNAL_WORKSPACE(9, "Communal/collaboration workspace"),
    CONCIERGE(10, "Concierge"),
    DISABLED_AMENITIES(11, "Disabled Amenities"),
    EV_CHARGING_STATIONS(12, "Electric Vehicle Charging Stations"),
    EMPLOYEE_HEALTH_OFFERS(13, "Employee Health Offers"),
    END_OF_TRIP(14, "End Of Trip (Showers, Bike Storage, Lockers)"),
    FUNCTION_ROOMS(15, "Function/Conference Rooms"),
    KITCHEN_AMENITIES(16, "Kitchen Amentities"),
    LANDSCAPED_GARDENS(17, "Landscaped Gardens"),
    OFF_SITE_PARKING(18, "Off-site Parking"),
    ON_SITE_BUILDING_MANAGEMENT(19, "On-site Building Management"),
    ON_SITE_CAFE(20, "On-site Cafe"),
    ON_SITE_GYM(21, "On-site Gym/Fitness Facilities"),
    ON_SITE_PARKING(22, "On-site Parking"),
    ON_SITE_RESTAURANTS_BAR(23, "On-site Restaurants/Bar"),
    ON_SITE_RETAIL(24, "On-site Retail"),
    ON_SITE_SECURITY(25, "On-site Security"),
    ROOFTOP_TERRACE(26, "Rooftop Terrace"),
    TAXI_STAND(27, "Taxi Stand"),
    WELLNESS_CENTRE(28, "Wellness Centre"),
    WHEELCHAIR_ACCESSIBILITY(29, "Wheelchair Accessibility");

    private final int id;
    private final String label;

    AmenitiesType(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static AmenitiesType fromId(int id) {
        for (AmenitiesType type : values()) {
            if (type.id == id) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid BuildSpecStatus ID: " + id);
    }
}

package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum AlertScheduleType implements BaseEnum {
    DAILY(1, "Daily"),
    WEEKLY(2, "Weekly"),
    NO_ALERTS(3, "No Alerts"),
    MONTHLY(4,"Monthly");

    private final int id;
    private final String label;

    public static AlertScheduleType fromId(int id) {
        for (AlertScheduleType type : values()) {
            if (type.id == id) {
                return type;
            }
        }
        throw new IllegalArgumentException("No AlertScheduleType with id " + id);
    }
}

package com.arealytics.core.enumeration;

import org.springframework.http.HttpStatus;

public enum StatusCode {
    SUCCESS("200", HttpStatus.OK),
    CREATED("201", HttpStatus.CREATED),
    ACCEPTED("202", HttpStatus.ACCEPTED),
    NO_CONTENT("204", HttpStatus.NO_CONTENT),
    BAD_REQUEST("400", HttpStatus.BAD_REQUEST),
    UNAUTHORIZED_ACCESS("401", HttpStatus.UNAUTHORIZED),
    FORBIDDEN("403", HttpStatus.FORBIDDEN),
    RESOURCE_NOT_FOUND("404", HttpStatus.NOT_FOUND),
    INTERNAL_SERVER_ERROR("500", HttpStatus.INTERNAL_SERVER_ERROR);

    private final String code;
    private final HttpStatus status;

    StatusCode(String code, HttpStatus status) {
        this.code = code;
        this.status = status;
    }

    public String getCode() {
        return code;
    }

    public HttpStatus getStatus() {
        return status;
    }
}

package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum LeaseResearchStatus implements BaseEnum {
  NEEDS_RESEARCH(1, "Needs Research"),
  NO_ISSUES(2, "No Issues");

  private final int id;
  private final String label;

  public static LeaseResearchStatus fromId(int id) {
    for (LeaseResearchStatus status : values()) {
      if (status.id == id) {
        return status;
      }
    }
    throw new IllegalArgumentException("Invalid LeaseResearchStatus id: " + id);
  }
}

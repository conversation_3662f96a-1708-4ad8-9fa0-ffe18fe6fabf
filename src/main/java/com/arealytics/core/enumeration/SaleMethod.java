package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum SaleMethod implements BaseEnum {
  LISTING(1, "Listing"),
  AUCTION(2, "Auction"),
  EXPRESSION_OF_INTEREST(3, "Expression of Interest"),
  OFF_MARKET(4, "Off Market"),
  OTHER(5, "Other"),
  UNKNOWN(6, "Unknown");

  private final int id;
  private final String label;

  public static SaleMethod fromId(int id) {
    for (SaleMethod method : values()) {
      if (method.getId() == id) {
        return method;
      }
    }
    throw new IllegalArgumentException("Invalid SaleMethod ID: " + id);
  }
}

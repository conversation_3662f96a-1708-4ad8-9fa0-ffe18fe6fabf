package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MediaRelationType implements BaseEnum {
    PROPERTY(1, "Property"),
    LISTING(2, "Listing"),
    SUITE(3, "Suite"),
    <PERSON>LE(4, "Sale"),
    COMPANY(5, "Company"),
    BRANCH(6, "Branch"),
    PERSON(7, "Person"),
    ENTITY(8, "Entity"),
    ALL_MEDIA(9, "All Media"),
    PROPERTY_CHANGE_LOG(10, "PropertyChangeLog"),
    LEASE(11, "Lease"),
    MEDIA_SUPPORT_DOCS(12, "MediaSupportDocs"),
    MARKET_BRIEF(13, "MarketBrief");

    private final Integer id;
    private final String label;

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static MediaRelationType fromId(int id) {
        for (MediaRelationType type : values()) {
            if (type.id == id) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid MediaRelationType ID: " + id);
    }
}

package com.arealytics.core.enumeration;

public enum GreenStarRating implements BaseEnum {
    ZERO(1, "0"),
    ONE(2, "1"),
    TWO(3, "2"),
    THREE(4, "3"),
    FOUR(5, "4"),
    FIVE(6, "5"),
    SIX(7, "6");

    private final int id;
    private final String label;

    GreenStarRating(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static GreenStarRating fromId(int id) {
        for (GreenStarRating rating : values()) {
            if (rating.id == id) {
                return rating;
            }
        }
        throw new IllegalArgumentException("Invalid GreenStarRating ID: " + id);
    }
}

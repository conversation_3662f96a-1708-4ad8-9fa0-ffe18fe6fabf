package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum Action implements BaseEnum{
    ADD(1, "Add"),
    UPDATE(2, "Update"),
    DELETE(3, "Delete");

    private final int id;
    private final String label;

    public static Action fromId(int id) {
        for (Action type : values()) {
            if (type.id == id) return type;
        }
        throw new IllegalArgumentException("Invalid Action id: " + id);
    }
}

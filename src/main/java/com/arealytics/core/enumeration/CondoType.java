package com.arealytics.core.enumeration;

public enum CondoType implements BaseEnum {
    NOT_STRATA(1, "Not Strata", "Freehold"),
    STRATA(2, "Strata","Strata"),
    MASTER_STRATA_RECORD(3, "Master Strata Record","Master Strata"),
    MASTER_FREEHOLD(4, "Master Freehold","Complex"),
    CHILD_FREEHOLD(5, "Child Freehold","Child Building");

    private final int id;
    private final String label;
    private final String displayText;

    CondoType(int id, String label, String displayText) {
        this.id = id;
        this.label = label;
        this.displayText = displayText;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public String getDisplayText() {
        return displayText;
    }

    public static CondoType fromId(int id) {
        for (CondoType type : values()) {
            if (type.id == id) {
                return type;
            }
        }
        return null;
    }
}


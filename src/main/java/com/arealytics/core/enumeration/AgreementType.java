package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum AgreementType implements BaseEnum {
    CONJUNCTION(1, "Conjunction"),
    EXCLUSIVE(3, "Exclusive"),
    OPEN(4, "Open");

    private final int id;
    private final String label;

    public static AgreementType fromId(int id) {
        for (AgreementType type : values()) {
            if (type.id == id) {
                return type;
            }
        }
        throw new IllegalArgumentException("No AgreementType with id " + id);
    }
}

package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum CapRateSource implements BaseEnum {
  REGULATORY_FILING(1, "RegulatoryFiling"),
  LISTING(2, "Listing"),
  LISTING_AGENT(3, "Listing Agent"),
  PROPERTY_OWNER(4, "Property Owner"),
  NEWS_ARTICLE(5, "News Article"),
  BUYER_AGENT(6, "Buyer Agent"),
  SELLER(7, "Seller"),
  BUYER_REP(8, "Buyer Rep"),
  SELLER_AGENT(9, "Seller Agent");

  private final int id;
  private final String label;

  public static CapRateSource fromId(int id) {
    for (CapRateSource type : values()) {
      if (type.getId() == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid CapRateSource ID: " + id);
  }
}

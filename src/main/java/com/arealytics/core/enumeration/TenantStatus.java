package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum TenantStatus implements BaseEnum {
  MOVED_IN(1, "Moved In"),
  MOVED_OUT(2, "Moved Out"),
  MOVE_IN_PENDING(3, "Move In - Pending"),
  MOVE_OUT_PENDING(4, "Move Out - Pending"),
  MOVE_OUT_FUTURE(5, "Move Out - Future"),
  MOVE_IN_FUTURE(6, "Move In - Future");

  private final int id;
  private final String label;

  public static TenantStatus fromId(int id) {
    for (TenantStatus status : values()) {
      if (status.id == id) {
        return status;
      }
    }
    throw new IllegalArgumentException("Invalid TenantStatus id: " + id);
  }
}

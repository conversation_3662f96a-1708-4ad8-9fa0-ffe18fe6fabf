package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UseType implements BaseEnum {
    OFFICE(5, "Office"),
    INDUSTRIAL(3, "Industrial"),
    RETAIL(2, "Retail"),
    APARTMENTS(4, "Apartments"),
    SPECIAL_USE(9, "Special Use"),
    LAND(7, "Land");

    private final Integer id;
    private final String label;

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static UseType fromId(int id) {
        for (UseType pt : values()) {
            if (pt.id == id) {
                return pt;
            }
        }
        throw new IllegalArgumentException("Invalid UseType ID: " + id);
    }
}

package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum StartingRateMethods implements BaseEnum {
  ACTUAL_LEASE(1, "Actual Lease"),
  LISTING(2, "Listing"),
  THIRD_PARTY(3, "Third party"),
  MARKET_METHOD(6, "Market method");

  private final int id;
  private final String label;

  public static StartingRateMethods fromId(int id) {
    for (StartingRateMethods source : values()) {
      if (source.id == id) {
        return source;
      }
    }
    throw new IllegalArgumentException("Invalid StartingRateMethods id: " + id);
  }
}

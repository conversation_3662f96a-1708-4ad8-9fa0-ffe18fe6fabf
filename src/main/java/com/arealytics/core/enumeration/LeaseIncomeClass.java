package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum LeaseIncomeClass implements BaseEnum {
  GROSS_LEASE_ACTUAL(1, "Gross Lease Actual"),
  NET_LEASE_ACTUAL(2, "Net Lease Actual"),
  GROSS_POTENTIAL_RENT(3, "Gross Potential Rent"),
  NET_POTENTIAL_RENT(4, "Net Potential Rent");

  private final int id;
  private final String label;

  public static LeaseIncomeClass fromId(int id) {
    for (LeaseIncomeClass type : values()) {
      if (type.getId() == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid LeaseIncomeClass ID: " + id);
  }
}

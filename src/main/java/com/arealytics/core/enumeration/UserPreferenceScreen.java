package com.arealytics.core.enumeration;

import java.util.Arrays;
import java.util.Optional;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UserPreferenceScreen {
  TRANSACTION("Transaction"),
  REQUIRED_PREFERENCE("RequiredPreference"),
  TENANT("Tenant"),
  REPORT_PREFERENCES("ReportPreferences"),
  LEASE_TRANSACTION("Lease Transaction"),
  DASHBOARD_ACTIVITY("DashboardActivity"),
  PROPERTY("Property"),
  MARKET_BRIEF_MESSAGE_PREFERENCES("MarketBriefMessagePreferences"),
  MARKET_BRIEF_PREFERENCES("MarketBriefPreferences"),
  MARKET_BRIEF_SUITE("MarketBriefSuite"),
  MARKET_BRIEF_TOUR("MarketBriefTour"),
  COMMERCIAL_AU_MARKET_BRIEF_RESPONSE("CommercialAUMarketBriefResponse"),
  RECENTLY_ADDED_LISTING("RecentlyAddedListing"),
  RECENT_LISTING_ACTIVITY("RecentListingActivity"),
  PURCHASE_LOG("PurchaseLog"),
  EXECUTION_DETAILS("ExecutionDetails"),
  SUITES("Suites");

  private final String label;

  public String getLabel() {
    return label;
  }

  public static Optional<UserPreferenceScreen> fromLabel(String label) {
    return Arrays.stream(UserPreferenceScreen.values())
        .filter(type -> type.label.equalsIgnoreCase(label))
        .findFirst();
  }
}

package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum LeaseType implements BaseEnum {
    GROSS(4, "Gross"),
    SEMI_GROSS(17, "Semi-Gross"),
    NET(10, "Net"),
    OTHER(13, "Other");

    private final int id;
    private final String label;

    public static LeaseType fromId(int id) {
        for (LeaseType type : values()) {
            if (type.id == id)
                return type;
        }
        throw new IllegalArgumentException("Invalid LeaseType id: " + id);
    }
}

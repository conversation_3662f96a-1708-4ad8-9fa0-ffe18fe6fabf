package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SpecificUseType implements BaseEnum {
    ANCHORED_STRIP_CENTER(1, "Anchored Strip Center"),
    AUTOMOTIVE(2, "Automotive"),
    COMMUNITY_CENTER(3, "Community Center"),
    CONVENIENCE_STRIP_CENTER(4, "Convenience/Strip Center"),
    FASHION_SPECIALTY_CENTER(5, "Fashion Specialty Center"),
    FREESTANDING_BANK(6, "Freestanding-Bank"),
    FREESTANDING_BIG_BOX(7, "Freestanding-Big Box"),
    FREESTANDING_OTHER(8, "Freestanding-Other"),
    SERVICE_STATION(9, "Service Station"),
    STRIP_RETAIL(10, "Strip Retail"),
    LIFESTYLE_CENTER(11, "Lifestyle Center"),
    NEIGHBOURHOOD_CENTRE(12, "Neighbourhood Centre"),
    FACTORY_OUTLET_CENTRE(13, "Factory Outlet Centre"),
    POWER_CENTRE(14, "Power Centre"),
    REGIONAL_CENTRE(15, "Regional Centre"),
    RESTAURANT_FAST_FOOD(16, "Restaurant-Fast Food"),
    RESTAURANT_FULL_SERVICE(17, "Restaurant-Full Service"),
    SUPER_REGIONAL_CENTRE(18, "Super Regional Centre"),
    THEME_FESTIVAL_CENTRE(19, "Theme/Festival Centre"),
    CREATIVE_LOFT(20, "Creative/Loft"),
    GENERAL_PURPOSE(21, "General Purpose"),
    GOVERNMENT(22, "Government"),
    MEDICAL(23, "Medical"),
    FLEX_RND(24, "Flex R&D"),
    INCUBATOR(25, "Incubator"),
    LIGHT_INDUSTRIAL(26, "Light Industrial"),
    HEAVY_MANUFACTURING(27, "Heavy Manufacturing"),
    TRUCK_TERMINAL(28, "Truck Terminal"),
    WAREHOUSE_BULK(29, "Warehouse-Bulk"),
    WAREHOUSE_DISTRIBUTION(30, "Warehouse-Distribution"),
    WAREHOUSE_FREEZER_COOLER(31, "Warehouse-Freezer/Cooler"),
    WAREHOUSE_UNDERGROUND(32, "Warehouse-Underground"),
    HOTEL_MOTEL(33, "Hotel/Motel"),
    SELF_STORAGE(34, "Self-Storage"),
    AIRPARK(35, "Airpark"),
    AUDITORIUM(36, "Auditorium"),
    BOAT_STORAGE(37, "Boat Storage"),
    BOWLING_ALLEY(38, "Bowling Alley"),
    CHURCH(39, "Church"),
    CLINIC(40, "Clinic"),
    COMMUTER_TERMINAL(41, "Commuter Terminal"),
    DAY_CARE(42, "Day Care"),
    FELLOWSHIP_HALL(43, "Fellowship Hall"),
    FITNESS_CENTER(44, "Fitness Center"),
    FRATERNAL_UNION_FELLOWSHIP_HALL(45, "Fraternal/Union/Fellowship Hall"),
    FUNERAL_HOME(46, "Funeral Home"),
    GO_KART_TRACK(47, "Go Kart Track"),
    GOLF_COURSE(48, "Golf Course"),
    HOSPITAL(49, "Hospital"),
    LUMBER_YARD(50, "Lumber Yard"),
    NURSERY_GREENHOUSE(51, "Nursery/Greenhouse"),
    NURSING_HOME(52, "Nursing Home"),
    PARKING_LOT_GARAGE(53, "Parking Lot/Garage"),
    POST_OFFICE(54, "Post Office"),
    PRISON(55, "Prison"),
    RADIO_TV_STATION(56, "Radio/TV Station"),
    RECREATIONAL_COMPLEX(57, "Recreational Complex"),
    SCHOOL(58, "School"),
    SKATING_RINK(59, "Skating Rink"),
    ATHLETICS_FACILITY(60, "Athletics Facility"),
    THEATER(61, "Theater"),
    WEDDING_CHAPEL(62, "Wedding Chapel"),
    EVENT_CENTRE(63, "Event Centre"),
    BED_AND_BREAKFAST(64, "Bed And Breakfast"),
    RANCH(65, "Ranch"),
    RESORT(66, "Resort"),
    CASINO_GAMING(67, "Casino/Gaming"),
    BAR_NIGHTCLUB(68, "Bar/Nightclub"),
    DATA_CENTRE(69, "Data Centre"),
    CAR_WASH(70, "Car Wash"),
    ELABORATE_RESIDENTIAL(71, "Elaborate Residential"),
    SALVAGE_YARD(72, "Salvage Yard"),
    ASSISTED_LIVING_FACILITY(73, "Assisted Living Facility"),
    MUSEUM(74, "Museum"),
    AUTO_DEALERSHIP(75, "Auto Dealership"),
    FIRE_STATION(76, "Fire Station"),
    MARINA(77, "Marina"),
    AIRPORT_HANGAR(78, "Airport Hangar"),
    OTHERS(79, "Others"),
    CELLULAR_TOWER(80, "Cellular Tower"),
    SHOOTING_RANGE(81, "Shooting Range"),
    CAMP_SITE(82, "Camp Site"),
    VETERINARY_CLINIC(83, "Veterinary Clinic"),
    LABORATORY(84, "Laboratory"),
    FUEL_STORAGE(85, "Fuel Storage"),
    CONDOMINIUM_STRATA(86, "Condominium/Strata"),
    DUPLEX(87, "Duplex"),
    FOURPLEX(88, "Fourplex"),
    GARDEN_APARTMENT(89, "Garden Apartment"),
    LOFT(90, "Loft"),
    MID_HIGH_RISE_APARTMENT(91, "Mid/High Rise Apartment"),
    MOBILE_HOME_PARKS(92, "Mobile Home Parks"),
    TRIPLEX(93, "Triplex"),
    TOWNHOUSE(94, "Townhouse"),
    WALK_UP_APARTMENT(95, "Walk Up Apartment"),
    MIXED_USE_1(96, "Mixed Use"),
    OTHER(97, "Other"),
    INDUSTRIAL(98, "Industrial"),
    MUH(99, "MUH"),
    MIXED_USE_2(100, "Mixed Use"),
    OFFICE(101, "Office"),
    RETAIL(102, "Retail"),
    SPECIAL_USE(103, "Special Use"),
    SHOWROOM_BULKY_GOODS(104, "Showroom/Bulky Goods"),
    GENERAL_WAREHOUSING_BULK(105, "General Warehousing - Bulk"),
    GENERAL_WAREHOUSING_DISTRIBUTION_CROSS_DOCK(106, "General Warehousing - Distribution/Cross Dock"),
    MULTI_UNIT_INDUSTRIAL_SMALL(107, "Multi-Unit Industrial - Small"),
    MULTI_UNIT_INDUSTRIAL_LARGE(108, "Multi-Unit Industrial - Large"),
    MULTI_UNIT_INDUSTRIAL_BUSINESS_PARK(109, "Multi-Unit Industrial - Business Park"),
    HEAVY_INDUSTRIAL_MANUFACTURING(110, "Heavy Industrial / Manufacturing"),
    GENERAL_WAREHOUSING_TEMP_CONTROLLED(111, "General Warehousing - Temperature Controlled"),
    GENERAL_WAREHOUSING_HIGH_BAY(112, "General Warehousing - High Bay"),
    FLEX_RND_HI_TECH(113, "Flex/R&D/Hi-Tech"),
    CONTAINER_PARK(114, "Container Park"),
    CAR_STORAGE(115, "Car Storage"),
    FREEHOLD_UNITS(116, "Freehold Units"),
    STRATA(117, "Strata"),
    AIRPORT_RETAIL(118, "Airport Retail"),
    CITY_CENTRE(119, "City Centre"),
    MIXED_USE_3(120, "Mixed Use"),
    PAD_SITE(121, "Pad Site"),
    RESTAURANT_CAFE(122, "Restaurant/Café"),
    SUB_REGIONAL_CENTRE(123, "Sub-regional Centre"),
    MULTI_TENANT_SMALL_FORMAT(124, "Multi-tenant/Small Format"),
    UNIVERSITY_RETAIL(125, "University Retail"),
    URBAN_TRANSIT_RETAIL(126, "Urban Transit Retail"),
    MAJOR_REGIONAL_CENTRE(127, "Major Regional Centre");

    private final Integer id;
    private final String label;

    @Override
    public int getId() {
        return id;
    }

    public static SpecificUseType fromId(int id) {
        for (SpecificUseType type : values()) {
            if (type.getId() == id) return type;
        }
        throw new IllegalArgumentException("Invalid SpecificUseType ID: " + id);
    }
}

package com.arealytics.core.enumeration;

public enum ConstructionStatus implements BaseEnum {
    PLANNED(1, "Planned"),
    UNDER_CONSTRUCTION(2, "Under Construction"),
    UNDER_RENOVATION(3, "Under Renovation"),
    COMPLETED(4, "Completed"),
    PROPOSED(5, "Proposed"),
    TBD(6, "TBD"),
    DEMOLISHED(7, "Demolished"),
    OBSOLETE(8, "Obsolete"),
    EXISTING(9, "Existing");

    private final int id;
    private final String label;

    ConstructionStatus(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static ConstructionStatus fromId(int id) {
        for (ConstructionStatus status : values()) {
            if (status.id == id) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid ConstructionStatus ID: " + id);
    }
}

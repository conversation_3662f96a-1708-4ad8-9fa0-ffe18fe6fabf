package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum InstrumentDocType implements BaseEnum {
  WARRANTY_DEED(1, "Warranty Deed"),
  QUIT_CLAIM_DEED(2, "Quit Claim Deed"),
  OTHER(3, "Other");

  private final int id;
  private final String label;

  public static InstrumentDocType fromId(int id) {
    for (InstrumentDocType type : values()) {
      if (type.getId() == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid InstrumentDocType ID: " + id);
  }
}

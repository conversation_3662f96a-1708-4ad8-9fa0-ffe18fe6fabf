package com.arealytics.core.enumeration;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum LTSLeaseResearchStatus {
    GROUPING_COMPLETED("Grouping Completed"),
    GROUPING_INPROGRESS("Grouping InProgress"),
    NOT_STARTED("Not Started"),
    MERGE_COMPLETED("Merge Completed"),
    LINKED_TO_TENANT("Linked To Tenant"),
    MERGE_INPROGRESS("Merge InProgress"),
    LINKED_TO_TENANT_INPROGRESS("Linked To Tenant InProgress");

    private final String value;

    LTSLeaseResearchStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @JsonValue
    public String toJson() {
        return this.name();
    }

    @JsonCreator
    public static LTSLeaseResearchStatus fromValue(String value) {
        for (LTSLeaseResearchStatus status : values()) {
            if (status.value.equalsIgnoreCase(value) || status.name().equalsIgnoreCase(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown enum value: " + value);
    }
}

package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FloorStatus implements BaseEnum {
    FULL_FLOOR(1, "Full Floor"), PARTIAL_FLOOR(2, "Partial Floor");

    private final int id;
    private final String label;

    public static FloorStatus fromId(int id) {
        for (FloorStatus status : values()) {
            if (status.id == id) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown FloorStatus id: " + id);
    }
}

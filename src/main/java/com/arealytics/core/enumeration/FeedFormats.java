package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum FeedFormats implements BaseEnum {
    REAXML(1, "ReaXML"),
    INTERNAL(4, "Internal");

    private final int id;
    private final String label;

    public static FeedFormats fromId(int id) {
        for (FeedFormats type : values()) {
            if (type.id == id)
                return type;
        }
        throw new IllegalArgumentException("Invalid FeedFormats id: " + id);
    }
}

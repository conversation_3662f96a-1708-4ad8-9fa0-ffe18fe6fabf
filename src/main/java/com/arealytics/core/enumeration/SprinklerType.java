package com.arealytics.core.enumeration;

public enum SprinklerType implements BaseEnum {
    ESFR(1, "ESFR"),
    FINISHED_SPACE_ONLY(2, "Finished Space Only"),
    DRY(3, "Dry"),
    <PERSON>ET(4, "Wet"),
    NO_SPRINKLER(5, "No Sprinkler"),
    UNKNOWN(6, "Unknown");

    private final int id;
    private final String label;

    SprinklerType(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static SprinklerType fromId(int id) {
        for (SprinklerType type : values()) {
            if (type.id == id) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid SprinklerType ID: " + id);
    }
}

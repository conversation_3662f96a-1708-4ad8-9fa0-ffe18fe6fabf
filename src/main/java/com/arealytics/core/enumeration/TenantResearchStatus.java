package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TenantResearchStatus implements BaseEnum {
  UNCONFIRMED(1, "Unconfirmed"),
  CONFIRMED(2, "Confirmed"),
  OUT_OF_BUSINESS(3, "Out of Business"),
  MOVED_ADDRESS_KNOWN(4, "Moved - Address Known"),
  MOVED_ADDRESS_UNKNOWN(5, "Moved - Address Unknown"),
  NOT_IN_ROSTER(6, "Not In Roster");

  private final int id;
  private final String label;

  public static TenantResearchStatus fromId(int id) {
    for (TenantResearchStatus type : values()) {
      if (type.id == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Unknown TenantResearchStatus id: " + id);
  }

}

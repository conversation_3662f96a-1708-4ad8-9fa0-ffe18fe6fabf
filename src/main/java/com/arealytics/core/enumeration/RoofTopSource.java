package com.arealytics.core.enumeration;

public enum RoofTopSource implements BaseEnum {
    MAPPING_PROVIDER(1, "Mapping Provider"),
    RESEARCHER(2, "Researcher");

    private final int id;
    private final String label;

    RoofTopSource(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static RoofTopSource fromId(int id) {
        for (RoofTopSource source : values()) {
            if (source.id == id) {
                return source;
            }
        }
        throw new IllegalArgumentException("Invalid RoofTopSource id: " + id);
    }
}

package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SaleType implements BaseEnum {
    INVESTMENT(3, "Investment"),
    OWNER_OCCUPIED(6, "Owner Occupied"),
    VACANT_POSSESSION(8, "Vacant Possession"),
    TRANSFER_OF_OWNERSHIP(1, "Transfer of Ownership");

    private final int id;
    private final String label;

    public static SaleType fromId(int id) {
        for (SaleType type : values()) {
            if (type.id == id)
                return type;
        }
        throw new IllegalArgumentException("Invalid SaleType id: " + id);
    }
}

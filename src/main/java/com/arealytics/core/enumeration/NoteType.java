package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum NoteType implements BaseEnum{
    NOTE(1, "Note"),
    MEETING(2, "Meeting"),
    RFI(3, "RFI"),
    RFP(4, "RFP"),
    PRESENTATION(5, "Presentation"),
    RECEIVED_EXCLUSIVE(6, "Received Exclusive"),
    COMPLETED_LEASE(7, "Completed Lease"),
    COMPLETED_SALE(8, "Completed Sale"),
    EMAIL(9, "Email"),
    SUITE_TRANSACTION_NOTE(10, "Suite Transaction Note"),
    SUITE_VERIFICATION_NOTE(11, "Suite Verification Note"),
    LEASE_COMP_TRANSACTION_NOTES(12, "Lease comp transaction notes"),
    LEASE_COMP_VERIFICATION_NOTES(13, "Lease comp verification notes"),
    LEASE_COMP_SOURCE_COMMENT_NOTES(14, "Lease comp source comment notes"),
    COMPANY_RELATED_TO(15, "Company Related To"),
    LEASE_CREDIT_ADJUSTMENT_NOTES(16, "Lease credit adjustment notes"),
    UNDER_RENOVATION(17, "Under Renovation"),
    UNDER_CONSTRUCTION(18, "Under Construction"),
    BUILDING_DEMOLISHED(19, "Building Demolished"),
    BUILDING_VACANT(20, "Building Vacant"),
    TENANT_MOVED(21, "Tenant Moved"),
    GOVT_BUILDING(22, "Govt Building"),
    CANNOT_ACCESS_TENANT_ROSTER(23, "Can't access Tenant Roster"),
    OTHER(24, "Other");

    private final int id;
    private final String label;

    public static NoteType fromId(int id) {
        for (NoteType type : values()) {
            if (type.getId() == id) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid NoteType id: " + id);
    }
}


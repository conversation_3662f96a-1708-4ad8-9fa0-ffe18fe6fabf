package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum TransactionOriginationType implements BaseEnum {
  REGISTERED(1, "Registered", false),
  SYSTEM_GENERATED(2, "System Generated", true),
  USER_GENERATED(3, "User Generated", true),
  CONFIRMED_SOURCE(4, "Confirmed Source", false),
  EXTERNAL_SOURCE(5, "External Source", true),
  RESEARCHED(6, "Researched", false);

  private final int id;
  private final String label;
  private final boolean editable;

  public static TransactionOriginationType fromId(int id) {
    for (TransactionOriginationType source : values()) {
      if (source.getId() == id) {
        return source;
      }
    }
    throw new IllegalArgumentException("Invalid TransactionOriginationType ID: " + id);
  }
}

package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum Features implements BaseEnum {
    WET_LAB(1, "WetLab"),
    DRY_LAB(2, "DryLab"),
    CLEAN_ROOM(3, "CleanRoom"),
    TEMPERATURE_CONTROLLED(4, "TemperatureControlle");

    private final Integer id;
    private final String label;

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static Features fromId(int id) {
        for (Features type : values()) {
            if (type.id == id) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid Feature ID: " + id);
    }
}

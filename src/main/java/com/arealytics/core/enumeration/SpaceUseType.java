package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SpaceUseType implements BaseEnum {
  OFFICE(9, "Office"),
  FLEX(2, "Flex"),
  HOSPITALITY(6, "Hospitality"),
  INDUSTRIAL(8, "Industrial"),
  LAND(4, "Land"),
  MEDICAL(10, "Medical"),
  MULTI_FAMILY(5, "Multi-family"),
  <PERSON><PERSON><PERSON>(1, "Other"),
  RETAIL(7, "Retail"),
  SPECIAL_USE(11, "Special Use"),
  TELECOM(3, "Telecom");

  private final int id;
  private final String label;

  public static SpaceUseType fromId(int id) {
    for (SpaceUseType type : values()) {
      if (type.id == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Unknown SpaceUseType id: " + id);
  }
}

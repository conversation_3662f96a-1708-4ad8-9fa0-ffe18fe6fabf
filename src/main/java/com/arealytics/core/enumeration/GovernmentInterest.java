package com.arealytics.core.enumeration;

public enum GovernmentInterest implements BaseEnum {
    OWNER_OCCUPIED(1, "Owner Occupied"),
    LEASES(2, "Leases"),
    NONE(3, "None");

    private final int id;
    private final String label;

    GovernmentInterest(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static GovernmentInterest fromId(int id) {
        for (GovernmentInterest interest : values()) {
            if (interest.id == id) {
                return interest;
            }
        }
        throw new IllegalArgumentException("Invalid GovernmentInterest ID: " + id);
    }
}

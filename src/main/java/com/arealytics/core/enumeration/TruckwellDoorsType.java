package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TruckwellDoorsType implements BaseEnum {
  TRUCKWELLS(1, "Truckwells"),
  BUILD_TO_SUIT(2, "Build to Suit"),
  TBD(3, "TBD");

  private final int id;
  private final String label;

  public static TruckwellDoorsType fromId(int id) {
    for (TruckwellDoorsType type : values()) {
      if (type.id == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Unknown TruckwellDoorsType id: " + id);
  }
}

package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum EncumbrancesType implements BaseEnum {
  EMINENT_DOMAIN(1, "Eminent Domain"),
  LIENS(2, "Liens"),
  NONE(3, "None"),
  RESTRICTIONS(4, "Restrictions"),
  UNKNOWN(5, "Unknown"),
  EASEMENT(6, "Easement");

  private final int id;
  private final String label;

  public static EncumbrancesType fromId(int id) {
    for (EncumbrancesType type : values()) {
      if (type.getId() == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid EncumbrancesType ID: " + id);
  }
}

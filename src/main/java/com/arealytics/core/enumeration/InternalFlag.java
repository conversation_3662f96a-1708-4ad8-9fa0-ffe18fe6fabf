package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum InternalFlag implements BaseEnum{
    In_Review(1, "In Review"),
    Need_Review(2, "Need Review"),
    Reviewed(3, "Reviewed");

    private final int id;
    private final String label;

    public static InternalFlag fromId(int id) {
        for (InternalFlag type : values()) {
            if (type.id == id) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid InternalFlag ID: " + id);
    }
}

package com.arealytics.core.enumeration;

public enum Unit implements BaseEnum {
    METRIC(1, "Metric"),
    IMPERIUM(2, "Imperium");

    private final int id;
    private final String label;

    Unit(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static Unit fromId(int id) {
        for (Unit unit : values()) {
            if (unit.id == id) {
                return unit;
            }
        }
        throw new IllegalArgumentException("Invalid Unit ID: " + id);
    }
}

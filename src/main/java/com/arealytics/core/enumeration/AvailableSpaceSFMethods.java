package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum AvailableSpaceSFMethods implements BaseEnum {
  ACTUAL(1, "Actual"),
  LISTING(2, "Listing"),
  THIRD_PARTY(4, "Third-party"),
  MARKET_METHOD(5, "Market method");

  private final int id;
  private final String label;

  public static AvailableSpaceSFMethods fromId(int id) {
    for (AvailableSpaceSFMethods method : values()) {
      if (method.id == id) {
        return method;
      }
    }
    throw new IllegalArgumentException("Invalid AvailableSpaceSFMethods id: " + id);
  }
}

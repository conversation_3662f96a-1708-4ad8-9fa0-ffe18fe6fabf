package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ShareLevel implements BaseEnum {
  MYSELF(1, "Myself"),
  LOCAL_OFFICE(2, "Local Office"),
  METRO(3, "Metro"),
  NATIONAL(5, "National"),
  METRO_OFFICE(6, "Metro Office"),
  EXCHANGE(7, "Exchange");

  private final int id;
  private final String label;

  public static ShareLevel fromId(int id) {
    for (ShareLevel level : values()) {
      if (level.id == id) {
        return level;
      }
    }
    throw new IllegalArgumentException("No ShareLevel with id " + id);
  }
}

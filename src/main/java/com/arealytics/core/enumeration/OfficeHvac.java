package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OfficeHvac implements BaseEnum {
    AC(1, "A/C"),
    HEAT(2, "Heat"),
    BOTH(3, "Both");

    private final int id;
    private final String label;

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static OfficeHvac fromId(int id) {
        for (OfficeHvac officeHvac : values()) {
            if (officeHvac.id == id) {
                return officeHvac;
            }
        }
        throw new IllegalArgumentException("Invalid OfficeHvac ID: " + id);
    }
}

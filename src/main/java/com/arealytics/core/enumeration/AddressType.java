package com.arealytics.core.enumeration;

public enum AddressType implements BaseEnum {
    BILLING(1, "Billing"),
    PHYSICAL(2, "Physical"),
    MAILING(3, "Mailing");

    private final int id;
    private final String label;

    AddressType(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static AddressType fromId(int id) {
        for (AddressType type : values()) {
            if (type.id == id) return type;
        }
        throw new IllegalArgumentException("Invalid AddressType id: " + id);
    }
}

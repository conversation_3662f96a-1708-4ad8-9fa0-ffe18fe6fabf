package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DockDoorsType implements BaseEnum {
  DOCK_DOORS(1, "Dock Doors"),
  BUILD_TO_SUIT(2, "Build To Suit"),
  TBD(3, "TBD");

  private final int id;
  private final String label;

  public static DockDoorsType fromId(int id) {
    for (DockDoorsType type : values()) {
      if (type.id == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Unknown DockDoorsType id: " + id);
  }
}

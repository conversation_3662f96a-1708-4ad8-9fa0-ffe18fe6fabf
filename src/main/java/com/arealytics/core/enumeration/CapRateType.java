package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum CapRateType implements BaseEnum {
  CALCULATED(1, "Calculated"),
  ESTIMATED(2, "Estimated");

  private final int id;
  private final String label;

  public static CapRateType fromId(int id) {
    for (CapRateType type : values()) {
      if (type.getId() == id) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid CapRateType ID: " + id);
  }
}

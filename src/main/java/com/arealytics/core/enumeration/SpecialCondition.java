package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum SpecialCondition implements BaseEnum {
  CALL_BROKER(2, "Call Broker"),
  EXTENSION_OPTION_EXCLUDED(3, "Extension Option Excld"),
  FIXED_FEES(5, "Fixed Fees"),
  FULL_TO_PROCURING_BROKER(6, "Full To Procuring Broker"),
  NONE(7, "None");

  private final int id;
  private final String label;

  public static SpecialCondition fromId(int id) {
    for (SpecialCondition condition : values()) {
      if (condition.id == id) {
        return condition;
      }
    }
    throw new IllegalArgumentException("No SpecialCondition with id " + id);
  }
}

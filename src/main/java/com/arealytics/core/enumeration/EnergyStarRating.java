package com.arealytics.core.enumeration;

public enum EnergyStarRating implements BaseEnum {
    ONE(1, "1"),
    ONE_POINT_FIVE(2, "1.5"),
    TWO(3, "2"),
    TWO_POINT_FIVE(4, "2.5"),
    THREE(5, "3"),
    THREE_POINT_FIVE(6, "3.5"),
    FOUR(7, "4"),
    FOUR_POINT_FIVE(8, "4.5"),
    FIVE(9, "5"),
    FIVE_POINT_FIVE(10, "5.5"),
    SIX(11, "6");

    private final int id;
    private final String label;

    EnergyStarRating(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static EnergyStarRating fromId(int id) {
        for (EnergyStarRating rating : values()) {
            if (rating.id == id) {
                return rating;
            }
        }
        throw new IllegalArgumentException("Invalid EnergyStarRating ID: " + id);
    }
}

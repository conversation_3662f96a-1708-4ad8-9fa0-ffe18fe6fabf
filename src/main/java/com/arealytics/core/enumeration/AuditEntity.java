package com.arealytics.core.enumeration;

public enum AuditEntity{
    PROPERTY("Property", "PropertyID"),
    PARCEL("Parcel", "ParcelID"),
    ADDRESS("Address", "AddressID"),
    LOCATION("Location", "LocationID"),
    BUILDING_FOOTPRINT("BuildingFootPrint", "BuildingFootPrintID"),
    MEDIA("Media", "MediaID"),
    MEDIA_RELATIONSHIP("MediaRelationship", "MediaID"),
    ADDITIONAL_ADDRESS( "AdditionalAddress", "AddressID");

    private final String name;
    private final String idFieldName;

    // Constructor
    AuditEntity(String name, String idFieldName) {
        this.name = name;
        this.idFieldName = idFieldName;
    }

    // Getter for name
    public String getName() {
        return name;
    }

    // Getter for idFieldName
    public String getIdFieldName() {
        return idFieldName;
    }
}

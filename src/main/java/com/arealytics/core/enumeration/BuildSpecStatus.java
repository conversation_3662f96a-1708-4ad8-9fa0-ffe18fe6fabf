package com.arealytics.core.enumeration;

public enum BuildSpecStatus implements BaseEnum {
    EXISTS(1, "Exists"),
    BUILD_TO_SUIT(2, "Build to Suit"),
    TO_BE_DETERMINED(3, "To Be Determined"),
    SPECULATIVE_DEVELOPMENT(4, "Speculative Development");

    private final int id;
    private final String label;

    BuildSpecStatus(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static BuildSpecStatus fromId(int id) {
        for (BuildSpecStatus status : values()) {
            if (status.id == id) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid BuildSpecStatus ID: " + id);
    }
}

package com.arealytics.core.enumeration;

public enum ZoningClass implements BaseEnum {
    AG(1, "AG"),
    B(2, "B"),
    B_3(3, "B-3"),
    I_1(4, "I-1"),
    I_2(5, "I-2"),
    INDUSTRIAL(6, "Industrial"),
    COMMERCIAL(7, "Commercial"),
    RESIDENTIAL(8, "Residential");

    private final int id;
    private final String label;

    ZoningClass(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static ZoningClass fromId(int id) {
        for (ZoningClass zc : values()) {
            if (zc.id == id) {
                return zc;
            }
        }
        throw new IllegalArgumentException("Invalid ZoningClass ID: " + id);
    }
}

package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum NOIType implements BaseEnum {
    CALCULATED(1, "Calculated"),
    ESTIMATED(2, "Estimated");

    private final int id;
    private final String label;

    public static NOIType fromId(int id) {
        for (NOIType type : values()) {
            if (type.getId() == id) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid NOIType ID: " + id);
    }
}

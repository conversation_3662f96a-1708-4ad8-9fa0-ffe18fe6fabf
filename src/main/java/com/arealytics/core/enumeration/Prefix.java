package com.arealytics.core.enumeration;

import lombok.Getter;

@Getter
public enum Prefix implements BaseEnum{
    EAST(1, "E"),
    NORTH(2, "N"),
    NORTHEAST(3, "NE"),
    NORTHWEST(4, "NW"),
    SOUTH(5, "S"),
    SOUTHEAST(6, "SE"),
    SOUTHWEST(7, "SW"),
    WEST(8, "W");

    private final int id;
    private final String label;

    Prefix(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static Prefix fromId(int id) {
        for (Prefix q : values()) {
            if (q.id == id) {
                return q;
            }
        }
        throw new IllegalArgumentException("Invalid Quadrant ID: " + id);
    }
}

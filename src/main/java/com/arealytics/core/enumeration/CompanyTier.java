package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum CompanyTier implements BaseEnum {
    TIER_1(1, "Tier 1"),
    TIER_2(2, "Tier 2"),
    TIER_3(3, "Tier 3"),
    TIER_4(4, "Tier 4");

    private final int id;
    private final String label;

    public static CompanyTier fromId(int id) {
        for (CompanyTier type : values()) {
            if (type.id == id) {
                return type;
            }
        }
        throw new IllegalArgumentException("No AgreementType with id " + id);
    }
}

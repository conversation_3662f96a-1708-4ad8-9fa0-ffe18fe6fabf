package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

public enum ComplexType implements BaseEnum{
    NOT_PART_OF_COMPLEX(1, "Not Part of Complex"),
    PART_OF_COMPLEX(2, "Part of Complex");

    private final Integer id;
    private final String label;

    ComplexType(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static ComplexType fromId(int id) {
        for (ComplexType complexType : values()) {
            if (complexType.id == id) {
                return complexType;
            }
        }
        throw new IllegalArgumentException("Invalid ComplexType ID: " + id);
    }
}

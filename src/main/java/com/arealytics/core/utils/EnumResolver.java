package com.arealytics.core.utils;

import java.lang.reflect.Field;


public class EnumResolver {

    public static <E extends Enum<E>> String resolveEnumLabel(
            Class<? extends Enum<?>> enumClass, 
            Object constantName, 
            String labelFieldName) {
        if (constantName == null) {
            return null;
        }

        try {
            // Iterate through enum constants
            for (Enum<?> constant : enumClass.getEnumConstants()) {
                // Check if the constant name matches
                if (constant.name().equalsIgnoreCase(constantName.toString())) {
                    // Use reflection to access the label/description field
                    Field labelField = enumClass.getDeclaredField(labelFieldName);
                    labelField.setAccessible(true); // Allow access to private fields
                    Object label = labelField.get(constant);

                    return label != null ? label.toString() : constantName.toString();
                }
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace(); 
        }

        // Return the original value as a fallback
        return constantName.toString();
    }
}

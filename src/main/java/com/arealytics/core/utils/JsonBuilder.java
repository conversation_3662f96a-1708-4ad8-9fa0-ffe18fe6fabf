package com.arealytics.core.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.List;
import java.util.stream.Collectors;

public class JsonBuilder {

    private static final ObjectMapper mapper = new ObjectMapper();

    public static JsonNode buildJsonNode(int propertyCount, List<Integer> propertyIds, int listingCount, List<Integer> listingIds,int suiteCount, List<Integer> suiteIds) {
        ObjectNode node = mapper.createObjectNode();
       node.put("PropertyCount", propertyCount);
        node.put("PropertyIDs", propertyIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(",")));
        node.put("ListingCount", listingCount);
        node.put("ListingIDs", listingIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(",")));
        node.put("SuiteCount", suiteCount);
        node.put("SuiteIDs", suiteIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(",")));
        return node;
    }
}

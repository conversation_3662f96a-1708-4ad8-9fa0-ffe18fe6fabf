package com.arealytics.core.utils;

import com.arealytics.core.domain.empiricalProd.EntityModel;

public class UserContext {
  private static final ThreadLocal<EntityModel> currentEntityId = new ThreadLocal<>();

  public static void setLoginEntity(EntityModel entity) {
    currentEntityId.set(entity);
  }

  public static EntityModel getLoginEntity() {
    return currentEntityId.get();
  }

  public static void clear() {
    currentEntityId.remove();
  }
}

package com.arealytics.core.utils;

import com.arealytics.core.enumeration.BaseEnum;
import com.fasterxml.jackson.databind.deser.std.NumberDeserializers.IntegerDeserializer;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.core.types.dsl.CaseBuilder.Cases;
import com.querydsl.core.types.dsl.EnumPath;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberExpression;
import com.querydsl.core.types.dsl.SimpleExpression;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.Expression;

public class EnumUtils<E extends Enum<E> & BaseEnum> {

    public Expression<String> getLabelFromQueryEnum(E attribute) {
        return Expressions.constant(attribute.getLabel());
    }

    public Expression<Integer> getIdFromQueryEnum(E attribute) {
        return Expressions.constant(attribute.getId());
    }

    public static <E extends Enum<E> & BaseEnum> Expression<String> getLabelCaseFromEnumPath(EnumPath<E> enumPath,
            Class<E> enumType) {
        CaseBuilder.Cases<String, StringExpression> cases = new CaseBuilder()
                .when(enumPath.eq(enumType.getEnumConstants()[0])).then(enumType.getEnumConstants()[0].getLabel());

        for (int i = 1; i < enumType.getEnumConstants().length; i++) {
            E e = enumType.getEnumConstants()[i];
            cases = cases.when(enumPath.eq(e)).then(e.getLabel());
        }

        return cases.otherwise("");
    }

    public static <E extends Enum<E> & BaseEnum> Expression<Integer> getIdCaseFromEnumPath(EnumPath<E> enumPath,
            Class<E> enumType) {
        Cases<Integer, SimpleExpression<Integer>> cases = new CaseBuilder()
                .when(enumPath.eq(enumType.getEnumConstants()[0]))
                .then(Expressions.constant(enumType.getEnumConstants()[0].getId()));

        for (int i = 1; i < enumType.getEnumConstants().length; i++) {
            E e = enumType.getEnumConstants()[i];
            cases = cases.when(enumPath.eq(e)).then(Expressions.constant(e.getId()));
        }

        return cases.otherwise(Expressions.constant(0));
    }

}

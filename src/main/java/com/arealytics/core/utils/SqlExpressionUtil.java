package com.arealytics.core.utils;

import com.querydsl.core.types.Expression;
//import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.StringExpression;

public class SqlExpressionUtil {
    /**
     * Utility method to generate a CONCAT SQL expression with or without space.
     *
     * @param addSpace true to insert a space between fields, false to concat directly
     * @param args     expressions to concatenate
     * @return Expression<String> representing the SQL CONCAT expression
     */
    @SafeVarargs
    public static StringExpression concat(boolean addSpace, Expression<String>... args) {
        if (args == null || args.length == 0) {
            throw new IllegalArgumentException("At least one expression must be provided.");
        }

        StringBuilder templateBuilder = new StringBuilder("CONCAT(");
        for (int i = 0; i < args.length; i++) {
            templateBuilder.append("{").append(i).append("}");
            if (i != args.length - 1) {
                templateBuilder.append(addSpace ? ", ' ', " : ", ");
            }
        }
        templateBuilder.append(")");

        return Expressions.stringTemplate(templateBuilder.toString(), (Object[]) args);
    }

    @SafeVarargs
    public static <T> Expression<T> coalesce(Expression<T>... args) {
        StringBuilder templateBuilder = new StringBuilder("COALESCE(");
        for (int i = 0; i < args.length; i++) {
            templateBuilder.append("{").append(i).append("}");
            if (i != args.length - 1) {
                templateBuilder.append(", ");
            }
        }
        templateBuilder.append(")");

        return Expressions.template(args[0].getType(), templateBuilder.toString(), (Object[]) args);
    }


}

package com.arealytics.core.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

// Custom Deserializer for incoming requests (LocalDate from dd/MM/yyyy)

@Component
public class FlexibleDateDeserializer extends JsonDeserializer<LocalDateTime> {

    private static final DateTimeFormatter INPUT_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");

    @Override
    public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt)
            throws IOException, JsonProcessingException {
        String date = p.getText();

        if (date == null || date.trim().isEmpty()) {
            return null;
        }

        try {
            // Try parsing as LocalDate first (dd/MM/yyyy format)
            LocalDate localDate = LocalDate.parse(date, INPUT_FORMATTER);
            return localDate.atStartOfDay(); // Convert to LocalDateTime at 00:00
        } catch (DateTimeParseException e) {
            try {
                // Fallback to ISO format if the above fails
                return LocalDateTime.parse(date);
            } catch (DateTimeParseException ex) {
                throw new IllegalArgumentException("Unable to parse date: " + date);
            }
        }
    }
}

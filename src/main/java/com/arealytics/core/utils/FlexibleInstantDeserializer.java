package com.arealytics.core.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Component
public class FlexibleInstantDeserializer extends JsonDeserializer<Instant> {
    private static final DateTimeFormatter INPUT_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");

    @Override
    public Instant deserialize(JsonParser p, DeserializationContext ctxt)
            throws IOException, JsonProcessingException {
        String date = p.getText();

        if (date == null || date.trim().isEmpty()) {
            return null;
        }

        try {
            // Try parsing as LocalDate first (dd/MM/yyyy)
            LocalDate localDate = LocalDate.parse(date, INPUT_FORMATTER);
            return localDate.atStartOfDay(ZoneId.of("UTC")).toInstant(); // Return Instant at UTC midnight
        } catch (DateTimeParseException e) {
            try {
                // Try parsing as ISO Instant (e.g., 2024-07-31T12:34:56Z)
                return Instant.parse(date);
            } catch (DateTimeParseException ex) {
                throw new IllegalArgumentException("Unable to parse date as Instant: " + date);
            }
        }
    }
}

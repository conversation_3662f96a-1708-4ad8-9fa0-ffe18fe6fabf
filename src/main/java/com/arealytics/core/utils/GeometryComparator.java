package com.arealytics.core.utils;

import org.javers.core.diff.custom.CustomValueComparator;
import org.locationtech.jts.geom.Geometry;

public class GeometryComparator implements CustomValueComparator<Geometry> {

    @Override
    public boolean equals(Geometry oldValue, Geometry newValue) {
        if (oldValue == null && newValue == null) {
            return true;
        }
        if (oldValue == null || newValue == null) {
            return false;
        }
        return oldValue.equalsExact(newValue); // Use equalsExact for precise geometry comparison
    }

    @Override
    public String toString(Geometry value) {
        return value != null ? value.toString() : null;
    }
}

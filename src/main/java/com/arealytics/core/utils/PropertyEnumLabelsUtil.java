package com.arealytics.core.utils;

import com.arealytics.core.dto.querydsl.PropertyDetailsQuerydslDTO;
import com.arealytics.core.dto.response.PropertyDetailsSizeResponseDTO;
import com.arealytics.core.exception.EnumLabelMappingException;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class PropertyEnumLabelsUtil {

    // Method to map a single object
    public static PropertyDetailsSizeResponseDTO mapEnumLabels(
            PropertyDetailsQuerydslDTO queryDTO,
            Map<String, String> enumFieldMapping
    ) {
        if (queryDTO == null) {
            return null;
        }

        PropertyDetailsSizeResponseDTO responseDTO = new PropertyDetailsSizeResponseDTO();
        BeanUtils.copyProperties(queryDTO, responseDTO);

        Class<?> inputClass = queryDTO.getClass();
        Class<?> outputClass = responseDTO.getClass();

        for (Map.Entry<String, String> entry : enumFieldMapping.entrySet()) {
            String idField = entry.getKey();
            String labelField = entry.getValue();

            try {
                String getterName = "get" + idField;
                Method getter = inputClass.getMethod(getterName);
                Object enumOrId = getter.invoke(queryDTO);

                if (enumOrId == null) {
                    continue;
                }

                String label;

                if (enumOrId instanceof Enum<?>) {
                    Method getLabelMethod = enumOrId.getClass().getMethod("getLabel");
                    label = (String) getLabelMethod.invoke(enumOrId);
                } else {
                    String enumClassName = idField.replace("ID", "");
                    String fullEnumClassName = "com.arealytics.core.enumeration." + enumClassName;

                    Class<?> enumClass = Class.forName(fullEnumClassName);
                    Method fromIdMethod = enumClass.getMethod("fromId", int.class);
                    Object enumInstance = fromIdMethod.invoke(null, (int) enumOrId);

                    Method getLabelMethod = enumClass.getMethod("getLabel");
                    label = (String) getLabelMethod.invoke(enumInstance);
                }

                String setterName = "set" + labelField;
                Method setter = outputClass.getMethod(setterName, String.class);
                setter.invoke(responseDTO, label);

            } catch (NoSuchMethodException nsme) {
                throw new EnumLabelMappingException("No such method for field " + idField, nsme);
            } catch (Exception e) {
                e.printStackTrace();
                throw new EnumLabelMappingException("Failed mapping for field " + idField + " -> " + labelField, e);
            }
        }
        return responseDTO;
    }

    // Method to map a list of objects
    public static List<PropertyDetailsSizeResponseDTO> mapEnumLabels(
            List<PropertyDetailsQuerydslDTO> queryDTOs,
            Map<String, String> enumFieldMapping
    ) {
        if (queryDTOs == null || queryDTOs.isEmpty()) {
            return Collections.emptyList();
        }

        return queryDTOs.stream()
                .map(dto -> mapEnumLabels(dto, enumFieldMapping))
                .collect(Collectors.toList());
    }
}

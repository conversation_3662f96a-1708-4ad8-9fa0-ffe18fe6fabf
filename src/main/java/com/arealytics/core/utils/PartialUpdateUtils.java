package com.arealytics.core.utils;

import java.beans.PropertyDescriptor;

import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

public class PartialUpdateUtils {
    public static <T> void copyNonNullProperties(T source, T target) {
        BeanWrapper srcWrapper = new BeanWrapperImpl(source);
        BeanWrapper tgtWrapper = new BeanWrapperImpl(target);

        for (PropertyDescriptor descriptor : srcWrapper.getPropertyDescriptors()) {
            String propertyName = descriptor.getName();
            Object value = srcWrapper.getPropertyValue(propertyName);

            if (!"class".equals(propertyName) && value != null) {
                tgtWrapper.setPropertyValue(propertyName, value);
            }
        }
    }
}

package com.arealytics.core.utils;

public class NumberUtils {

  public static boolean isInteger(String str) {
    if (str == null)
      return false;
    try {
      Integer.parseInt(str);
      return true;
    } catch (NumberFormatException e) {
      return false;
    }
  }

  public static Integer safeParseInt(String value) {
    try {
      return Integer.parseInt(value.trim());
    } catch (NumberFormatException | NullPointerException e) {
      return null;
    }
  }
}

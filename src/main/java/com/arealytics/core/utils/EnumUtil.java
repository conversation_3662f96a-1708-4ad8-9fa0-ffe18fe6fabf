package com.arealytics.core.utils;

import com.arealytics.core.enumeration.BaseEnum;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.stream.Collectors;

public class EnumUtil {

    public static <E extends Enum<E> & BaseEnum> String getEnumValuesFromIds(
            String commaSeparatedIds,
            Class<E> enumClass,
            boolean returnLabel
    ) {
        if (commaSeparatedIds == null || commaSeparatedIds.trim().isEmpty()) {
            return "";
        }

        return Arrays.stream(commaSeparatedIds.split(","))
                .map(String::trim)
                .map(Integer::parseInt)
                .map(id -> fromId(enumClass, id))
                .map(enumVal -> returnLabel ? getLabel(enumVal) : enumVal.name())
                .collect(Collectors.joining(","));
    }

    private static <E extends Enum<E> & BaseEnum> E fromId(Class<E> enumClass, int id) {
        for (E enumConstant : enumClass.getEnumConstants()) {
            if (enumConstant.getId() == id) {
                return enumConstant;
            }
        }
        throw new IllegalArgumentException("Invalid ID: " + id + " for enum " + enumClass.getSimpleName());
    }

    private static <E extends Enum<E>> String getLabel(E enumVal) {
        try {
            Method method = enumVal.getClass().getMethod("getLabel");
            return (String) method.invoke(enumVal);
        } catch (Exception e) {
            throw new RuntimeException("Enum " + enumVal.getClass().getSimpleName() + " does not have getLabel method");
        }
    }
}

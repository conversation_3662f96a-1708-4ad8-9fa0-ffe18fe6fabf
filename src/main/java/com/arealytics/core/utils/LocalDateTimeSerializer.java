package com.arealytics.core.utils;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

// Custom Serializer for outgoing responses (LocalDateTime to ISO format)

@Component
public class LocalDateTimeSerializer extends JsonSerializer<LocalDateTime> {

    @Override
    public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
        if (value != null) {
             // Convert LocalDateTime to actual UTC
            OffsetDateTime utcDateTime = value
                    .atZone(ZoneId.systemDefault())          // Attach system default timezone
                    .withZoneSameInstant(ZoneOffset.UTC)     // Convert to UTC
                    .toOffsetDateTime();                     // Get OffsetDateTime with +00:00 offset
            
            // Serialize in proper ISO 8601 UTC format
            gen.writeString(utcDateTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));
        }
    }
}

package com.arealytics.core.projection;

import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.dto.response.PropertySearchSaleResponseDTO;
import com.arealytics.core.stringTemplates.PropertyStringTemplates;
import com.arealytics.core.utils.SqlExpressionUtil;
import com.arealytics.core.utils.UnitConversionUtil;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;

import java.math.BigDecimal;

import static com.arealytics.core.common.querydsl.join.JoinConditions.JoinListingAgentOnListingEntity;
import static com.arealytics.core.common.querydsl.join.JoinConditions.JoinPersonOnEntityModel;

public class SalePropertiesProjection {
    private static final QProperty property = QProperty.property;
    private static final QAddress address = QAddress.address;
    private static final QListing listing = QListing.listing;
    private static final QMedia media = QMedia.media;
    private static final QListingforSale listingforSale = QListingforSale.listingforSale;
    private static final QPerson listingAgentPerson = new QPerson("listingAgentPerson");
    private static final QCompany listingBranch = QCompany.company;
    private static final QListingEntity listingEntity = QListingEntity.listingEntity;
    private static final QEntityModel listingAgent = new QEntityModel("listingAgent");

    public static JPQLQuery<String> buildAdditionalBrokersQuery() {
        return JPAExpressions
                .select(PropertyStringTemplates.getAdditionalBrokersName())
                .from(listingEntity)
                .leftJoin(listingAgent).on(JoinListingAgentOnListingEntity(listingAgent, listingEntity))
                .leftJoin(listingAgentPerson).on(JoinPersonOnEntityModel(listingAgentPerson, listingAgent))
                .where(
                        listingEntity.listing.listingId.eq(listing.listingId)
                )
                .groupBy(listingEntity.listing.listingId);
    }


    public static Expression<PropertySearchSaleResponseDTO> projectSalePropertiesDTO() {
        JPQLQuery<String> additionalBrokersSubQuery = buildAdditionalBrokersQuery();
        return Projections.constructor(
                PropertySearchSaleResponseDTO.class,
                listing.listingTypeId,
                listing.isActive,
                SqlExpressionUtil.coalesce(media.path, Expressions.constant("NoPhoto.jpg")),
                property.propertyDetails.yearBuilt,
                property.propertyDetails.floors,
                property.amenities.propertyComments,
                property.propertyDetails.buildingComments,
                Expressions.numberTemplate(BigDecimal.class, "ROUND({0}, 0)", listing.totalVacant),
                UnitConversionUtil.sqftToSqmConversion(listing.totalVacant),
                listing.listingStatus.listingStatusId,
                listingforSale.isCondoSale,
                property.createdDate,
                additionalBrokersSubQuery
        );
    }
}

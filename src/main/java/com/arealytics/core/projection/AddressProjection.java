package com.arealytics.core.projection;

import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.dto.response.AddressDTO;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Projections;

public class AddressProjection {
    private static final QAddress address = QAddress.address;
    private static final QCity city = QCity.city;
    private static final QCountry country = QCountry.country;
    private static  final QState state = QState.state;
    private static final QCounty county = QCounty.county;

    public static Expression<AddressDTO> projectAddressDTO() {
        return Projections.constructor(
                AddressDTO.class,
                address.addressId,
                address.addressTypeId,
                address.addressStreetNumber,
                address.addressStreetName,
                address.addressText,
                address.zipCode,
                address.parentTableId,
                address.sequence,
                address.suffixId.as("streetSuffix1"),
                address.suffix2Id.as("streetSuffix2"),
                address.zip4,
                address.floorNumber,
                address.cityId,
                city.cityName.as("City"),
                address.stateId,
                state.stateAbbr.as("StateCode"),
                state.stateName.as("State"),
                address.countyId,
                county.countyName.as("County"),
                address.countryId,
                country.countryName.as("CountryName"),
                country.countryName.as("Country"),
                country.alpha2Code.as("CountryCode"),
                address.address1,
                address.address2,
                address.streetNumberMin,
                address.streetNumberMax,
                address.eastWestSt,
                address.northSouthSt,
                address.quadrantId,
                address.prefixId.as("streetPrefix1"),
                address.prefix2Id.as("streetPrefix2"),
                address.buildingNumber,
                address.partOfCenterComplex,
                address.complexName,
                address.primaryStreet,
                address.primaryAccess,
                address.primaryTrafficCount,
                address.primaryTrafficCountDate,
                address.primaryFrontage,
                address.secondaryStreet,
                address.secondaryAccess,
                address.secondaryTrafficCount,
                address.secondaryTrafficCountDate,
                address.secondaryFrontage,
                address.isIntersection.as("addressType"));
    }
}

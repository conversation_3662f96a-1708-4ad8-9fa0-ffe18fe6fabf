package com.arealytics.core.projection;

import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.dto.response.PropertySearchLeaseResponseDTO;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPQLQuery;

import static com.arealytics.core.projection.SalePropertiesProjection.buildAdditionalBrokersQuery;

public class LeasePropertiesProjection {
    private static final QProperty property = QProperty.property;
    private static final QListing listing = QListing.listing;

    public static Expression<PropertySearchLeaseResponseDTO> projectLeaseProprtiesDTO() {
        JPQLQuery<String> additionalBrokersSubQuery = buildAdditionalBrokersQuery();
        return Projections.constructor(
                PropertySearchLeaseResponseDTO.class,
                listing.listingTypeId,
                listing.isActive,
                property.propertyDetails.classTypeID,
                property.amenities.propertyComments,
//                        Expressions.stringTemplate("CONCAT('$', {0}, ' - ', '$', {1})",
//                                listingforLease.askingLeaseRatePerMonthMin, listingforLease.askingLeaseRatePerMonthMax).as("AskingRate"),
//                        Expressions.stringTemplate("CONCAT({0}, CASE WHEN {1} IS NOT NULL THEN CONCAT('-', {1}) ELSE '' END)",
//                                listingBranch.altCompanyName, listingBranch.companyName).as("ListingCompanyName"),
//                        Expressions.stringTemplate("CONCAT({0}, ' ', {1})", listingAgentPerson.firstName, listingAgentPerson.lastName).as("AgentName"),
//                        new CaseBuilder()
//                                .when(listingforLease.askingLeaseRatePerYearMin.isNotNull()
//                                        .and(listingforLease.askingLeaseRatePerYearMax.isNotNull())
//                                        .and(listingforLease.askingLeaseRatePerYearMin.ne(listingforLease.askingLeaseRatePerYearMax)))
//                                .then(Expressions.stringTemplate("CONCAT('$', FORMAT({0}, 0), ' - ', '$', FORMAT({1}, 0))",
//                                        listingforLease.askingLeaseRatePerYearMin, listingforLease.askingLeaseRatePerYearMax))
//                                .when(listingforLease.askingLeaseRatePerYearMin.isNotNull()
//                                        .and(listingforLease.askingLeaseRatePerYearMax.isNotNull())
//                                        .and(listingforLease.askingLeaseRatePerYearMin.eq(listingforLease.askingLeaseRatePerYearMax)))
//                                .then(Expressions.stringTemplate("CONCAT('$', FORMAT({0}, 0))",
//                                        listingforLease.askingLeaseRatePerYearMin))
//                                .when(listingforLease.askingLeaseRatePerYearMin.isNotNull()
//                                        .and(listingforLease.askingLeaseRatePerYearMax.isNull()))
//                                .then(Expressions.stringTemplate("CONCAT('$', FORMAT17, 0))",
//                                        listingforLease.askingLeaseRatePerYearMin))
//                                .otherwise("").as("Price"),
//                        Expressions.numberTemplate(Double.class, "ROUND({0}, 0)", listing.totalVacant)
//                                .as("TotalVacant"),
//                        Expressions.stringTemplate("CAST(ROUND(IFNULL({0}, 0) * 0.092903, 2) AS CHAR(20))", listing.totalVacant)
//                                .as("TotalVacantSM"),
//                        new CaseBuilder()
//                                .when(listingforLease.askingLeaseRatePerYearMin.isNotNull()
//                                        .and(listingforLease.askingLeaseRatePerYearMax.isNotNull())
//                                        .and(listingforLease.askingLeaseRatePerYearMin.ne(listingforLease.askingLeaseRatePerYearMax)))
//                                .then(Expressions.stringTemplate("CONCAT('$', FORMAT({0}, 0), ' - ', '$', FORMAT({1}, 0))",
//                                        listingforLease.askingLeaseRatePerYearMin, listingforLease.askingLeaseRatePerYearMax))
//                                .when(listingforLease.askingLeaseRatePerYearMin.isNotNull()
//                                        .and(listingforLease.askingLeaseRatePerYearMax.isNotNull())
//                                        .and(listingforLease.askingLeaseRatePerYearMin.eq(listingforLease.askingLeaseRatePerYearMax)))
//                                .then(Expressions.stringTemplate("CONCAT('$', FORMAT({0}, 0))",
//                                        listingforLease.askingLeaseRatePerYearMin))
//                                .when(listingforLease.askingLeaseRatePerYearMin.isNotNull()
//                                        .and(listingforLease.askingLeaseRatePerYearMax.isNull()))
//                                .then(Expressions.stringTemplate("CONCAT('$', FORMAT({0}, 0))",
//                                        listingforLease.askingLeaseRatePerYearMin))
//                                .otherwise("").as("AskingLeaseRatePerYrText"),
//                        Expressions.numberTemplate(Double.class, "ROUND({0}, 0)", listingforLease.minDiv).as("MinDiv"),
//                        Expressions.stringTemplate("CAST(ROUND({0} * 0.092903, 0) AS CHAR(20))", listingforLease.minDiv)
//                                .as("MinDivSM"),
//                        Expressions.numberTemplate(Integer.class, "DATEDIFF(CURRENT_TIMESTAMP, {0})", listing.modifiedDate)
//                                .as("ModDateCounter"),
//                        otherBrokersSubQuery.as("AdditionalAgents"),
                listing.listingStatus.listingStatusId,
                property.propertyDetails.yearBuilt,
                property.propertyDetails.floors,
                property.propertyDetails.buildingComments,
                property.propertyDetails.mainPhotoUrl,
                property.createdDate,
                property.createdBy.entityId,
                property.modifiedBy.entityId,
                additionalBrokersSubQuery

//                Expressions.nullExpression(String.class),
//                Expressions.nullExpression(BigDecimal.class),
//                Expressions.nullExpression(String.class),
//                Expressions.nullExpression(BigDecimal.class),
//                Expressions.nullExpression(String.class)
                // (Expression<List<PropertyDTO>>) (Object) Expressions.nullExpression(Object.class)
        );
    }
}


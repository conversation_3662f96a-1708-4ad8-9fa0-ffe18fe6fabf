package com.arealytics.core.projection;

import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.enumeration.SupportRoleType;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;

import com.arealytics.core.utils.UnitConversionUtil;
import com.arealytics.core.dto.response.PropertySearchLeaseAndSaleResponseDTO;

public class LeaseAndSalePropertyProjection {
    private static final QProperty property = QProperty.property;
    private static final QAddress address = QAddress.address;
    private static final QListing listing = QListing.listing;
    private static final QListingforLease listingForLease = QListingforLease.listingforLease;
    private static final QListingforSale listingforSale = QListingforSale.listingforSale;
    private static final QListingEntity listingEntity = QListingEntity.listingEntity;
    private static final QCompanySupport companySupport = QCompanySupport.companySupport;
    private static final QEntityModel listingAgent = QEntityModel.entityModel;

    public static Expression<PropertySearchLeaseAndSaleResponseDTO> projectLeaseAndSaleProprtiesDTO() {
        return Projections.constructor(
                PropertySearchLeaseAndSaleResponseDTO.class,
                property.propertyDetails.classTypeID,
                address.streetNumberMinN,
                address.streetNumberMaxN,
                listingForLease.askingLeaseRatePerYearMin,
                listingForLease.askingLeaseRatePerYearMax,
                property.propertyDetails.tenancyTypeID,
                listingForLease.leaseTypeId,
                property.propertySize.lotSizeSF,
                UnitConversionUtil.sqftToSqmConversion(property.propertySize.lotSizeSF),
//                listingAgent.company.companyId,
                Expressions.nullExpression(Integer.class),
                listingEntity.entity.entityId,
//                companySupport.supportRoleTypeId,
                Expressions.nullExpression(SupportRoleType.class),
                listingforSale.isCondoSale,
                property.propertyDetails.constructionStatusID,
                listing.listingStatus.listingStatusId,
                Expressions.nullExpression(Integer.class),
                Expressions.nullExpression(Boolean.class),
                property.createdDate

        );
    }
}


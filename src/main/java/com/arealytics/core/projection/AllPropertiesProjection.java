package com.arealytics.core.projection;

import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.dto.response.PropertyDTO;
import com.arealytics.core.dto.response.PropertySearchAllResponseDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;

import java.math.BigDecimal;
import java.util.List;

public class AllPropertiesProjection {
    private static final QProperty property = QProperty.property;
    private static final QPropertyStrataRelationship propertyStrataRelationship = QPropertyStrataRelationship.propertyStrataRelationship;
    private static  final QStatusDefination statusDefination = QStatusDefination.statusDefination;

    public static Expression<PropertySearchAllResponseDTO> projectAllProprtiesDTO() {
        return Projections.constructor(
                PropertySearchAllResponseDTO.class,
                property.isActive,
                property.propertyDetails.yearBuilt,
                property.propertyDetails.floors,
                property.propertyDetails.condoUnit,
                property.internalToolFields.needsResearchComments,
                property.propertyDetails.lastReviewedBy,
                property.propertyDetails.lastReviewedDate,
                property.propertySize.lotSize,
                property.propertySize.lotSizeSF,
                property.propertySize.contributedGBASizeSF,
                property.internalToolFields.researchTypeID,
                property.internalToolFields.researchTypeName,
                property.internalToolFields.isSkipped,
                property.propertyDetails.mainPhotoUrl,
                property.propertyDetails.hasNoBuildingFootprints,
                property.propertyDetails.hasNoExistingParcelInTileLayer,
                propertyStrataRelationship.masterPropertyId,
                property.createdDate,
                property.modifiedDate,
                property.createdBy.entityId,
                property.modifiedBy.entityId,
                statusDefination.statusName,
                Expressions.nullExpression(String.class),
                Expressions.nullExpression(BigDecimal.class),
                Expressions.nullExpression(String.class),
                Expressions.nullExpression(BigDecimal.class),
                Expressions.nullExpression(String.class),
                Expressions.nullExpression(List.class)
        );
    }
}


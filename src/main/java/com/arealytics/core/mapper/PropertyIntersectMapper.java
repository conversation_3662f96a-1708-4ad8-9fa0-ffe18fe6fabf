package com.arealytics.core.mapper;

import com.arealytics.core.domain.empiricalProd.Market;
import com.arealytics.core.domain.empiricalProd.SubMarket;
import com.arealytics.core.dto.response.PropertyIntersectResponseDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface PropertyIntersectMapper {

    PropertyIntersectMapper INSTANCE = Mappers.getMapper(PropertyIntersectMapper.class);

    @Mapping(source = "market.marketId", target = "marketID")
    @Mapping(source = "market.marketName", target = "marketName")
    @Mapping(source = "market.metroId", target = "metroID")
    @Mapping(source = "subMarket.subMarketId", target = "subMarketID")
    @Mapping(source = "subMarket.subMarketName", target = "subMarketName")
    PropertyIntersectResponseDTO toDTO(Market market, SubMarket subMarket);

    @Mapping(source = "market.marketId", target = "marketID")
    @Mapping(source = "market.marketName", target = "marketName")
    @Mapping(source = "market.metroId", target = "metroID")
    @Mapping(target = "subMarketID", ignore = true)
    @Mapping(target = "subMarketName", ignore = true)
    PropertyIntersectResponseDTO toDTO(Market market);
}

package com.arealytics.core.mapper;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.arealytics.core.domain.empiricalGIS.BuildingFootPrint;
import com.arealytics.core.dto.request.BuildingFootPrintRequestDTO;
import com.arealytics.core.dto.response.BuildingFootPrintDTO;

@Mapper(
        componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BuildingFootPrintMapper {
    BuildingFootPrintDTO toDTO(BuildingFootPrint buildingFootprint);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(source = "area", target = "sizeInSF")
    @Mapping(source = "minFloorNumber", target = "propertyMinFloor")
    @Mapping(source = "maxFloorNumber", target = "propertyMaxFloor")
    BuildingFootPrint toEntity(BuildingFootPrintRequestDTO dto);
}

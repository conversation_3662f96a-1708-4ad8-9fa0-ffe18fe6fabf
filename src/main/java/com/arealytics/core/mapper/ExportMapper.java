package com.arealytics.core.mapper;

import com.arealytics.core.domain.empiricalProd.ExportSettings;
import com.arealytics.core.dto.response.ExportSettingsResponseDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValueMappingStrategy;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL)
public interface ExportMapper {

  @Mapping(source = "parentTableId", target = "parentTableID")
  @Mapping(source = "settingsJson", target = "settingsJSON", qualifiedByName = "getSettingsFromJson")
  ExportSettingsResponseDTO toDTO(ExportSettings exportSettings);

  @Named("getSettingsFromJson")
  default Map<String, Object> getUpdatesFromJson(String settings) throws IOException {
    try {
      ObjectMapper mapper = new ObjectMapper();
      List<Map<String, Object>> json = mapper.readValue(settings, new TypeReference<List<Map<String, Object>>>() {});

      return json.getFirst();
    } catch (IOException e) {
      throw new RuntimeException("Error parsing updatesJson", e);
    }
  }
}

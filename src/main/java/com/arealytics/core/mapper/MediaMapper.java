package com.arealytics.core.mapper;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueMappingStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.arealytics.core.domain.empiricalProd.MediaRelationship;
import com.arealytics.core.dto.request.MediaRequestDTO;
import com.arealytics.core.dto.response.MediaDTO;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL)
public interface MediaMapper {

  @Mapping(source = "media.mediaId", target = "mediaID")
  @Mapping(source = "media.mediaName", target = "mediaName")
  @Mapping(source = "media.height", target = "height")
  @Mapping(source = "media.width", target = "width")
  @Mapping(source = "media.size", target = "size")
  @Mapping(source = "media.path", target = "path")
  @Mapping(source = "media.ext", target = "ext")
  @Mapping(source = "media.description", target = "description")
  @Mapping(target = "modifiedByName", ignore = true)
  @Mapping(source = "mediaRelationshipId", target = "mediaRelationshipID")
  @Mapping(source = "mediaRelationTypeId", target = "mediaRelationTypeID")
  @Mapping(expression = "java(mediaRelationship.getMediaRelationTypeId() != null ? mediaRelationship.getMediaRelationTypeId().getLabel() : null)", target = "mediaRelationTypeName")
  @Mapping(source = "mediaTypeId", target = "mediaTypeID")
  @Mapping(expression = "java(mediaRelationship.getMediaTypeId() != null ? mediaRelationship.getMediaTypeId().getLabel() : null)", target = "mediaTypeName")
  @Mapping(source = "mediaSubTypeId", target = "mediaSubTypeID")
  @Mapping(expression = "java(mediaRelationship.getMediaSubTypeId() != null ? mediaRelationship.getMediaSubTypeId().getLabel() : null)", target = "mediaSubTypeName")
  @Mapping(source = "relationId", target = "relationID")
  @Mapping(source = "isDefault", target = "isDefault")
  @Mapping(source = "media.isOwnMedia", target = "isOwnMedia")
  @Mapping(source = "media.mediaSourceId", target = "mediaSourceID")
  @Mapping(source = "media.sourceComments", target = "sourceComments")
  @Mapping(source = "property.propertyID", target = "propertyID")
  @Mapping(source = "property.propertyDetails.condoUnit", target = "condoUnit")
  @Mapping(source = "media.createdBy.entityId", target = "createdBy")
  @Mapping(source = "media.createdDate", target = "createdDate")
  @Mapping(source = "media.modifiedBy.entityId", target = "modifiedBy")
  @Mapping(source = "media.modifiedDate", target = "modifiedDate")
  @Mapping(target = "hasEdit", ignore = true)
  @Mapping(target = "buildingSizeSF", ignore = true)
  MediaDTO toDTO(MediaRelationship mediaRelationship);

  @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
  @Mapping(source = "mediaName", target = "media.mediaName")
  @Mapping(source = "height", target = "media.height")
  @Mapping(source = "width", target = "media.width")
  @Mapping(source = "size", target = "media.size")
  @Mapping(source = "path", target = "media.path")
  @Mapping(source = "ext", target = "media.ext")
  @Mapping(source = "description", target = "media.description")
  @Mapping(source = "ownMedia", target = "media.isOwnMedia") // using ownMedia due to setter and getter
  @Mapping(source = "mediaSourceId", target = "media.mediaSourceId")
  @Mapping(source = "sourceComments", target = "media.sourceComments")
  @Mapping(source = "mediaRelationTypeId", target = "mediaRelationTypeId")
  @Mapping(source = "mediaTypeId", target = "mediaTypeId")
  @Mapping(source = "mediaSubTypeId", target = "mediaSubTypeId")
  @Mapping(source = "relationId", target = "relationId")
  @Mapping(source = "propertyId", target = "property.propertyID")
  @Mapping(source = "default", target = "isDefault") // using default due to setter and getter
  MediaRelationship toEntity(
      MediaRequestDTO mediaRequestDTO, @MappingTarget MediaRelationship entity);
}

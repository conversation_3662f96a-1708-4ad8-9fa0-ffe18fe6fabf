package com.arealytics.core.mapper;

import com.arealytics.core.domain.empiricalProd.ReleaseUpdates;
import com.arealytics.core.dto.response.ReleaseUpdatesResponseDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValueMappingStrategy;

import java.io.IOException;
import java.util.List;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL)
public interface ReleaseUpdatesMapper {

  @Mapping(target = "releaseID", source = "releaseId")
  @Mapping(target = "applicationID", source = "application.applicationId")
  @Mapping(target = "updates", source = "updates", qualifiedByName = "getUpdatesFromJson")
  ReleaseUpdatesResponseDTO toDTO(ReleaseUpdates releaseUpdates);

  @Named("getUpdatesFromJson")
  default List<ReleaseUpdatesResponseDTO.Update> getUpdatesFromJson(String updates) throws IOException {
    try {
      ObjectMapper mapper = new ObjectMapper();
      return mapper.readTree(updates)
          .get("updates") // get the array under "updates"
          .traverse(mapper)
          .readValueAs(new TypeReference<List<ReleaseUpdatesResponseDTO.Update>>() {
          });
    } catch (IOException e) {
      throw new RuntimeException("Error parsing updatesJson", e);
    }
  }
}

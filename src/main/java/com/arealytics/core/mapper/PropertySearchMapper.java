package com.arealytics.core.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.dto.querydsl.PropertySearchQuerydslDTO;

@Mapper(componentModel = "spring")
public interface PropertySearchMapper {

    // Completely ignore this mapping to avoid infinite loop
    @Mapping(source = "propertyDetails.useTypeID", target = "useTypeID")
    @Mapping(source = "propertyDetails.specificUseID", target = "specificUseID")
    @Mapping(source = "propertyDetails.specificUseName", target = "specificUseName")
    @Mapping(source = "propertySize.buildingSizeSF", target = "buildingSize")
    @Mapping(source = "propertySize.lotSizeSF", target = "lotSize")
    @Mapping(source = "propertyDetails.yearBuilt", target = "yearBuilt")
    @Mapping(source = "propertyDetails.floors", target = "floors")
    @Mapping(source = "propertyDetails.buildingComments", target = "buildingComments")
    @Mapping(source = "propertyDetails.condoTypeID", target = "condoTypeID")
    @Mapping(source = "internalToolFields.isSkipped", target = "isSkipped")
    @Mapping(source = "internalToolFields.researchTypeName", target = "researchTypeName")
    @Mapping(source = "internalToolFields.researchTypeID", target = "researchTypeID")
    @Mapping(source = "property.createdBy.entityId", target = "createdBy")
    @Mapping(source = "property.modifiedBy.entityId", target = "modifiedBy")
    PropertySearchQuerydslDTO toDTO(Property property);

    @Mapping(source = "createdBy", target = "createdBy.entityId")
    @Mapping(source = "modifiedBy", target = "modifiedBy.entityId")
    Property toEntity(PropertySearchQuerydslDTO dto);
}

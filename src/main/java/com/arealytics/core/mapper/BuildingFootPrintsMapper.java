package com.arealytics.core.mapper;


import com.arealytics.core.domain.empiricalGIS.BuildingFootPrint;
import com.arealytics.core.dto.response.BuildingFootPrintsDTO;
import com.arealytics.core.enumeration.SpecificUseType;
import com.arealytics.core.enumeration.UseType;
import com.arealytics.core.utils.UnitConversionUtil;
import org.mapstruct.*;

@Mapper(
        componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL
)
public interface BuildingFootPrintsMapper {
    @Mapping(source = "crePropertyId", target = "propertyId")
    @Mapping(source = "sizeInSF", target = "sizeInSM", qualifiedByName = "sqftToSqm")
    @Mapping(source = "useTypeId", target = "useTypeName", qualifiedByName = "useTypeLabel")
    @Mapping(source = "additionalUseTypeId", target = "additionalUseTypeName", qualifiedByName = "additionalUseTypeLabel")
    @Mapping(source = "mainSpecificUseTypeId", target = "mainSpecificUseTypeName", qualifiedByName = "mainSpecificUseTypeLabel")
    @Mapping(source = "additionalSpecificUseTypeId", target = "additionalSpecificUseTypeName", qualifiedByName = "additionalSpecificUseTypeLabel")
    @Mapping(target = "condoUnit", expression = "java(null)")
    @Mapping(target = "propertyUseTypeID", expression = "java(null)")
    BuildingFootPrintsDTO toDTO(BuildingFootPrint buildingFootprint);

    @Named("sqftToSqm")
    default Double sqftToSqm(Double sizeInSF) {
        return sizeInSF != null ?  UnitConversionUtil.sqftToSqm(sizeInSF) : null;
    }

    @Named("useTypeLabel")
    default String useTypeLabel(Integer useTypeId) {
        return  useTypeId != null && UseType.fromId(useTypeId) != null
                ? UseType.fromId(useTypeId).getLabel()
                : null;
    }
    
    @Named("additionalUseTypeLabel")
    default String additionalUseTypeLabel(Integer additionalUseTypeId) {
        return  additionalUseTypeId != null && UseType.fromId(additionalUseTypeId) != null
                ? UseType.fromId(additionalUseTypeId).getLabel()
                : null;
    }

    @Named("mainSpecificUseTypeLabel")
    default String mainSpecificUseTypeLabel(Integer mainSpecificUseTypeId) {
        return  mainSpecificUseTypeId != null && SpecificUseType.fromId(mainSpecificUseTypeId) != null
                ? SpecificUseType.fromId(mainSpecificUseTypeId).getLabel()
                : null;
    }

    @Named("additionalSpecificUseTypeLabel")
    default String additionalSpecificUseTypeLabel(Integer additionalSpecificUseTypeId) {
        return  additionalSpecificUseTypeId != null && SpecificUseType.fromId(additionalSpecificUseTypeId) != null
                ? SpecificUseType.fromId(additionalSpecificUseTypeId).getLabel()
                : null;
    }
}

package com.arealytics.core.mapper;

import java.util.Arrays;
import java.util.Objects;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.domain.empiricalProd.PropertyStrataRelationship;
import com.arealytics.core.dto.response.PropertyStrataDetailsDTO;
import com.arealytics.core.dto.response.PropertyStrataRelationshipResponseDTO;
import com.arealytics.core.enumeration.CondoType;

@Mapper(
        componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PropertyStrataMapper {
    @Mapping(source = "masterProperty.propertyID", target = "masterPropertyId")
    @Mapping(source = "strataProperty.propertyID", target = "strataPropertyId")
    @Mapping(source = "createdBy.entityId", target = "createdBy")
    @Mapping(source = "modifiedBy.entityId", target = "modifiedBy")
    @Mapping(source = "isActive", target = "active")
    PropertyStrataRelationshipResponseDTO toDto(PropertyStrataRelationship entity);

    @Mapping(source = "propertyID", target = "propertyId")
    @Mapping(source = "propertyName", target = "propertyName")
    @Mapping(source = "propertyLocation.addressId.addressText", target = "address")
    @Mapping(source = "propertySize.lotSizeSF", target = "lotSizeSF")
    @Mapping(source = "propertySize.buildingSizeSF", target = "buildingSF")
    @Mapping(target = "master", source="propertyDetails.condoTypeID",
            qualifiedByName = "determineMasterChild")
    @Mapping(source = "propertyDetails.parcelInfo", target = "parcelNumbers")
    @Mapping(source = "propertyDetails.condoUnit", target = "condoUnit")
    @Mapping(
            source = "propertyDetails.condoTypeID",
            target = "strataType",
            qualifiedByName = "getStrataType")
    PropertyStrataDetailsDTO toStrataDetailsDto(Property entity);

    @Named("determineMasterChild")
    default boolean determineMasterChild(CondoType condoType) {
      return Arrays.asList(CondoType.MASTER_FREEHOLD, CondoType.MASTER_STRATA_RECORD).contains(condoType);
    }

    @Named("getStrataType")
    default String getStrataType(CondoType condoType) {
      return Arrays.asList(CondoType.MASTER_FREEHOLD, CondoType.MASTER_STRATA_RECORD).contains(condoType) ? "Master"
          : Objects.equals(condoType, CondoType.STRATA) ? "Strata" : "Child";
    }
}

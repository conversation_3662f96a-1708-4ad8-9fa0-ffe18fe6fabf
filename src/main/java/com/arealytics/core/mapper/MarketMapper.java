package com.arealytics.core.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.arealytics.core.domain.empiricalProd.Market;
import com.arealytics.core.dto.response.MarketInfoDTO;

@Mapper(componentModel = "spring")
public interface MarketMapper {

    // Entity to Response DTO
    @Mapping(target = "marketId", source = "marketId")
    @Mapping(target = "marketName", source = "marketName")
    @Mapping(target = "useTypeId", source = "useType.useTypeId")
    @Mapping(target = "useTypeName", source = "useType.useTypeName")
    MarketInfoDTO toDTO(Market market);
}

package com.arealytics.core.mapper;

import com.arealytics.core.domain.empiricalProd.CompanySearchShapes;
import com.arealytics.core.dto.response.PolygonShapesResponseDTO;
import com.arealytics.core.service.EntityService;
import org.mapstruct.*;

@Mapper(componentModel = "spring")
public interface PolygonShapesMapper {

    @Mapping(target = "companySearchShapeID", source = "companySearchShapeId")
    @Mapping(target = "polygon", source = "polygon", qualifiedByName = "polygonToString")
    @Mapping(target = "createdBy", source = "createdBy.entityId")
    @Mapping(target = "createdByName", source = "createdBy.entityId", qualifiedByName = "getFullNameByEntityId")
    @Mapping(target = "modifiedBy", source = "modifiedBy.entityId")
    @Mapping(target = "modifiedByName", source = "modifiedBy.entityId", qualifiedByName = "getFullNameByEntityId")
    PolygonShapesResponseDTO toDTO(CompanySearchShapes shape, @Context EntityService entityService);

    // Converts Geometry/Polygon object to String 
    @Named("polygonToString")
    default String polygonToString(Object polygon) {
        return polygon != null ? polygon.toString() : null;
    }

    // Custom method to get full name using EntityService
    @Named("getFullNameByEntityId")
    default String getFullNameByEntityId(Integer entityId, @Context EntityService entityService) {
        return entityService.getFullNameByEntityId(entityId);
    }
}

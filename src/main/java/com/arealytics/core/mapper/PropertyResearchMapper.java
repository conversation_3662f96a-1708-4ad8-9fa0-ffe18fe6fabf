package com.arealytics.core.mapper;

import com.arealytics.core.dto.response.PropertyResearchResponseDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueMappingStrategy;

import com.arealytics.core.domain.empiricalProd.PropertyResearchStatus;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL)
public interface PropertyResearchMapper {

    @Mapping(source = "propertyResearchStatusId", target = "propertyResearchStatusID")
    @Mapping(source = "property.propertyID", target = "propertyID")
    @Mapping(source = "createdBy.entityId", target = "createdBy")
    @Mapping(source = "modifiedBy.entityId", target = "modifiedBy")
    @Mapping(source = "isActive", target = "isActive")
    @Mapping(source = "propertyResearchType.propertyResearchTypeId", target = "propertyResearchTypeID")
    @Mapping(source = "propertyResearchType.propertyResearchTypeName", target = "propertyResearchTypeName")
    @Mapping(target = "modifiedPersonName", ignore = true)
    PropertyResearchResponseDTO toDTO(PropertyResearchStatus propertyResearchStatus);

}


package com.arealytics.core.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.NullValueMappingStrategy;

import com.arealytics.core.domain.empiricalProd.GeneralSettings;
import com.arealytics.core.dto.response.GeneralSettingsDTO;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL)
public interface GeneralSettingsMapper {

    GeneralSettingsDTO toDTO(GeneralSettings generalSettings);
}

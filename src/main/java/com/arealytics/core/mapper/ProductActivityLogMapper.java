package com.arealytics.core.mapper;

import org.mapstruct.*;
import com.arealytics.core.domain.empiricalProd.ProductActivityLog;
import com.arealytics.core.dto.request.ProductActivityLogRequestDTO;
import com.arealytics.core.enumeration.ParentTable;

import java.time.LocalDateTime;

@Mapper(componentModel = "spring", imports = { LocalDateTime.class, ParentTable.class })
public interface ProductActivityLogMapper {

    @Mapping(target = "doNotShowAck", source = "isAcknowledged")
    @Mapping(target = "additionalInfo", source = "additionalInfo")
    @Mapping(target = "createdDate", expression = "java(LocalDateTime.now())")
    @Mapping(target = "parentID", source = "articleID")
    @Mapping(target = "parentTableID", expression = "java(dto.getArticleID() != null ? ParentTable.ProductUpdate : null)")
    @Mapping(target = "property", ignore = true)
    @Mapping(target = "entity", ignore = true)
    @Mapping(target = "product", ignore = true)
    ProductActivityLog toEntity(ProductActivityLogRequestDTO dto);
}

package com.arealytics.core.mapper;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueMappingStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.arealytics.core.domain.empiricalProd.ParcelProperty;
import com.arealytics.core.dto.request.ParcelPropertyRequestDTO;
import com.arealytics.core.dto.response.ParcelPropertyDTO;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL)
public interface ParcelPropertyMapper {

    @Mapping(source = "parcel.block", target = "block")
    @Mapping(source = "parcel.lot", target = "lot")
    @Mapping(source = "parcel.parcelId", target = "parcelID")
    @Mapping(source = "parcel.parcelNo", target = "parcelNo")
    @Mapping(source = "propertyId", target = "propertyID")
    @Mapping(source = "parcel.subDivision", target = "subDivision")
    @Mapping(source = "parcel.parcelSize", target = "parcelSize")
    @Mapping(target = "parcelSizeSM", ignore = true)
    ParcelPropertyDTO toDTO(ParcelProperty parcelProperty);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(source = "parcelNo", target = "parcel.parcelNo")
    @Mapping(source = "parcelSF", target = "parcel.parcelSize")
    @Mapping(source = "lot", target = "parcel.lot")
    @Mapping(source = "block", target = "parcel.block")
    @Mapping(source = "subDivision", target = "parcel.subDivision")
    @Mapping(target = "sequence", ignore = true)
    ParcelProperty toEntity(
            ParcelPropertyRequestDTO parcelPropertyRequestDTO,
            @MappingTarget ParcelProperty entity);
}

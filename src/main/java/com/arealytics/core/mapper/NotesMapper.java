package com.arealytics.core.mapper;

import java.util.List;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueMappingStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.arealytics.core.domain.empiricalProd.Notes;
import com.arealytics.core.domain.empiricalProd.NotesMediaRelationship;
import com.arealytics.core.dto.request.NotesRequestDTO;
import com.arealytics.core.dto.response.NotesResponseDTO;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL)
public interface NotesMapper {

    @Mapping(target="isActive", constant="true")
    Notes toEntity(NotesRequestDTO dto);

    NotesResponseDTO toDto(Notes note);

    @Mapping(source = "media.mediaId", target = "mediaId")
    @Mapping(source = "media.path", target = "path")
    NotesResponseDTO.LinkedMediaDTO toLinkedMediaDto(NotesMediaRelationship relationship);

    List<NotesResponseDTO.LinkedMediaDTO> toLinkedMediaDtoList(List<NotesMediaRelationship> relationships);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateNotesFromDto(NotesRequestDTO dto, @MappingTarget Notes note);

}

package com.arealytics.core.mapper;

import com.arealytics.core.domain.empiricalProd.UserPreferences;
import com.arealytics.core.dto.request.UserPreferenceRequestDTO;
import com.arealytics.core.dto.response.UserPreferenceResponseDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import com.arealytics.core.domain.empiricalProd.EntityModel;
import com.arealytics.core.dto.response.UsersDTO;
import com.arealytics.core.enumeration.UserPreferenceType;
import com.arealytics.core.enumeration.UserPreferenceScreen;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL)
public interface UsersMapper {
     UsersMapper INSTANCE = Mappers.getMapper(UsersMapper.class);

    @Mapping(source = "entityId", target = "entityId")
    @Mapping(source = "personId", target = "personId")
    @Mapping(source = "person.firstName", target = "firstName")
    @Mapping(source = "person.lastName", target = "lastName")
    @Mapping(source = "company.altCompanyName", target = "company")
    @Mapping(expression = "java(entity.getRoleId().getLabel())", target = "role")
    @Mapping(expression = "java(entity.getRoleId().getId())", target = "roleId")
    UsersDTO toUsersDTO(EntityModel entity);

    @Mapping(source = "userPreferencesId", target = "userPreferencesID")
    @Mapping(source = "userId", target = "userID")
    @Mapping(source = "data", target = "data", qualifiedByName = "GetDataFromJson")
    @Mapping(source = "type", target = "type", qualifiedByName = "mapToUserPreferenceType") 
    @Mapping(source = "screen", target = "screen", qualifiedByName = "mapToUserPreferenceScreen")
    UserPreferenceResponseDTO toUserPreferenceDTO(UserPreferences userPreferences);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(expression = "java(userPreferenceRequestDTO.getType().getLabel())", target = "type")
    @Mapping(expression = "java(userPreferenceRequestDTO.getScreen().getLabel())", target = "screen")
    UserPreferences toUserPreferencesEntity(UserPreferenceRequestDTO userPreferenceRequestDTO, @MappingTarget UserPreferences entity);

    @Named("GetDataFromJson")
    default List<Map<String, Object>> GetDataFromJson(String data) throws IOException {
      try {
        ObjectMapper mapper = new ObjectMapper();
        List<Map<String, Object>> json = mapper.readValue(data, new TypeReference<List<Map<String, Object>>>() {
        });

        return json;
      } catch (IOException e) {
        throw new RuntimeException("Error parsing updatesJson", e);
      }
    }

    @Named("mapToUserPreferenceType")
    static UserPreferenceType mapToUserPreferenceType(String typeLabel) {
      return UserPreferenceType.fromLabel(typeLabel).orElse(null);
    }

    @Named("mapToUserPreferenceScreen")
    static UserPreferenceScreen mapToUserPreferenceScreen(String screenLabel) {
      return UserPreferenceScreen.fromLabel(screenLabel).orElse(null);
    }
}

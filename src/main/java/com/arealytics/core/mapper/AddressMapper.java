package com.arealytics.core.mapper;

import com.arealytics.core.dto.response.AdditionalAddressDTO;
import com.arealytics.core.dto.response.AddressDTO;
import com.arealytics.core.dto.response.PropertyDetailsDTO;
import com.arealytics.core.dto.request.AdditionalAddressRequestDTO;
import org.mapstruct.*;

import com.arealytics.core.domain.empiricalProd.Address;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL)
public interface AddressMapper {

    // Mapping for creating a new Address
    @Mapping(target = "addressTypeId", constant = "PHYSICAL")
    @Mapping(target = "parentId", source = "propertyId")
    @Mapping(target = "parentTableId", constant = "Property")
    @Mapping(target = "locationId", source = "locationId")
    @Mapping(target = "isActive", constant = "true")
    @Mapping(target = "sequence", constant = "1")
    @Mapping(target = "suffixId", source = "propertyDTO.address.streetSuffix1")
    @Mapping(target = "suffix2Id", source = "propertyDTO.address.streetSuffix2")
    @Mapping(target = "zip4", source = "propertyDTO.address.zip4")
    @Mapping(target = "zipCode", source = "propertyDTO.address.zipCode")
    @Mapping(target = "floorNumber", source = "propertyDTO.address.floorNumber")
    @Mapping(target = "cityId", source = "propertyDTO.address.cityId")
    @Mapping(target = "stateId", source = "propertyDTO.address.stateId")
    @Mapping(target = "countyId", source = "propertyDTO.address.countyId")
    @Mapping(target = "countryId", source = "propertyDTO.address.countryId")
    @Mapping(target = "address1", source = "propertyDTO.address.address1")
    @Mapping(target = "address2", source = "propertyDTO.address.address2")
    @Mapping(target = "streetNumberMin", source = "propertyDTO.address.streetNumberMin")
    @Mapping(target = "streetNumberMax", source = "propertyDTO.address.streetNumberMax")
    @Mapping(target = "eastWestSt", source = "propertyDTO.address.eastWestSt")
    @Mapping(target = "northSouthSt", source = "propertyDTO.address.northSouthSt")
    @Mapping(target = "quadrantId", source = "propertyDTO.address.quadrantID")
    @Mapping(target = "prefixId", source = "propertyDTO.address.streetPrefix1")
    @Mapping(target = "prefix2Id", source = "propertyDTO.address.streetPrefix2")
    @Mapping(target = "addressStreetName", source = "propertyDTO.address.addressStreetName")
    @Mapping(target = "buildingNumber", source = "propertyDTO.address.buildingNumber")
    @Mapping(target = "partOfCenterComplex", source = "propertyDTO.address.partOfCenterComplex")
    @Mapping(target = "complexName", source = "propertyDTO.address.complexName")
    @Mapping(target = "primaryStreet", source = "propertyDTO.address.primaryStreet")
    @Mapping(target = "primaryAccess", source = "propertyDTO.address.primaryAccess")
    @Mapping(target = "primaryTrafficCount", source = "propertyDTO.address.primaryTrafficCount")
    @Mapping(target = "primaryTrafficCountDate", source = "propertyDTO.address.primaryTrafficCountDate")
    @Mapping(target = "primaryFrontage", source = "propertyDTO.address.primaryFrontage")
    @Mapping(target = "secondaryStreet", source = "propertyDTO.address.secondaryStreet")
    @Mapping(target = "secondaryAccess", source = "propertyDTO.address.secondaryAccess")
    @Mapping(target = "secondaryTrafficCount", source = "propertyDTO.address.secondaryTrafficCount")
    @Mapping(target = "secondaryTrafficCountDate", source = "propertyDTO.address.secondaryTrafficCountDate")
    @Mapping(target = "secondaryFrontage", source = "propertyDTO.address.secondaryFrontage")
    @Mapping(target = "isIntersection", source = "propertyDTO.address.addressType")
    @Mapping(
            target = "addressStreetNumber",
            source = "propertyDTO.address",
            qualifiedByName = "buildStreetNumber")
    @Mapping(target = "addressText", source = "propertyDTO.address", qualifiedByName = "buildAddressText")
    @Mapping(
            target = "streetNumberMinN",
            source = "propertyDTO.address.streetNumberMin",
            qualifiedByName = "toNumericStreetNumber")
    @Mapping(
            target = "streetNumberMaxN",
            source = "propertyDTO.address.streetNumberMax",
            qualifiedByName = "toNumericStreetNumber")
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "modifiedBy", ignore = true)
    Address toAddress(PropertyDetailsDTO propertyDTO, Integer locationId, Integer propertyId);

    // Mapping for updating an existing Address
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "sequence", constant = "1")
    @Mapping(target = "suffixId", source = "address.streetSuffix1")
    @Mapping(target = "suffix2Id", source = "address.streetSuffix2")
    @Mapping(target = "zip4", source = "address.zip4")
    @Mapping(target = "zipCode", source = "address.zipCode")
    @Mapping(target = "floorNumber", source = "address.floorNumber")
    @Mapping(target = "cityId", source = "address.cityId")
    @Mapping(target = "stateId", source = "address.stateId")
    @Mapping(target = "countyId", source = "address.countyId")
    @Mapping(target = "countryId", source = "address.countryId")
    @Mapping(target = "address1", source = "address.address1")
    @Mapping(target = "address2", source = "address.address2")
    @Mapping(target = "streetNumberMin", source = "address.streetNumberMin")
    @Mapping(target = "streetNumberMax", source = "address.streetNumberMax")
    @Mapping(target = "eastWestSt", source = "address.eastWestSt")
    @Mapping(target = "northSouthSt", source = "address.northSouthSt")
    @Mapping(target = "quadrantId", source = "address.quadrantID")
    @Mapping(target = "prefixId", source = "address.streetPrefix1")
    @Mapping(target = "prefix2Id", source = "address.streetPrefix2")
    @Mapping(target = "addressStreetName", source = "address.addressStreetName")
    @Mapping(target = "buildingNumber", source = "address.buildingNumber")
    @Mapping(target = "partOfCenterComplex", source = "address.partOfCenterComplex")
    @Mapping(target = "complexName", source = "address.complexName")
    @Mapping(target = "primaryStreet", source = "address.primaryStreet")
    @Mapping(target = "primaryAccess", source = "address.primaryAccess")
    @Mapping(target = "primaryTrafficCount", source = "address.primaryTrafficCount")
    @Mapping(target = "primaryTrafficCountDate", source = "address.primaryTrafficCountDate")
    @Mapping(target = "primaryFrontage", source = "address.primaryFrontage")
    @Mapping(target = "secondaryStreet", source = "address.secondaryStreet")
    @Mapping(target = "secondaryAccess", source = "address.secondaryAccess")
    @Mapping(target = "secondaryTrafficCount", source = "address.secondaryTrafficCount")
    @Mapping(target = "secondaryTrafficCountDate", source = "address.secondaryTrafficCountDate")
    @Mapping(target = "secondaryFrontage", source = "address.secondaryFrontage")
    @Mapping(target = "isIntersection", source = "address.addressType")
    @Mapping(
            target = "addressStreetNumber",
            source = "address",
            qualifiedByName = "buildStreetNumber")
    @Mapping(target = "addressText", source = "address", qualifiedByName = "buildAddressText")
    @Mapping(
            target = "streetNumberMinN",
            source = "address.streetNumberMin",
            qualifiedByName = "toNumericStreetNumber")
    @Mapping(
            target = "streetNumberMaxN",
            source = "address.streetNumberMax",
            qualifiedByName = "toNumericStreetNumber")
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "modifiedBy", ignore = true)
    Address updateAddressFromDto(PropertyDetailsDTO propertyDTO, @MappingTarget Address address);

    @Named("buildStreetNumber")
    default String buildStreetNumber(AddressDTO dto) {
        String min = dto.getStreetNumberMin();
        String max = dto.getStreetNumberMax();
        if (min != null && (max == null || min.equals(max))) {
            return min;
        } else if (min != null && max != null && !max.isEmpty()) {
            return min + "-" + max;
        }
        return min;
    }

    @Named("buildAddressText")
    default String buildAddressText(AddressDTO dto) {
        String streetNumber = buildStreetNumber(dto);
        String addressText =
                (streetNumber != null ? streetNumber : "")
                        + (dto.getAddressStreetName() != null
                        ? " " + dto.getAddressStreetName()
                        : "");
        return addressText.trim();
    }

    @Named("toNumericStreetNumber")
    default Integer toNumericStreetNumber(String streetNumber) {
        if (streetNumber == null) {
            return null;
        }
        try {
            return Integer.parseUnsignedInt(streetNumber);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * Maps a Boolean value to an Integer (1 for true, 0 for false)
     */
    default Integer map(Boolean value) {
        return value == null ? null : (value ? 1 : 0);
    }

    @Mapping( target = "addressTypeId", constant = "PHYSICAL")
    @Mapping(target = "parentTableId", constant = "Property")
    @Mapping(source = "addressType", target = "isIntersection")
    @Mapping(source = "streetSuffix1", target = "suffixId")
    @Mapping(source = "streetSuffix2", target = "suffix2Id")
    @Mapping(source = "streetPrefix1", target = "prefixId")
    @Mapping(source = "streetPrefix2", target = "prefix2Id")
    Address addtionalAddresstoDto(AdditionalAddressRequestDTO additionalAddressDTO);

    @Mapping(source ="state.stateName", target = "stateDisplayName")
    @Mapping(source = "city.cityName", target = "cityDisplayName")
    @Mapping(source = "isIntersection", target = "addressType")
    @Mapping(source= "createdBy.entityId", target="createdBy")
    @Mapping(source= "modifiedBy.entityId", target="modifiedBy")
    @Mapping(source = "prefixId", target = "streetPrefix1")
    @Mapping(source = "prefix2Id", target = "streetPrefix2")
    @Mapping(source = "suffixId", target = "streetSuffix1")
    @Mapping(source = "suffix2Id", target = "streetSuffix2")
    @Mapping(source ="state.stateAbbr", target = "stateAbbr")
    @Mapping(source = "quadrantId", target="quadrantID")
    AdditionalAddressDTO toAdditionalAddressDto(Address address);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(source = "addressType", target = "isIntersection")
    @Mapping(source = "streetSuffix1", target = "suffixId")
    @Mapping(source = "streetSuffix2", target = "suffix2Id")
    @Mapping(source = "streetPrefix1", target = "prefixId")
    @Mapping(source = "streetPrefix2", target = "prefix2Id")
    void updateAdditionalAddressFromDto(AdditionalAddressRequestDTO dto, @MappingTarget Address address);

}



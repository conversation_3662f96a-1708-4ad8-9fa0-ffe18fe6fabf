package com.arealytics.core.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.arealytics.core.domain.empiricalProd.Country;
import com.arealytics.core.dto.response.CountryDTO;

@Mapper(
        componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface  CountryMapper {
    CountryDTO toDto(Country country);
}

package com.arealytics.core.repository.empiricalProd;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.arealytics.core.domain.empiricalProd.PropertyAuditLog;

@Repository
public interface PropertyAuditStatusRepository extends JpaRepository<PropertyAuditLog, Integer>{
     PropertyAuditLog findByPropertyPropertyIDAndIsActiveTrue(Integer propertyId);
}

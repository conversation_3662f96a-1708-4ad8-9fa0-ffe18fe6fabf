package com.arealytics.core.repository.empiricalProd;

import com.arealytics.core.domain.empiricalProd.PropertyResearchType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PropertyResearchTypeRepository extends JpaRepository<PropertyResearchType, Integer> {
    List<PropertyResearchType> findByIsActiveTrue();
}

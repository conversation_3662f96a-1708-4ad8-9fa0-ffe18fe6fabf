package com.arealytics.core.repository.empiricalProd.custom.Impl;


import com.arealytics.core.common.querydsl.QuerydslRepositorySupportBase;
import com.arealytics.core.common.querydsl.caseBuilder.PropertyCaseBuilder;
import com.arealytics.core.common.querydsl.join.CompanyJoinConditions;
import com.arealytics.core.common.querydsl.join.JoinConditions;
import com.arealytics.core.common.querydsl.join.PropertyJoinBuilder;
import com.arealytics.core.common.querydsl.predicate.PredicateConditions;
import com.arealytics.core.common.querydsl.query.PropertyCountExpressions;
import com.arealytics.core.common.querydsl.query.PropertySearchBaseQueryBuilder;
import com.arealytics.core.common.querydsl.queryBuilder.PropertyDetailsFetch;
import com.arealytics.core.common.querydsl.sortBuilder.PropertySortBuilder;
import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.dto.querydsl.*;
import com.arealytics.core.dto.request.PropertyMapSearchRequestDTO;
import com.arealytics.core.dto.request.PropertySearchDetailsRequestDTO;
import com.arealytics.core.dto.request.PropertySearchECRERequestDTO;
import com.arealytics.core.dto.request.PropertySearchRequestDTO;
import com.arealytics.core.dto.response.*;
import com.arealytics.core.enumeration.*;
import com.arealytics.core.enumeration.UseType;
import com.arealytics.core.mapper.PropertyIntersectMapper;
import com.arealytics.core.models.StreetRangeData;
import com.arealytics.core.projection.*;
import com.arealytics.core.repository.empiricalProd.custom.PropertyRepositoryCustom;
import com.arealytics.core.stringTemplates.PropertyStringTemplates;
import com.arealytics.core.utils.EnumUtil;
import com.arealytics.core.utils.EnumUtils;
import com.arealytics.core.utils.NumberUtils;
import com.arealytics.core.utils.UnitConversionUtil;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.*;

import com.querydsl.core.types.dsl.*;
import com.querydsl.core.types.dsl.CaseBuilder.Initial;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.arealytics.core.utils.SqlExpressionUtil;

import jakarta.persistence.EntityManager;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.ParseException;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.arealytics.core.common.querydsl.join.JoinConditions.*;
import static com.arealytics.core.common.querydsl.join.JoinConditions.JoinEntityOnEntityContact;
import static com.arealytics.core.common.querydsl.query.PropertySearchBaseQueryBuilder.*;
import static com.querydsl.core.types.dsl.Expressions.stringTemplate;

public class PropertyRepositoryImpl extends QuerydslRepositorySupportBase<Property>
        implements PropertyRepositoryCustom {

    private final JPAQueryFactory jpaQueryFactory;
    private final PropertyIntersectMapper propertyIntersectMapper;

    private final QProperty property = QProperty.property;
    private final QAddress address = QAddress.address;
    private final QAddress branchAddress = new QAddress("branch");
    private final QLocation location = QLocation.location;
    private final QCity city = QCity.city;
    private final QState state = QState.state;
    private final QCity branchCity = new QCity("branchCity");
    private final QState branchState = new QState("branchState");
    private final QUse use = QUse.use;
    private final QMarket market = QMarket.market;
    private final QSpecificUses specificUses = QSpecificUses.specificUses;
    private final QCounty county = QCounty.county ;
    private final QCountry country = QCountry.country ;
    private final QSubMarket subMarket = QSubMarket.subMarket;
    private final QPropertyStrataRelationship propertyStrataRelationship = QPropertyStrataRelationship.propertyStrataRelationship;
    private final QAmenities amenities = QAmenities.amenities;
    private final QStatusDefination statusDefination = QStatusDefination.statusDefination;
    private final QUseType useType = QUseType.useType;
    private final QListingforLease listingforLease = QListingforLease.listingforLease;
    private final QListingforSale listingforSale = QListingforSale.listingforSale;
    private final QZipCode zipcode = QZipCode.zipCode1;
    private final QSale sale = QSale.sale;
    private final QGroupRelationship groupRelationship = QGroupRelationship.groupRelationship;
    private final QSaleGroup saleGroup = QSaleGroup.saleGroup;
    private final QListing listing = QListing.listing;
    private final QListingEntity listingEntity = QListingEntity.listingEntity;
    private final QListingGroup listingGroup = QListingGroup.listingGroup;
    private final QEntityModel listingAgent = new QEntityModel("listingAgent");
    private final QPerson listingAgentPerson = new QPerson("listingAgentPerson");
    private final QCompany listingBranch = new QCompany("listingBranch");
    private final QCompany company = QCompany.company;
    private final QLeaseType leaseType = QLeaseType.leaseType;
    private final QSuite suite = QSuite.suite;
    //private final QContigSuitesListings sContig = QContigSuitesListings.contigSuitesListings;
    private final QCompanySupport companySupport = QCompanySupport.companySupport;
    // Subquery for OtherBrokers
    private final QListingEntity leSub = new QListingEntity("leSub");
    private final QEntityModel eSub = new QEntityModel("eSub");
    private final QPerson epSub = new QPerson("epSub");
    private final QSpaceType spaceType = QSpaceType.spaceType;
    private final QFloor floor = QFloor.floor;
    private final QSuiteContigBlocks suiteContigBlocks = QSuiteContigBlocks.suiteContigBlocks;
    private final QSuiteContigs suiteContigs = QSuiteContigs.suiteContigs;
    private final QSuiteContigs suiteContigsMax = new QSuiteContigs("maxSuite");
    private final QEntityModel entity = QEntityModel.entityModel;
    private final QCompanyRelationship companyRelationship = QCompanyRelationship.companyRelationship;
    private final QEntityContact entityContactOffice = new QEntityContact("entityOffice");
    private final QEntityContact entityContactMobile = new QEntityContact("entityMobile");
    private final QEntityContact entityContactEmail = new QEntityContact("entityEmail");
    private final QEntityContact entityContactDirectWorkPhone = new QEntityContact("entityDirectWorkPhone");
    private final QCompanyContact companyContactOffice = new QCompanyContact("companyOffice");
    private final QCompanyContact companyContactEmail = new QCompanyContact("companyEmail");
    private final QGroupEntity groupEntity = QGroupEntity.groupEntity;

    private static final QPerson person = QPerson.person;

    private final PredicateConditions predicateBuilder = new PredicateConditions();
    public PropertyRepositoryImpl(EntityManager entityManager, JPAQueryFactory queryFactory, PropertyIntersectMapper propertyIntersectMapper) {
        super(Property.class, queryFactory);
        this.jpaQueryFactory = new JPAQueryFactory(entityManager);
        this.propertyIntersectMapper = propertyIntersectMapper;
    }

    @Override
    public PropertyDetailsQuerydslDTO findPropertyDetailsByPropertyID(Integer propertyId) {
        BooleanBuilder predicate = predicateBuilder.buildPropertyDetailsPredicate(List.of(propertyId), property);
        JPQLQuery<PropertyDetailsQuerydslDTO> query = PropertyDetailsFetch.buildPropertyDetailsFetchByPropertyIDQuery(getQueryFactory(), predicate);
        return  query.fetchOne();
    }

    @Override
    public List<PropertyDetailsQuerydslDTO> fetchChildPropertyDetailsByPropertyIDs(List<Integer> propertyIds) {
        BooleanBuilder predicate = predicateBuilder.buildPropertyDetailsPredicate(propertyIds, property);
        JPQLQuery<PropertyDetailsQuerydslDTO> query = PropertyDetailsFetch.buildPropertyDetailsFetchByPropertyIDQuery(getQueryFactory(), predicate);
        return  query.fetch();
    }

    @Override
    public List<Integer> getChildPropertyIdsByMasterId(Integer masterPropertyId) {
        JPQLQuery<Integer> query = PropertyDetailsFetch.buildChildPropertyIdsByMasterIdQuery(getQueryFactory(), masterPropertyId);
        return  query.fetch();
    }

    public List<PropertySearchAllPropertiesQuerydslDTO> findPropertiesBySearch(PropertySearchRequestDTO searchCriteria, Pageable pageable) {
        BooleanBuilder predicate = predicateBuilder.searchBuild(searchCriteria, property, address, city, state, statusDefination);

        return buildSearchBaseQuery(jpaQueryFactory,predicate)
                        .select(Projections.constructor(
                                        PropertySearchAllPropertiesQuerydslDTO.class,
                                        property.propertyID,
                                        property.propertyName,
                                        property.propertyDetails.condoTypeID,
                                        property.propertyDetails.useTypeID,
                                        useType.useTypeName,
                                        property.propertyDetails.specificUseID,
                                        property.propertyDetails.specificUseName,
                                        property.propertySize.buildingSize,
                                        property.propertySize.buildingSizeSF,
                                        AddressProjection.projectAddressDTO(),
                                        LocationProjection.projectLocationDTO(),
                                        AllPropertiesProjection.projectAllProprtiesDTO(),
                                        city.cityName
                                ))
                                .offset(pageable.getOffset())
                                .limit(pageable.getPageSize())
                .fetch();

    }

    public Long countBySearchCriteria(PropertySearchRequestDTO searchCriteria) {

        BooleanBuilder predicate = predicateBuilder.searchBuild(searchCriteria, property, address, city, state, statusDefination);

        return buildSearchBaseQuery(jpaQueryFactory,predicate)
                .select(property.count())
                .fetchOne();
    }

    public List<PropertyMapSearchQuerydslDTO> findPropertiesByMapSearch(PropertyMapSearchRequestDTO searchCriteria, Pageable pageable) {

        BooleanBuilder predicate = predicateBuilder.mapSearchBuild(searchCriteria, property, address, location);

        return buildSearchBaseQuery(jpaQueryFactory, predicate)
                .select(Projections.constructor(
                        PropertyMapSearchQuerydslDTO.class,
                                        property.propertyID,
                                        property.propertyName,
                                        property.propertyDetails.yearBuilt,
                                        property.propertyDetails.condoTypeID,
                                        property.propertyDetails.condoUnit,
                                        property.propertyDetails.useTypeID,
                                        property.propertyDetails.useTypeName,
                                        property.propertyDetails.specificUseID,
                                        property.propertyDetails.specificUseName,
                                        property.propertySize.buildingSizeSF,
                                        property.propertySize.lotSizeSF,
                                        property.propertySize.contributedGBASizeSF,
                                        property.internalToolFields.researchTypeID,
                                        property.internalToolFields.researchTypeName,
                                        property.propertyDetails.mainPhotoUrl,
                                        property.propertyDetails.hasNoBuildingFootprints,
                                        AddressProjection.projectAddressDTO(),
                                        LocationProjection.projectLocationDTO(),
                                        city.cityName,
                                        state.stateAbbr,
                                        country.alpha3Code,
                                        property.propertyDetails.parcelInfo,
                                        property.internalToolFields.trueOwners,
                                        property.internalToolFields.recordedOwners,
                                        property.propertyDetails.classTypeID,
                                        property.propertyDetails.constructionStatusID
                                ))
                                .offset(pageable.getOffset())
                                .limit(pageable.getPageSize())
                                .fetch();

      }

    public List<MasterPropertiesDTO> findMasterPropertiesBySearch(String searchText, String unit,
          CondoType strataType) {
                	
            QProperty strataProperty = new QProperty("strataProperty");
            SubQueryExpression<String> lastStrataUnitSubquery = JPAExpressions
                .select(strataProperty.propertyDetails.condoUnit.max())
                .from(propertyStrataRelationship)
                .join(strataProperty)
                .on(strataProperty.propertyID.eq(propertyStrataRelationship.strataProperty.propertyID))
                .where(propertyStrataRelationship.masterProperty.propertyID.eq(property.propertyID));
            BooleanBuilder predicate = predicateBuilder.masterSearchBuilder(searchText, unit, strataType, property, address);

        return fetch(
            factory -> factory.select(Projections.constructor(MasterPropertiesDTO.class,
                property.propertyID,
                property.propertyName,
                address.addressText,
                city.cityId,
                city.cityName,
                state.stateId,
                state.stateName,
                state.stateAbbr,
                country.countryId,
                country.countryName,
                address.zipCode,
                            useType.useTypeName.as("propertyUse"),
                property.propertyDetails.parcelInfo,
                property.propertySize.buildingSizeSF.as("buildingSF"),
                UnitConversionUtil.sqftToSqmConversion( property.propertySize.buildingSizeSF).as("buildingSM"),
                SqlExpressionUtil.concat(true, property.propertyName, property.propertyID.stringValue())
                    .as("propertyNameDisplay"),
                property.internalToolFields.researchTypeID.as("researchTypeID"),
                property.internalToolFields.researchTypeName.as("researchTypeName"),
                lastStrataUnitSubquery))
                .from(property)
                .join(property.addresses, address)
                .on(joinAddressOnPropertyPrimaryAddress(address, property))
                .join(address.city, city)
                .join(address.state, state)
                .leftJoin(country).on(JoinCountryOnAddress(country, address))
                    .leftJoin(useType).on(JoinUseTypeOnProperty(useType, property))
                .where(predicate));
    }

    public Long countByMapSearchCriteria(PropertyMapSearchRequestDTO searchCriteria) {

        BooleanBuilder predicate = predicateBuilder.mapSearchBuild(searchCriteria, property, address, location);

        return buildSearchBaseQuery(jpaQueryFactory,predicate)
                .select(property.count())
                .fetchOne();
    }

    public List<PropertySearchQuerydslDTO> findStrataPropertiesByMasterStrataProperty(Integer masterProperty) {


        BooleanBuilder predicate = new BooleanBuilder();
        predicate = predicate.and(propertyStrataRelationship.isActive.eq(true).and(propertyStrataRelationship.masterPropertyId.eq(masterProperty)));

        return buildSearchBaseQuery(jpaQueryFactory,predicate)
                .select(Projections.constructor(PropertySearchQuerydslDTO.class,
                        property.propertyID,
                        property.propertyName,
                        property.isActive,
                        property.propertyDetails.yearBuilt,
                        property.propertyDetails.floors,
                        property.propertyDetails.condoTypeID,
                        property.propertyDetails.condoUnit,
                        property.propertyDetails.useTypeID,
                        useType.useTypeName,
                        property.propertyDetails.specificUseID,
                        property.propertyDetails.specificUseName,
                        property.propertyDetails.buildingComments,
                        property.propertyDetails.lastReviewedBy,
                        property.propertyDetails.lastReviewedDate,
                        property.propertySize.buildingSize,
                        property.propertySize.lotSize,
                        property.propertySize.buildingSizeSF,
                        property.propertySize.lotSizeSF,
                        property.propertySize.contributedGBASizeSF,
                        property.internalToolFields.researchTypeID,
                        property.internalToolFields.researchTypeName,
                        property.internalToolFields.isSkipped,
                        property.propertyDetails.mainPhotoUrl,
                        property.propertyDetails.hasNoBuildingFootprints,
                        property.propertyDetails.hasNoExistingParcelInTileLayer,
                        propertyStrataRelationship.masterPropertyId,
                        AddressProjection.projectAddressDTO(),
                        LocationProjection.projectLocationDTO(),
                        property.createdDate,
                        property.modifiedDate,
                        property.createdBy.entityId,
                        property.modifiedBy.entityId,
                        statusDefination.statusName,
                        city.cityName))
                .fetch();
    }

    public List<PropertyLeaseSearchQuerydslDTO> findPropertiesByLeaseSearch(PropertySearchRequestDTO searchCriteria, Pageable pageable) {

        BooleanBuilder predicate = predicateBuilder.leaseSearchBuild(searchCriteria, property, address, city, state, listing,listingAgent,listingEntity,listingforLease,companySupport);

        return PropertySearchBaseQueryBuilder.buildLeaseSearchBaseQuery(jpaQueryFactory,predicate)
                .select(Projections.constructor(
                        PropertyLeaseSearchQuerydslDTO.class,
                        property.propertyID,
                        property.propertyName,
                        property.propertyDetails.condoTypeID,
                        property.propertyDetails.useTypeID,
                        useType.useTypeName,
                        property.propertyDetails.specificUseID,
                        property.propertyDetails.specificUseName,
                        property.propertySize.buildingSize,
                        property.propertySize.buildingSizeSF,
                        listing.listingId,
                        leaseType.leaseTypeName,
                        property.modifiedDate,
                        listing.recordTypeId,
                        state.stateName,
                        listing.totalAvailable,
                        AddressProjection.projectAddressDTO(),
                        LocationProjection.projectLocationDTO(),
                        LeasePropertiesProjection.projectLeaseProprtiesDTO(),
                        city.cityName
                ))
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetch();

    }

    public Long countByLeaseSearchCriteria(PropertySearchRequestDTO searchCriteria) {

        BooleanBuilder predicate = predicateBuilder.leaseSearchBuild(searchCriteria, property, address, city, state, listing,listingAgent,listingEntity,listingforLease,companySupport);

        List<Long> groupedListings = PropertySearchBaseQueryBuilder.buildLeaseSearchBaseQuery(jpaQueryFactory,predicate)
                .select(property.count())
                .fetch();

        Integer groupCount = groupedListings.size();

        return groupCount.longValue();
    }

    @Override
    public List<PropertySaleSearchQuerydslDTO> findPropertiesBySaleSearch(PropertySearchRequestDTO searchCriteria, Pageable pageable) {
        BooleanBuilder predicate = predicateBuilder.saleSearchBuild(searchCriteria, property, useType, use, listing, state, address, suite, listingAgent, listingEntity, companySupport, listingforSale);
        return PropertySearchBaseQueryBuilder.buildSaleSearchQuery(jpaQueryFactory, predicate).select(
                Projections.constructor(
                                PropertySaleSearchQuerydslDTO.class,
                                property.propertyID,
                                PropertyStringTemplates.getPropertyName(property, address),
                                property.propertyDetails.useTypeID,
                                useType.useTypeName,
                                property.propertyDetails.specificUseID,
                                specificUses.specificUsesName,
                                property.propertySize.buildingSizeSF,
                                UnitConversionUtil.sqftToSqmConversion(property.propertySize.buildingSizeSF),
                                property.propertyDetails.condoTypeID,
                                listing.recordTypeId,
                                PropertyStringTemplates.getAgentName(listingAgentPerson).as("agentName"),
                                Expressions.nullExpression(String.class),
                                listingforSale.askingSalePrice,
                                Expressions.nullExpression(String.class),
                                PropertyStringTemplates.getCompanyName(listingBranch).as("companyName"),
                                listing.listingId,
                                Expressions.numberTemplate(Integer.class, "DATEDIFF(current_timestamp(), {0})", listing.modifiedDate),
                                listing.modifiedDate,
                                Expressions.numberTemplate(BigDecimal.class, "ROUND({0}, 2)", listingforSale.salePricePerSF),
                                UnitConversionUtil.sqftToSqmConversion(listingforSale.salePricePerSF),
                                Expressions.numberTemplate(BigDecimal.class, "ROUND({0}, 0)", listing.totalAvailable),
                                UnitConversionUtil.sqftToSqmConversion(listing.totalAvailable),
                                AddressProjection.projectAddressDTO(),
                                LocationProjection.projectLocationDTO(),
                                SalePropertiesProjection.projectSalePropertiesDTO()
                        )).offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetch();

    }

    @Override
    public Long countBySaleSearchCriteria(PropertySearchRequestDTO searchCriteria) {

        BooleanBuilder predicate = predicateBuilder.saleSearchBuild(searchCriteria, property, useType, use, listing, state, address, suite, listingAgent, listingEntity, companySupport, listingforSale);
        return (long) buildSaleSearchQuery(jpaQueryFactory, predicate)
                .select(property.propertyID, listing.listingId)
                .groupBy(property.propertyID, listing.listingId)
                .fetch()
                .size();
    }

    public List<PropertySearchLeaseAndSaleERCQuerydslDTO> findPropertiesByLeaseAndSaleSearch(
            PropertySearchRequestDTO searchCriteria, Pageable pageable) {

        BooleanBuilder predicate = predicateBuilder.leaseAndSaleSearchERCBuild(searchCriteria, property, address, city,
                state, listing, listingAgent, listingEntity, listingforLease, listingforSale, companySupport,
                groupRelationship, listingBranch);

        return PropertySearchBaseQueryBuilder.buildERCLeaseAndSaleBaseQuery(jpaQueryFactory, predicate, searchCriteria).select(
                Projections.constructor(
                        PropertySearchLeaseAndSaleERCQuerydslDTO.class,
                        property.propertyID,
                        PropertyCaseBuilder.getPropertyName(property, address),
                        property.propertyDetails.useTypeID,
                        useType.useTypeName,
                        property.propertyDetails.specificUseID,
                        property.propertyDetails.specificUseName,
                        property.propertySize.buildingSizeSF,
                        UnitConversionUtil.sqftToSqmConversion(property.propertySize.buildingSizeSF),
                        property.propertyDetails.condoTypeID,
                        listing.recordTypeId,
                        PropertyStringTemplates.getAgentName(listingAgentPerson).as("agentName"),
                        listingforSale.askingSalePrice,
                        PropertyStringTemplates.getCompanyName(listingBranch).as("listingCompanyName"),
                        listing.listingId,
                        PropertyStringTemplates.getModDateCounter(listing).as("modDateCounter"),
                        PropertyStringTemplates.getPrice(listingforSale, listingforLease).as("price"),
                        PropertyStringTemplates.getFormattedModifiedDate(listing).as("modifiedDate"),
                        PropertyStringTemplates.getRoundedSalePrice(listingforSale).as("salePricePerSF"),
                        UnitConversionUtil.sqftToSqmConversion(listingforSale.salePricePerSF).as("salePricePerSM"),
                        PropertyStringTemplates.getTotalAvailableRounded(listing).as("totalAvailable"),
                        UnitConversionUtil.sqftToSqmConversion(listing.totalAvailable).as("totalAvailableSM"),
                        AddressProjection.projectAddressDTO(),
                        LocationProjection.projectLocationDTO(),
                        LeaseAndSalePropertyProjection.projectLeaseAndSaleProprtiesDTO()))
                .groupBy(listing.listingId)
                .orderBy(listing.recordTypeId.asc(), listing.listingId.asc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetch();

    }

   public List<PropertySearchSaleListingResponseDTO> findSaleListingByLeaseAndSaleSearch(PropertySearchRequestDTO searchCriteria){
        BooleanBuilder predicate = predicateBuilder.saleListingLeaseAndSaleSearch(searchCriteria, property, address, city,
                state, listing, listingAgent, listingEntity, listingforLease, listingforSale, companySupport,
                groupRelationship, listingBranch, use);

        return PropertySearchBaseQueryBuilder.buildSaleListingQueryForLeaseAndSale(jpaQueryFactory, predicate, searchCriteria).select(
                Projections.constructor(
                        PropertySearchSaleListingResponseDTO.class,
                        property.propertyID,
                        listing.listingId,
                        PropertyCaseBuilder.getPropertyName(property, address),
                        address.addressText,
                        city.cityName,
                        listing.listingStatus.listingStatusName,
                        listing.listingStatus.listingStatusId,
                        useType.useTypeId,
                        useType.useTypeName,
                        listingforSale.condoUnit,
                        listingforSale.saleSize,
                        UnitConversionUtil.sqftToSqmConversion(listingforSale.saleSize),
                        listingforSale.askingSalePrice,
                        listing.vacant,
                        EnumUtils.getIdCaseFromEnumPath(listingforSale.saleTypeId, SaleType.class),
                        EnumUtils.getLabelCaseFromEnumPath(listingforSale.saleTypeId, SaleType.class),
                        PropertyStringTemplates.getCompanyName(listingBranch).as("companyName"),
                        PropertyStringTemplates.getAgentName(listingAgentPerson).as("agentName"),
                        PropertyStringTemplates.getFormattedModifiedDate(listing).as("modifiedDate"),
                        listingforSale.exclFromAvailability,
                        Expressions.nullExpression(Integer.class),
                        Expressions.nullExpression(Boolean.class),
//                        companySupport.supportEntityId.entityId,
                        Expressions.nullExpression(Integer.class),
                        PropertyStringTemplates.getModDateCounter(listing).as("modDateCounter")))
                .groupBy(listing.listingId)
                .fetch();
    }

    public List<PropertySearchSuiteResponseDTO> findSuitesByLeaseAndSaleSearch(PropertySearchRequestDTO searchCriteria) {
        BooleanBuilder predicate = predicateBuilder.leaseAndSaleSearchERCBuild(searchCriteria, property, address, city,
                state, listing, listingAgent, listingEntity, listingforLease, listingforSale, companySupport,
                groupRelationship, listingBranch);

        return buildSuiteQueryForLeaseAndSale(jpaQueryFactory, predicate, searchCriteria).select(
                        Projections.constructor(
                                PropertySearchSuiteResponseDTO.class,
                                property.propertyID,
                                listing.listingId,
                                listingGroup.suite.suiteID,
                                PropertyCaseBuilder.getPropertyName(property, address),
                                address.addressText,
                                city.cityName,
                                use.useType.useTypeId,
                                use.useType.useTypeName,
                                EnumUtils.getLabelCaseFromEnumPath(suite.suiteUseID,SpaceUseType.class),
                                EnumUtils.getIdCaseFromEnumPath(suite.suiteUseID, SpaceUseType.class),
                                EnumUtils.getIdCaseFromEnumPath(suite.suiteStatusID, SuiteStatus.class),
                                EnumUtils.getLabelCaseFromEnumPath(suite.suiteStatusID,SuiteStatus.class),
                                suite.spaceTypeID.spaceTypeId,
                                spaceType.spaceTypeName,
                                suite.suiteNumber,
                                suite.availableSF,
                                UnitConversionUtil.sqftToSqmConversion(suite.availableSF),
//                                PropertyStringTemplates.buildAskingRateText(suite, leaseType),
                                Expressions.nullExpression(String.class),
                                suite.minSF,
                                UnitConversionUtil.sqftToSqmConversion(suite.minSF),
                                suite.exclAvailable,
                                PropertyCaseBuilder.isContiguous(suiteContigs),
                                suite.tiAllowance,
                                PropertyStringTemplates.getCompanyName(listingBranch).as("companyName"),
                                PropertyStringTemplates.getAgentName(listingAgentPerson).as("agentName"),
                                PropertyStringTemplates.getFormattedModifiedDate(listing).as("modifiedDate"),
                                suite.isVacant,
                                suite.askingRateHigh,
                                suite.askingRateLow,
                                Expressions.nullExpression(Integer.class),
                                Expressions.nullExpression(Boolean.class),
                                PropertyStringTemplates.getModDateCounter(listing).as("modDateCounter"),
                                suite.floorID))
                .groupBy(listing.listingId)
                .fetch();
    }

    public Long countByLeaseAndSaleSearchCriteria(PropertySearchRequestDTO searchCriteria) {

        BooleanBuilder predicate = predicateBuilder.leaseAndSaleSearchERCBuild(searchCriteria, property, address, city, state, listing, listingAgent, listingEntity, listingforLease, listingforSale, companySupport, groupRelationship, listingBranch);

        return (long) buildERCLeaseAndSaleBaseQuery(jpaQueryFactory, predicate, searchCriteria)
                .select(property.propertyID, listing.listingId)
                .groupBy(property.propertyID, listing.listingId)
                .fetch()
                .size();
    }

    @Override
    public List<PropertyIntersectResponseDTO> findMarketsAndSubMarketsByPoint(Point point, Integer useTypeId, Integer propertyID) {

        BooleanBuilder predicate = null;
        if(propertyID != null) {
            predicate = predicateBuilder.buildMarketPredicate(market, use, property, useTypeId, propertyID, point);
        } else {
            predicate = predicateBuilder.buildSubMarketPredicate(subMarket, market, useTypeId, point);
        }
        JPQLQuery<PropertyIntersectResponseDTO> query = PropertyDetailsFetch.buildMetroIdFromMarketsAndSubMarketsByPoint(getQueryFactory(), propertyID, point, predicate);
        return  query.fetch();
    }

    public List<PropertyElasticSearchResponseDTO> findPropertiesByElasticSearch(String text, Integer stateId,
        Boolean isElasticSearch, Integer limit) {

      // Split the input text by "/" and extract the searchable text
      final String[] searchValues = text.split("/");
      String searchText = searchValues.length > 1 ? searchValues[1] : searchValues[0];

      // Normalize range if present in the search text (e.g., "100-200")
      if (searchText.contains("-")) {
        String[] rangeParts = searchText.split("-");
        if (rangeParts.length == 2) {
          searchText = rangeParts[0].trim() + "-" + rangeParts[1].trim();
        }
      }

      // Extract street range information (min, max, street name, odd/even) from search text
      StreetRangeData streetRange = parseStreetRange(searchText);

      Expression<String> propertyNameDisplay = stringTemplate(
          "CONCAT({0}, CONCAT(' (', CONCAT({1}, ')')))",
          property.propertyName, property.propertyID);

      // Build the dynamic BooleanBuilder predicate for filtering data
      BooleanBuilder predicate = predicateBuilder.buildPropertyElasticSearchPredicate(
          stateId,
          searchText,
          streetRange.getMin(),
          streetRange.getMax(),
          streetRange.getStreetName(),
          streetRange.getIsOdd(),
          property,
          address,
          zipcode,
          state,
          isElasticSearch);

      return fetch(factory -> factory.select(Projections.constructor(PropertyElasticSearchResponseDTO.class,
          address.addressText.concat(", ").concat(city.cityName).concat(", ").concat(address.zipCode),
          address.addressText,
          city.cityId,
          city.cityName,
          property.propertyDetails.condoTypeID,
          property.propertyID,
          property.propertyName,
          propertyNameDisplay,
          state.stateAbbr,
          state.stateId,
          state.stateName))
          .from(property)
          .join(property.addresses, address)
          .join(address.city, city)
          .join(address.state, state)
          .join(zipcode).on(zipcode.zipCode.eq(address.zipCode.castToNum(Integer.class)))
          .where(predicate)
          .groupBy(property.propertyID)
          .limit(limit)
          .orderBy(
              property.propertyDetails.condoTypeID.desc(),
              property.propertyDetails.condoUnit.asc(),
              address.streetNumberMin.asc(),
              address.streetNumberMax.asc()));
    }

    public List<PropertyECRESearchQuerydslDTO> findPropertiesByECRESearch(PropertySearchECRERequestDTO searchCriteria, Pageable pageable) throws ParseException {

        BooleanBuilder predicate = predicateBuilder.ecreSearchBuild(searchCriteria, property, address, amenities,location,null,null,null,null,null);

        return buildECRESearchBaseQuery(jpaQueryFactory,predicate,searchCriteria)
                .select(Projections.constructor(
                        PropertyECRESearchQuerydslDTO.class,
                        property.propertyID,
                        property.propertyDetails.condoTypeID,
                        property.propertyDetails.useTypeID,
                        useType.useTypeName,
                        location.latitude,
                        location.longitude,
                        propertyStrataRelationship.masterPropertyId,
                        Expressions.nullExpression(Integer.class)
                ))
                .distinct()
                .fetch();
    }

    public Long countByECRESearchCriteria(PropertySearchECRERequestDTO searchCriteria) throws ParseException {

        BooleanBuilder predicate = predicateBuilder.ecreSearchBuild(searchCriteria, property, address, amenities,location,null,null,null,null,null);

        return buildECRESearchBaseQuery(jpaQueryFactory,predicate,searchCriteria)
                .select(property.count())
                .fetchOne();
    }

    public List<PropertyECRESearchQuerydslDTO> findPropertiesByECRESearchForLeaseAndSale(PropertySearchECRERequestDTO searchCriteria, Pageable pageable) throws ParseException {

        BooleanBuilder predicate = predicateBuilder.ecreSearchBuild(searchCriteria, property, address, amenities,location,listing,listingforSale,listingforLease,listingEntity,listingAgent);

        return buildECRESearchBaseQueryforLeaseAndSale(jpaQueryFactory,predicate,searchCriteria)
                .select(Projections.constructor(
                        PropertyECRESearchQuerydslDTO.class,
                        property.propertyID,
                        property.propertyDetails.condoTypeID,
                        property.propertyDetails.useTypeID,
                        useType.useTypeName,
                        location.latitude,
                        location.longitude,
                        propertyStrataRelationship.masterPropertyId,
                        listing.listingId
                ))
                .distinct()
                .fetch();
    }

    public Long countByECRESearchCriteriaForLeaseAndSale(PropertySearchECRERequestDTO searchCriteria) throws ParseException {

        BooleanBuilder predicate = predicateBuilder.ecreSearchBuild(searchCriteria, property, address, amenities,location,listing,listingforSale,listingforLease,listingEntity,listingAgent);

        return (long) buildECRESearchBaseQueryforLeaseAndSale(jpaQueryFactory, predicate, searchCriteria)
                .select(property.propertyID, listing.listingId)
                .groupBy(property.propertyID, listing.listingId)
                .fetch()
                .size();
    }

    public List<FullStrataDetailResponseDTO> findLinkedPropertyDetails(List<Integer> propertyIds,
        StrataDetailsSortBy sortBy, QuerySortDirection direction) {

      // Boolean expression to determine if a property is a 'Master' type
      final BooleanExpression isMaster = property.propertyDetails.condoTypeID.in(
          CondoType.MASTER_FREEHOLD,
          CondoType.MASTER_STRATA_RECORD);

      // Determine the type of strata: Master, Strata, or Child
      final StringExpression strataType = PropertyCaseBuilder.getStrataTypeFromProperty(property, isMaster);

      // Determine the owner string value based on available data
      final StringExpression owner = PropertyCaseBuilder.getOwnerFromProperty(property);

      // Determine owner type label
      final StringExpression ownerType = PropertyCaseBuilder.getOwnerTypeFromProperty(property);

      // Generate sorting logic based on user input
      final OrderSpecifier<?> orderBy = PropertySortBuilder.getOrderForStrataDetails(
          sortBy, direction, property, address, sale, strataType);

      // Create base query with selected fields mapped to DTO
      JPAQuery<FullStrataDetailResponseDTO> query = PropertyJoinBuilder.JoinSaleOnPropertyWithSaleGroup(
          property,
          groupRelationship,
          saleGroup,
          sale,
          jpaQueryFactory.select(Projections.constructor(
                  FullStrataDetailResponseDTO.class,
              property.propertyID,
              property.propertyName,
              address.addressText,
              property.propertyDetails.condoUnit,
              property.propertyDetails.useTypeName,
              property.propertySize.lotSizeSF,
              UnitConversionUtil.sqftToSqmConversion(property.propertySize.lotSizeSF),
              property.propertySize.buildingSizeSF,
              UnitConversionUtil.sqftToSqmConversion(property.propertySize.buildingSizeSF),
              isMaster,
              strataType,
              owner,
              ownerType,
              property.propertyDetails.parcelInfo,
              sale.saleId,
              sale.deedOrSaleDate.max(),
              sale.soldPrice,
              property.propertyDetails.floors,
              UnitConversionUtil.sqftToSqmConversion(property.propertySize.totalAvailableSF),
              UnitConversionUtil.sqftToSqmConversion(property.propertySize.totalSaleSizeSF),
              property.propertyDetails.hasNoBuildingFootprints,
              property.propertySize.contributedGBASizeSF,
              UnitConversionUtil.sqftToSqmConversion(property.propertySize.contributedGBASizeSF)))
          .from(property));

      // Finalize query with address join, filters, group and sort
      return query
          .join(property.addresses, address)
          .on(joinAddressOnPropertyPrimaryAddress(address, property))
          .where(property.propertyID.in(propertyIds))
          .groupBy(property.propertyID)
          .orderBy(orderBy)
          .fetch();
    }

    @Override
    public List<PropertySearchDetailsResponseDTO> searchProperties(PropertySearchDetailsRequestDTO requestDTO, int offset, int limit) {
        // Get base query
        JPAQuery<?> baseQuery = PropertySearchBaseQueryBuilder.buildBasePropertySearchQuery(jpaQueryFactory, requestDTO);

        // Use centralized count expressions
        NumberExpression<Long> lCount = PropertyCountExpressions.getLCount(property, listing, listingGroup);
        NumberExpression<Long> llCount = PropertyCountExpressions.getLLCount(property, listing, listingGroup, listingforLease);
        NumberExpression<Long> slCount = PropertyCountExpressions.getSLCount(property, listing, listingGroup, listingforSale);
        NumberExpression<Long> dlCount = PropertyCountExpressions.getDLCount(property, listing, listingGroup);

        // Apply projection
        JPAQuery<PropertySearchDetailsResponseDTO> query = baseQuery.select(Projections.bean(PropertySearchDetailsResponseDTO.class,
                property.propertyID.as("propertyID"),
                PropertyCaseBuilder.getPropertyNameFromPropertyOrAddress(property,address)
                        .as("propertyName"),
                city.cityName.as("city"),
                county.countyName.as("county"),
                address.zipCode.as("zipCode"),
                useType.useTypeName.as("propertyType"),
                specificUses.specificUsesName.as("specificUses"),
                address.addressText.as("address"),
                property.propertySize.buildingSize.as("buildingSize"),
                stringTemplate("CAST(ROUND({0} * 0.092903, 0) AS STRING)", property.propertySize.buildingSize)
                        .as("buildingSizeSM"),
                Expressions.numberTemplate(BigDecimal.class, "COALESCE({0}, {1})", location.longitude, location.longitude)
                        .as("longitude"),
                Expressions.numberTemplate(BigDecimal.class, "COALESCE({0}, {1})", location.latitude, location.latitude)
                        .as("latitude"),
                stringTemplate("COALESCE({0}, 'NoPhoto.jpg')", property.propertyDetails.mainPhotoUrl)
                        .as("mainPhotoUrl"),
                property.propertyDetails.mainPhotoUrl.as("mediaUrl"),
                property.propertyDetails.yearBuilt.as("yearBuilt"),
                property.propertyDetails.floors.as("floors"),
                property.amenities.propertyComments.as("propertyComments"),
                state.stateName.as("state"),
                country.countryName.as("country"),
                property.propertyDetails.buildingComments.as("buildingComments"),
                market.marketName.as("marketName"),
                subMarket.subMarketName.as("subMarketName"),
                property.propertyDetails.constructionStatusID.as("constructionStatusID"),
                PropertyStringTemplates.getTotalAvailable(lCount, dlCount, slCount, listing, property).as("totalAvailable"),
                PropertyStringTemplates.getTotalAvailableForSort(lCount, dlCount, slCount, listing, property).as("totalAvailableForSort"),
                PropertyStringTemplates.getTotalAvailableSM(lCount, dlCount, slCount, listing, property).as("totalAvailableSM"),
//              PropertyStringTemplates.getListingTypeName(lCount, llCount, slCount, listing).as("listingTypeName"),
                PropertyStringTemplates.getAskingRate(llCount, listingforLease).as("askingRate"),
                PropertyStringTemplates.getListingCompanyName(lCount, dlCount, slCount, property).as("listingCompanyName"),
                PropertyStringTemplates.getAgentName(lCount, dlCount, slCount, listingAgentPerson).as("agentName"),
                PropertyStringTemplates.getAskingSalePrice(slCount, listingforSale).as("askingSalePrice"),
                PropertyStringTemplates.getRecordTypeName(lCount, llCount, slCount, listing).as("recordTypeName"),
                PropertyStringTemplates.getPrice(llCount, slCount, listingforLease, listingforSale, property).as("price"),
                PropertyStringTemplates.getSalePricePerSF(slCount, listingforSale).as("salePricePerSF"),
                PropertyStringTemplates.getAskingSalePrice(slCount, listingforSale).as("askingSalePrice"),
                PropertyStringTemplates.getSalePricePerSM(slCount, listingforSale).as("salePricePerSM"),
                PropertyStringTemplates.getTotalVacant(lCount, listing).as("totalVacant"),
                PropertyStringTemplates.getTotalVacantSM(lCount, listing).as("totalVacantSM"),
                PropertyStringTemplates.getAskingLeaseRatePerYrText(lCount, dlCount, slCount, listingforLease).as("askingLeaseRatePerYrText"),
                leaseType.leaseTypeName.as("leaseTypeName"),
                property.propertyDetails.condoTypeID.as("condoTypeID"),
                stringTemplate("COALESCE({0}, {1})", property.internalToolFields.trueOwners, property.internalToolFields.recordedOwners)
                               .as("owners"),
                country.alpha2Code.as("countryCode"),
                state.stateAbbr.as("stateCode"),
                property.propertyDetails.condoUnit.as("condoUnit"),
                propertyStrataRelationship.masterPropertyId.as("masterPropertyID")
        ));

        // Apply sorting using PropertySortBuilder
        query.orderBy(PropertySortBuilder.getOrderForPropertySearch( lCount, dlCount, slCount,llCount,
                requestDTO, property, address, city, county, useType, specificUses,
                listingforLease, listingforSale, listing, listingGroup, listingAgentPerson, market, subMarket));

        return query.offset(offset).limit(limit).fetch();
    }


    public List<PropertySearchDetailsResponseDTO> findPropertiesDetailsSearchBySale(
            PropertySearchDetailsRequestDTO searchCriteria, int offset, int limit) {

        NumberExpression<Long> lCount = PropertyCountExpressions.getLCount(property, listing, listingGroup);
        NumberExpression<Long> llCount = PropertyCountExpressions.getLLCount(property, listing, listingGroup,
                listingforLease);
        NumberExpression<Long> slCount = PropertyCountExpressions.getSLCount(property, listing, listingGroup,
                listingforSale);
        NumberExpression<Long> dlCount = PropertyCountExpressions.getDLCount(property, listing, listingGroup);

        Expression<String> recordTypeLabelExpr = EnumUtils.getLabelCaseFromEnumPath(listing.recordTypeId,
                        RecordType.class);

        OrderSpecifier<?> orderSpecifier = PropertySortBuilder.getOrderForPropertySearch(
                lCount, dlCount, slCount, llCount,
                searchCriteria,
                property, address, city, county,
                useType, specificUses,
                listingforLease, listingforSale,
                listing, listingGroup,
                listingAgentPerson, market, subMarket);

        JPAQuery<PropertySearchDetailsResponseDTO> baseQuery = PropertySearchBaseQueryBuilder
                .buildECREDetailsSearchBaseQueryforSale(jpaQueryFactory, searchCriteria)
                .select(Projections.bean(PropertySearchDetailsResponseDTO.class,
                        property.propertyID,
                        listing.listingId.as("listingID"),
                        listing.isActive.as("isActive"),
                        PropertyCaseBuilder.getPropertyNameFromPropertyOrAddress(property,address)
                                .as("propertyName"),
                        city.cityName.as("city"),
                        address.zipCode.as("zipCode"),
                        useType.useTypeName.as("propertyType"),
                        specificUses.specificUsesName.as("specificUses"),
                        address.addressText.as("address"),
                        property.propertySize.buildingSizeSF.as("buildingSize"),
                        PropertyStringTemplates.getBuildingSize(property)
                                .as("buildingSizeSM"),
                        PropertyStringTemplates.getTotalAvailable(lCount, dlCount, slCount, listing, property)
                                .as("totalAvailable"),
                        PropertyStringTemplates.getTotalAvailableSM(lCount, dlCount, slCount, listing, property)
                                .as("totalAvailableSM"),
                        location.longitude.as("longitude"),
                        location.latitude.as("latitude"),
                        stringTemplate("COALESCE({0}, 'NoPhoto.jpg')", property.propertyDetails.mainPhotoUrl)
                                .as("mainPhotoUrl"),
                        property.propertyDetails.mainPhotoUrl.as("mediaUrl"),
                        property.propertyDetails.yearBuilt.as("yearBuilt"),
                        property.propertyDetails.floors.as("floors"),
                        property.amenities.propertyComments.as("propertyComments"),
                        state.stateName.as("state"),
                        property.createdDate,
                        country.countryName.as("country"),
                        property.propertyDetails.buildingComments.as("buildingComments"),
                        market.marketName.as("marketName"),
                        subMarket.subMarketName.as("subMarketName"),
                        Expressions.numberTemplate(BigDecimal.class, "ROUND({0}, {1})", listing.totalVacant, 0).as("totalVacant"),
                        PropertyStringTemplates.getTotalVacantSMForSale(listing).as("totalVacantSM"),
                        PropertyStringTemplates.getListingCompanyNameForSale(listingBranch).as("listingCompanyName"),
                        property.propertyDetails.constructionStatusID.as("constructionStatusID"),
                        PropertyStringTemplates.getTotalAvailableSMForSale(listing).as("totalAvailableSM"),
                        listingforSale.askingSalePrice.as("askingSalePrice"),
                        PropertyStringTemplates.getAgentName(person).as("agentName"),
                        PropertyStringTemplates.getRoundedTotalAvailable(listing).as("totalAvailable"),
                        PropertyStringTemplates.getSalePriceFormatted(listingforSale).as("price"),
                        listingforSale.salePricePerSF.as("salePricePerSF"),
                        property.propertyDetails.condoTypeID.as("condoTypeID"),
                        stringTemplate("COALESCE({0}, {1})", property.internalToolFields.trueOwners,
                                        property.internalToolFields.recordedOwners)
                                .as("owners"),
                        country.alpha2Code.as("countryCode"),
                        ExpressionUtils.as(recordTypeLabelExpr, "recordTypeName"),
                        state.stateAbbr.as("stateCode"),
                        property.propertyDetails.condoUnit.as("condoUnit"),
                        propertyStrataRelationship.masterPropertyId.as("masterPropertyID")));

        baseQuery.orderBy(PropertySortBuilder.getOrderBySaleSearch(
                searchCriteria, property, address, city, county, useType, specificUses,
                listingforLease, listingforSale, listing, listingGroup, listingAgentPerson, market, subMarket));

        return baseQuery.offset(offset).limit(limit).fetch();
    }


    @Override
    public List<PropertySearchDetailsResponseDTO> findLeaseSearchDetails(PropertySearchDetailsRequestDTO requestDTO, int offset, int limit) {

        NumberExpression<Long> lCount = PropertyCountExpressions.getLCount(property, listing, listingGroup);
        NumberExpression<Long> llCount = PropertyCountExpressions.getLLCount(property, listing, listingGroup, listingforLease);
        NumberExpression<Long> slCount = PropertyCountExpressions.getSLCount(property, listing, listingGroup, listingforSale);
        NumberExpression<Long> dlCount = PropertyCountExpressions.getDLCount(property, listing, listingGroup);

        Expression<String> recordTypeLabelExpr = EnumUtils.getLabelCaseFromEnumPath(listingGroup.listing.recordTypeId,
                        RecordType.class);

        Expression<String> listingTypeLabelExpr = EnumUtils.getLabelCaseFromEnumPath( listing.listingTypeId, ListingType.class);

        JPAQuery<PropertySearchDetailsResponseDTO> propertyQuery = PropertySearchBaseQueryBuilder
                .buildBaseLeaseSearchQuery(jpaQueryFactory, requestDTO)
                .select(Projections.bean(PropertySearchDetailsResponseDTO.class,
                        property.propertyID.as("propertyID"),
                        listing.listingId.as("listingID"),
                        listing.isActive.as("isActive"),
                        PropertyCaseBuilder.getPropertyNameFromPropertyOrAddress(property, address).as("propertyName"),
                        city.cityName.as("city"),
                        property.propertyDetails.classTypeID.as("classTypeName"),
                        address.zipCode.as("zipCode"),
                        useType.useTypeName.as("propertyType"),
                        property.propertyDetails.specificUseName.as("specificUses"),
                        address.addressText.as("address"),
                        property.propertySize.buildingSizeSF.as("buildingSize"),
                        PropertyStringTemplates.getBuildingSize(property).as("buildingSizeSM"),
                        PropertyStringTemplates.getTotalAvailable(lCount, dlCount, slCount, listing, property).as("totalAvailable"),
                        PropertyStringTemplates.getTotalAvailableSM(lCount, dlCount, slCount, listing, property).as("totalAvailableSM"),
                        ExpressionUtils.as(Expressions.constant(BigDecimal.ZERO), "totalAvailableForSort"),
                        stringTemplate("CONCAT('$', ROUND({0}), ' - ', '$', ROUND({1}))",
                                listingforLease.askingLeaseRatePerMonthMin,
                                listingforLease.askingLeaseRatePerMonthMax).as("askingRate"),
                        location.longitude.as("longitude"),
                        location.latitude.as("latitude"),
                        stringTemplate("COALESCE({0}, 'NoPhoto.jpg')", property.propertyDetails.mainPhotoUrl)
                                .as("mainPhotoUrl"),
                        property.propertyDetails.mainPhotoUrl.as("mediaUrl"),
                        property.propertyDetails.yearBuilt.as("yearBuilt"),
                        property.propertyDetails.floors.as("floors"),
                        property.amenities.propertyComments.as("propertyComments"),
                        state.stateName.as("state"),
                        country.countryName.as("country"),
                        county.countyName.as("county"),
                        property.propertyDetails.buildingComments.as("buildingComments"),
                        PropertyStringTemplates.getAskingLeaseRateText(listingforLease.askingLeaseRatePerYearMin, listingforLease.askingLeaseRatePerYearMax)
                                .as("askingLeaseRatePerYrText"),
                        listingforSale.askingSalePrice.as("askingSalePrice"),
                        stringTemplate(
                                "COALESCE((CASE " +
                                        "WHEN {0} IS NOT NULL AND {1} IS NOT NULL AND {0} <> {1} " +
                                        "THEN CONCAT('$', ROUND({0}), ' - ', '$', ROUND({1})) " +
                                        "WHEN {0} IS NOT NULL AND {1} IS NOT NULL AND {0} = {1} " +
                                        "THEN CONCAT('$', ROUND({0})) " +
                                        "WHEN {0} IS NOT NULL AND {1} IS NULL " +
                                        "THEN CONCAT('$', ROUND({0})) " +
                                        "ELSE NULL " +
                                        "END), CONCAT('$', ROUND({2})))",
                                listingforLease.askingLeaseRatePerYearMin,
                                listingforLease.askingLeaseRatePerYearMax,
                                listingforSale.askingSalePrice
                        ).as("price"),
                        listingforSale.askingSalePrice.round().as("salePricePerSF"),
                        stringTemplate(
                                "CAST(ROUND({0} * 10.764, 2) AS STRING)",
                                listingforSale.salePricePerSF
                        ).as("salePricePerSM"),
                        stringTemplate(
                                "CONCAT({0}, CASE WHEN {1} IS NOT NULL THEN CONCAT('-', {1}) ELSE '' END)",
                                listingBranch.altCompanyName,
                                listingBranch.companyName
                        ).as("listingCompanyName"),
                        stringTemplate("CONCAT({0}, ' ', {1})", listingAgentPerson.firstName, listingAgentPerson.lastName)
                                .as("agentName"),
                        ExpressionUtils.as(recordTypeLabelExpr, "recordTypeName"),
                        PropertyStringTemplates.getTotalVacant(lCount, listing).as("totalVacant"),
                        PropertyStringTemplates.getTotalVacantSM(lCount, listing).as("totalVacantSM"),
                        listingforLease.minDiv.round().as("minDiv"),
                        stringTemplate("CAST(ROUND(({0} * 0.092903), 0) AS STRING)", listingforLease.minDiv)
                                .as("minDivSM"),
                        ExpressionUtils.as(listingTypeLabelExpr, "listingTypeName"),
                        leaseType.leaseTypeName.as("leaseTypeName"),
                        property.propertyDetails.constructionStatusID.as("constructionStatusID"),
                        market.marketName.as("marketName"),
                        subMarket.subMarketName.as("subMarketName"),
                        property.propertyDetails.condoTypeID.as("condoTypeID"),
                        stringTemplate("COALESCE({0}, {1})", property.internalToolFields.trueOwners, property.internalToolFields.recordedOwners)
                                .as("owners"),
                        country.alpha2Code.as("countryCode"),
                        state.stateAbbr.as("stateCode"),
                        property.propertyDetails.condoUnit.as("condoUnit"),
                        propertyStrataRelationship.masterPropertyId.as("masterPropertyID"),
                        address.streetNumberMinN.as("streetNumberMinN"),
                        address.streetNumberMaxN.as("streetNumberMaxN")
                ));

        // Apply sorting using PropertySortBuilder
        propertyQuery.orderBy(PropertySortBuilder.getOrderForPropertySearch( lCount, dlCount, slCount,llCount,
                requestDTO, property, address, city, county, useType, specificUses,
                listingforLease, listingforSale, listing, listingGroup, listingAgentPerson, market, subMarket));

        List<PropertySearchDetailsResponseDTO> propertyDetails = propertyQuery.offset(offset).limit(limit).fetch();

        // Collect lease listing and property IDs to filter suite queries
        // Get the labels from the enum dynamically
        List<String> leaseTypeNames = Stream.of(ListingType.DIRECT, ListingType.SUBLEASE, ListingType.CO_WORKING)
                .map(ListingType::getLabel)
                .collect(Collectors.toList());
        List<Integer> leaseListingIds = propertyDetails.stream()
                .filter(p -> {
                    String typeName = String.valueOf(p.getListingTypeName());
                    return typeName != null && leaseTypeNames.contains(typeName);
                })
                .map(PropertySearchDetailsResponseDTO::getListingID)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        List<Integer> propertyIds = propertyDetails.stream()
                .map(PropertySearchDetailsResponseDTO::getPropertyID)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (leaseListingIds.isEmpty() || propertyIds.isEmpty()) {
            propertyDetails.forEach(p -> p.setSuites(Collections.emptyList()));
            return propertyDetails;
        }

        // Fetch individual suites (non-contiguous)
        List<SuiteSearchDetailsResponseDTO> suiteDetails = (List<SuiteSearchDetailsResponseDTO>) PropertySearchBaseQueryBuilder
                .buildBaseLeaseSuiteSearchQuery(jpaQueryFactory, requestDTO, false, leaseListingIds, propertyIds)
                .select(Projections.bean(SuiteSearchDetailsResponseDTO.class,
                        Expressions.numberTemplate(Integer.class, "{0}", property.propertyID).as("propertyID"),
                        Expressions.numberTemplate(Integer.class, "{0}", listing.listingId).as("listingID"),
                        Expressions.numberTemplate(Integer.class, "{0}", suite.suiteID).as("suiteID"),
                        PropertyCaseBuilder.getPropertyNameFromPropertyOrAddress(property, address).as("propertyName"),
                        address.addressText.as("address"),
                        Expressions.numberTemplate(BigDecimal.class, "{0}", property.propertySize.buildingSizeSF).as("buildingSize"),
                        PropertyStringTemplates.getBuildingSize(property).as("buildingSizeSM"),
                        stringTemplate("CONCAT({0}, CASE WHEN {1} IS NOT NULL THEN CONCAT('-', {1}) ELSE '' END)",
                                company.altCompanyName, company.companyName).as("listingCompanyName"),
                        stringTemplate("CONCAT({0}, ' ', {1})", listingAgentPerson.firstName, listingAgentPerson.lastName).as("agentName"),
                        floor.floorNumber.as("floorNumber"),
                        suite.suiteNumber.as("suiteNumber"),
                        Expressions.numberTemplate(BigDecimal.class, "{0}", suite.availableSF).as("availableSF"),
                        PropertyStringTemplates.getAvailableSM(suite).as("availableSM"),
                        Expressions.asBoolean(suite.isVacant).as("isVacant"),
                        suite.possessionTypeID.as("possessionTypeID"),
                        suite.dateAvailable.as("dateAvailable"),
                        suite.suiteUseID.as("spaceUseTypeName"),
                        suite.dateOnMarket.as("dateOnMarket"),
                        suite.exclAvailable.as("exclAvailable"),
                        Expressions.asBoolean(false).as("isContiguous"),
                        Expressions.numberTemplate(BigDecimal.class, "{0}", suite.tiAllowance).as("tiAllowance"),
                        Expressions.asBoolean(suite.fitout).as("fitout"),
                        Expressions.numberTemplate(BigDecimal.class, "{0}", suite.minSF).as("minSF"),
                        stringTemplate("CAST(ROUND({0} * 0.092903, 0) AS STRING)", suite.minSF).as("minSM"),
                        stringTemplate(
                                "CASE WHEN {0} IS NOT NULL AND {1} IS NULL " +
                                        "THEN CONCAT('$', ROUND({0}, 2), ' ', COALESCE({2}, ''), '/Yr') " +
                                        "ELSE '' END",
                                suite.askingRateLow,
                                suite.askingRateHigh,
                                leaseType.leaseTypeName
                        ).as("askingRateText")
                ))
                .fetch();

        // Fetch contiguous suite blocks
        List<SuiteSearchDetailsResponseDTO> contiguousBlocks = (List<SuiteSearchDetailsResponseDTO>) PropertySearchBaseQueryBuilder
                .buildBaseLeaseSuiteSearchQuery(jpaQueryFactory, requestDTO, true, leaseListingIds, propertyIds)
                .select(Projections.bean(SuiteSearchDetailsResponseDTO.class,
                        property.propertyID.as("propertyID"),
                        listing.listingId.as("listingID"),
                        stringTemplate("COALESCE({0}, {1})", suiteContigBlocks.contigBlockID, suite.suiteID)
                                .castToNum(Integer.class).as("suiteID"),
                         PropertyCaseBuilder.getPropertyNameFromPropertyOrAddress(property, address).as("propertyName"),
                         address.addressText.as("address"),
                         property.propertySize.buildingSizeSF.as("buildingSize"),
                         PropertyStringTemplates.getBuildingSize(property).as("buildingSizeSM"),
                        stringTemplate("CONCAT({0}, CASE WHEN {1} IS NOT NULL THEN CONCAT('-', {1}) ELSE '' END)",
                                 company.altCompanyName, company.companyName).as("listingCompanyName"),
                        stringTemplate("CONCAT({0}, ' ', {1})", listingAgentPerson.firstName, listingAgentPerson.lastName).as("agentName"),
                        Expressions.numberTemplate(BigDecimal.class, "{0}", suiteContigBlocks.minSize).as("availableSF"),
                        Expressions.stringTemplate("CAST(ROUND({0} * 0.092903, 0) AS STRING)", suiteContigBlocks.minSize).as("availableSM"),
                         suiteContigBlocks.maxSize.as("maxSize"),
                         Expressions.numberTemplate(BigDecimal.class, "COALESCE({0} * 0.092903, 0)", suiteContigBlocks.maxSize).as("maxSizeSM"),
                         suite.fitout.as("fitout"),
                         Expressions.asBoolean(true).as("isContiguous")
                ))
                .fetch();

        // Map suites to properties by propertyID
        Map<Integer, List<SuiteSearchDetailsResponseDTO>> suitesByPropertyId = new HashMap<>();

        // Add individual suites
        suiteDetails.forEach(suite -> suitesByPropertyId.computeIfAbsent(suite.getPropertyID(), k -> new ArrayList<>()).add(suite));

        // Add contiguous blocks
        contiguousBlocks.forEach(block -> suitesByPropertyId.computeIfAbsent(block.getPropertyID(), k -> new ArrayList<>()).add(block));

        // Attach suites to property results
        for (PropertySearchDetailsResponseDTO property : propertyDetails) {
            property.setSuites(suitesByPropertyId.getOrDefault(property.getPropertyID(), Collections.emptyList()));
        }

        return propertyDetails;
    }

    public Long countByGridSearchAllProperties(PropertySearchDetailsRequestDTO requestDTO) {

        return (long) buildBasePropertySearchQuery(jpaQueryFactory, requestDTO)
                .select(property.propertyID)
                .groupBy(property.propertyID)
                .fetch()
                .size();
    }

    public Long countByGridSearchForLease(PropertySearchDetailsRequestDTO requestDTO) {

        return (long) buildBaseLeaseSearchQuery(jpaQueryFactory, requestDTO)
                .select(property.propertyID, listing.listingId)
                .groupBy(property.propertyID, listing.listingId)
                .fetch()
                .size();
    }

    public Long countByGridSearchForSale(PropertySearchDetailsRequestDTO requestDTO) {
        return (long) buildECREDetailsSearchBaseQueryforSale(jpaQueryFactory, requestDTO)
                .select(property.propertyID, listing.listingId)
                .groupBy(property.propertyID, listing.listingId)
                .fetch().size();
    }

    /**
     * Parses a search input string to extract street number range and street name
     * details.
     *
     * Example inputs:
     * - "100-200 Main" -> min: 100, max: 200, streetName: "Main", isOdd: false
     * - "123 Elm" -> min: 123, max: null, streetName: "Elm", isOdd: true
     *
     * @param input the input string containing range and street name
     * @return StreetRangeData containing parsed min, max, street name, and odd/even
     *         status
     */
    private StreetRangeData parseStreetRange(String input) {
      String[] parts = input.trim().split("\\s+");

      if (parts.length == 0) {
        return new StreetRangeData(null, null, "", null);
      }

      Integer min = null, max = null;
      String firstPart = parts[0];

      // Check if the first part is a street number or range (e.g., 123 or 100-200)
      if (firstPart.matches("^\\d+(-\\d+)?$")) {
        String[] rangeParts = firstPart.split("-");

        min = NumberUtils.safeParseInt(rangeParts[0].trim());

        if (rangeParts.length > 1) {
          max = NumberUtils.safeParseInt(rangeParts[1].trim());

          // If min equals max, treat it as a single number (not a range)
          if (Objects.equals(min, max)) {
            max = null;
          }
        }
      }

      // Extract the street name: everything after the first token
      String streetName = String.join(" ", Arrays.copyOfRange(parts, 1, parts.length)).trim();

      // Determine if the street number is odd
      Boolean isOdd = (min != null) ? min % 2 != 0 : null;

      return new StreetRangeData(min, max, streetName, isOdd);
    }

}

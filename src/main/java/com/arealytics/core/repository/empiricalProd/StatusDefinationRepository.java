package com.arealytics.core.repository.empiricalProd;

import org.springframework.data.jpa.repository.JpaRepository;

import com.arealytics.core.domain.empiricalProd.StatusDefination;
import java.util.List;


public interface StatusDefinationRepository extends JpaRepository<StatusDefination, Integer>  {
    List<StatusDefination> findByIsPropertyAuditStatusAndIsActiveTrue(String isPropertyAuditStatus);
}

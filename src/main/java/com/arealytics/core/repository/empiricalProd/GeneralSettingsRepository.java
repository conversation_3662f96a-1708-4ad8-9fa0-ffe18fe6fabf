
package com.arealytics.core.repository.empiricalProd;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import com.arealytics.core.domain.empiricalProd.GeneralSettings;

public interface GeneralSettingsRepository extends JpaRepository<GeneralSettings, Integer> {
  Optional<GeneralSettings> findByKeyAndSettingsGroupAndIsActiveTrue(String Key, String SettingsGroup);

  Optional<GeneralSettings> findBySettingsID(Integer settingsId);
}

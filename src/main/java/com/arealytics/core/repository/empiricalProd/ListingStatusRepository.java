package com.arealytics.core.repository.empiricalProd;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import com.arealytics.core.domain.empiricalProd.ListingStatus;

@Repository
public interface ListingStatusRepository extends JpaRepository<ListingStatus, Integer> {

    List<ListingStatus> findByIsActiveTrueAndListingStatusIdNotOrderBySequence(Integer listingStatusId);
}

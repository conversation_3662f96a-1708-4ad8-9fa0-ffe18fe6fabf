package com.arealytics.core.repository.empiricalProd;

import com.arealytics.core.domain.empiricalProd.ReleaseUpdates;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ReleaseUpdatesRepository extends JpaRepository<ReleaseUpdates, Integer> {
  Optional<ReleaseUpdates> findByApplication_ApplicationIdAndIsActiveTrue(Integer applicationId);
}

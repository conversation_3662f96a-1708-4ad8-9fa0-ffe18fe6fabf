package com.arealytics.core.repository.empiricalProd;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.arealytics.core.enumeration.ParentTable;

import java.util.List;
import java.util.Optional;

import com.arealytics.core.domain.empiricalProd.Notes;

@Repository
public interface NotesRespository extends JpaRepository<Notes, Integer>{
    List<Notes> findByParentTableIdAndParentIdAndIsActiveTrueOrderByModifiedDate(ParentTable parentTableId, Integer parentId);
    Optional<Notes> findByNoteIdAndIsActiveTrue(Integer noteId);
    List<Notes> findByParentTableIdAndParentIdAndIsActiveTrueOrderByModifiedDateDesc(ParentTable parentTableId, Integer parentId);
}

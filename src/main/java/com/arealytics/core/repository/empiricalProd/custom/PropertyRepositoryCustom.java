package com.arealytics.core.repository.empiricalProd.custom;

import com.arealytics.core.dto.querydsl.*;
import com.arealytics.core.dto.request.PropertyMapSearchRequestDTO;
import com.arealytics.core.dto.request.PropertySearchDetailsRequestDTO;
import com.arealytics.core.dto.request.PropertySearchECRERequestDTO;
import com.arealytics.core.dto.request.PropertySearchRequestDTO;
import com.arealytics.core.dto.response.*;
import com.arealytics.core.enumeration.CondoType;

import com.arealytics.core.enumeration.QuerySortDirection;
import com.arealytics.core.enumeration.StrataDetailsSortBy;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.ParseException;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface PropertyRepositoryCustom {
    PropertyDetailsQuerydslDTO findPropertyDetailsByPropertyID(Integer propertyId);
    List<PropertySearchAllPropertiesQuerydslDTO> findPropertiesBySearch(PropertySearchRequestDTO searchCriteria,Pageable pageable);
    List<PropertyDetailsQuerydslDTO> fetchChildPropertyDetailsByPropertyIDs(List<Integer> propertyIds);
    List<Integer> getChildPropertyIdsByMasterId(Integer masterPropertyId);
    Long countBySearchCriteria(PropertySearchRequestDTO searchCriteria);
    List<PropertyMapSearchQuerydslDTO> findPropertiesByMapSearch(PropertyMapSearchRequestDTO searchCriteria, Pageable pageable);
    Long countByMapSearchCriteria(PropertyMapSearchRequestDTO searchCriteria);
    List<MasterPropertiesDTO> findMasterPropertiesBySearch(String searchText, String unit, CondoType strataType);
    List<PropertyLeaseSearchQuerydslDTO> findPropertiesByLeaseSearch(PropertySearchRequestDTO searchCriteria, Pageable pageable);
    Long countByLeaseSearchCriteria(PropertySearchRequestDTO searchCriteria);
    List<PropertySaleSearchQuerydslDTO> findPropertiesBySaleSearch(PropertySearchRequestDTO searchCriteria,Pageable pageable);
    Long countBySaleSearchCriteria(PropertySearchRequestDTO searchCriteria);
    List<PropertySearchLeaseAndSaleERCQuerydslDTO> findPropertiesByLeaseAndSaleSearch(PropertySearchRequestDTO searchCriteria,Pageable pageable);
    Long countByLeaseAndSaleSearchCriteria(PropertySearchRequestDTO searchCriteria);
    List<PropertySearchSaleListingResponseDTO> findSaleListingByLeaseAndSaleSearch(PropertySearchRequestDTO searchCriteria);
    List<PropertySearchSuiteResponseDTO>findSuitesByLeaseAndSaleSearch(PropertySearchRequestDTO searchCriteria);
    List<PropertyIntersectResponseDTO> findMarketsAndSubMarketsByPoint(Point point, Integer useTypeId, Integer propertyID);
    List<PropertySearchQuerydslDTO> findStrataPropertiesByMasterStrataProperty(Integer masterPropertyID);
    List<PropertyElasticSearchResponseDTO> findPropertiesByElasticSearch(String text, Integer stateId, Boolean isElasticSearch, Integer limit);
    List<PropertyECRESearchQuerydslDTO> findPropertiesByECRESearch(PropertySearchECRERequestDTO searchCriteria, Pageable pageable) throws ParseException;
    Long countByECRESearchCriteria(PropertySearchECRERequestDTO searchCriteria) throws ParseException;
    List<PropertyECRESearchQuerydslDTO> findPropertiesByECRESearchForLeaseAndSale(PropertySearchECRERequestDTO searchCriteria, Pageable pageable) throws ParseException;
    Long countByECRESearchCriteriaForLeaseAndSale(PropertySearchECRERequestDTO searchCriteria) throws ParseException;
    List<FullStrataDetailResponseDTO> findLinkedPropertyDetails(List<Integer> propertyId, StrataDetailsSortBy sortBy, QuerySortDirection direction);
    List<PropertySearchDetailsResponseDTO> searchProperties(PropertySearchDetailsRequestDTO requestDTO, int offset, int limit);
    List<PropertySearchDetailsResponseDTO> findLeaseSearchDetails(PropertySearchDetailsRequestDTO requestDTO, int offset, int limit);
    List<PropertySearchDetailsResponseDTO> findPropertiesDetailsSearchBySale(PropertySearchDetailsRequestDTO searchCriteria, int offSet, int limit);
    Long countByGridSearchAllProperties(PropertySearchDetailsRequestDTO requestDTO);
    Long countByGridSearchForLease(PropertySearchDetailsRequestDTO requestDTO);
    Long countByGridSearchForSale(PropertySearchDetailsRequestDTO requestDTO);
}

package com.arealytics.core.repository.empiricalProd;

import com.arealytics.core.domain.empiricalProd.ChangeLogAdditionalAddress;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ChangeLogAdditionalAddressRepository extends JpaRepository<ChangeLogAdditionalAddress, Integer> {

    List<ChangeLogAdditionalAddress> findByAddressIdOrderByAdditionalAddressChangeLogIdDesc(Integer addressId);
}

package com.arealytics.core.repository.empiricalProd;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

import com.arealytics.core.domain.empiricalProd.NotesMediaRelationship;

public interface NotesMediaRelationshipRepository extends JpaRepository<NotesMediaRelationship, Integer>{
    List<NotesMediaRelationship> findByNotes_NoteIdInAndIsActiveTrue(List<Integer> noteIds);
    List<NotesMediaRelationship> findByNotes_NoteIdAndIsActiveTrue (Integer noteId);
}

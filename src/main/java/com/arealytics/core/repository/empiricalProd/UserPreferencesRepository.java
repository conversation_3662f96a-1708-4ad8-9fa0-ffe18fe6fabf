package com.arealytics.core.repository.empiricalProd;

import com.arealytics.core.domain.empiricalProd.UserPreferences;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface UserPreferencesRepository extends JpaRepository<UserPreferences, Integer> {
  Optional<UserPreferences> findFirstByUserIdAndTypeAndScreenOrderByModifiedDateDesc(Integer userId, String type, String screen);
  Optional<UserPreferences> findByUserPreferencesId(Integer userPreferenceId);
}

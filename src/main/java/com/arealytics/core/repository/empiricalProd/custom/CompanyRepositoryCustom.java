package com.arealytics.core.repository.empiricalProd.custom;

import com.arealytics.core.dto.request.AgentSearchRequestDTO;
import com.arealytics.core.dto.response.AgentSearchResponseDTO;
import com.arealytics.core.dto.response.CompanySearchResponseDTO;
import com.arealytics.core.dto.response.ExportCheckLimitResponseDTO;

import java.util.List;

public interface CompanyRepositoryCustom {
  List<CompanySearchResponseDTO> getCompanies(Integer companyId, Integer branchId, String searchValue);

  List<AgentSearchResponseDTO> getAgents(AgentSearchRequestDTO request);

  ExportCheckLimitResponseDTO getExportLimitCheckFlag(Integer entityId);
}

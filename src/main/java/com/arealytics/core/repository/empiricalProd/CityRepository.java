package com.arealytics.core.repository.empiricalProd;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.arealytics.core.domain.empiricalProd.City;

@Repository
public interface CityRepository extends JpaRepository<City, Integer> {
    List<City> findByIsActiveTrueOrderByCityNameAsc();

    Optional<City> findByCityNameAndStateId(String cityName, Integer stateId);
}

package com.arealytics.core.repository.empiricalProd;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

import com.arealytics.core.domain.empiricalProd.EntityModel;
import com.arealytics.core.domain.empiricalProd.SavedSearchPortfolio;

@Repository

public interface SavedSearchPortfolioRepository extends JpaRepository<SavedSearchPortfolio, Integer> {
    boolean existsByPortfolioNameAndCreatedByAndIsActive(String portfolioName, EntityModel createdBy, boolean isActive);
    Optional<SavedSearchPortfolio> findByPortfolioNameAndCreatedBy(String portfolioName,EntityModel createdBy);
}

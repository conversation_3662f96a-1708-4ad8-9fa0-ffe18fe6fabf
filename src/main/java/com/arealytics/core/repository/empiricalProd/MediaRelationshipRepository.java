package com.arealytics.core.repository.empiricalProd;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.arealytics.core.domain.empiricalProd.MediaRelationship;
import com.arealytics.core.enumeration.MediaRelationType;

@Repository
public interface MediaRelationshipRepository extends JpaRepository<MediaRelationship, Integer> {
  @EntityGraph(attributePaths = {
          "media",
          "media.createdBy",
          "media.createdBy.company",
          "media.createdBy.person",
          "property"
  })
  List<MediaRelationship> findByMediaRelationTypeIdAndRelationIdInAndIsActiveTrue(
      MediaRelationType mediaRelationType, List<Integer> relationId);

  List<MediaRelationship> findByRelationIdAndMediaRelationTypeIdAndIsDefaultTrueAndIsActiveTrue(
      Integer relationId, MediaRelationType mediaRelationType);

  Optional<MediaRelationship> findByMedia_MediaIdAndIsActiveTrue(Integer mediaId);

  List<MediaRelationship> findByMedia_MediaIdInAndIsActiveTrue(List<Integer> mediaIds);

  Optional<MediaRelationship> findByMediaRelationshipIdAndIsActiveTrue(Integer mediaRelationshipId);
}

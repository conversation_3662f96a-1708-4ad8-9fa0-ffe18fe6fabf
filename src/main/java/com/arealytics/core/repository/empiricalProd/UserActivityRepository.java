package com.arealytics.core.repository.empiricalProd;

import com.arealytics.core.domain.empiricalProd.UserActivity;
import com.arealytics.core.enumeration.ParentTable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

public interface UserActivityRepository extends JpaRepository<UserActivity, Integer> {
  List<UserActivity> findByParentTableAndCreatedByAndUserActivityTypeAndCreatedDateBetween(ParentTable parentTableId,
      Integer entityId, String userActivityType, Instant startDate, Instant endDate);

      Optional<UserActivity> findByUserActivityId(Integer userActivityId);
}

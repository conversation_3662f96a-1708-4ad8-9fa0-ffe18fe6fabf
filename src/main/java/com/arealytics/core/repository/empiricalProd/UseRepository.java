package com.arealytics.core.repository.empiricalProd;

import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.enumeration.ParentTable;
import org.springframework.data.jpa.repository.JpaRepository;

import com.arealytics.core.domain.empiricalProd.Use;

import java.util.Optional;

public interface UseRepository extends JpaRepository<Use, Integer> {
    Optional<Use> findByParentTableIdAndPropertyAndSequence(ParentTable parentTableId, Property property, Integer sequence);
 Optional<Use> findByParentIdAndParentTableIdAndIsActiveAndSequenceAndUseTypeUseTypeId(
            Integer parentId, ParentTable parentTableId, Boolean isActive, Integer sequence, Integer useTypeId);
}

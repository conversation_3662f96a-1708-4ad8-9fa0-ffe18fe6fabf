package com.arealytics.core.repository.empiricalProd;

import com.arealytics.core.domain.empiricalProd.ChangeLogAddress;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ChangeLogAddressRepository  extends JpaRepository<ChangeLogAddress, Integer> {
    @EntityGraph(attributePaths = {
            "changeLogField",
            "application",
            "address"
    })
    List<ChangeLogAddress> findByAddressParentIdOrderByAddressChangeLogIdDesc(Integer parentId);
}

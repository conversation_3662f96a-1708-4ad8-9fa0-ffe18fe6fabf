package com.arealytics.core.repository.empiricalProd;

//import com.arealytics.core.domain.empiricalProd.ChangeLogAddress;
import com.arealytics.core.domain.empiricalProd.ChangeLogAddress;
import com.arealytics.core.domain.empiricalProd.ChangeLogProperty;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChangeLogPropertyRepository extends JpaRepository<ChangeLogProperty, Integer> {

    @EntityGraph(attributePaths = {
            "changeLogField",
            "application"
    })
    List<ChangeLogProperty> findByPropertyIdOrderByPropertyChangeLogIdDesc(Integer propertyId);

}

package com.arealytics.core.repository.empiricalProd;

import com.arealytics.core.domain.empiricalProd.ChangeLogMedia;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChangeLogMediaRepository extends JpaRepository<ChangeLogMedia, Integer> {
    List<ChangeLogMedia> findByMediaIdOrderByMeidaChangeLogIdDesc(Integer mediaId);
}

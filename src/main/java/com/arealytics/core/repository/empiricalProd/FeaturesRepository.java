package com.arealytics.core.repository.empiricalProd;

import com.arealytics.core.domain.empiricalProd.Amenities;
import com.arealytics.core.domain.empiricalProd.Feature;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FeaturesRepository extends JpaRepository<Feature, Integer> {
    List<Feature> findByPropertyIdAndIsActiveTrue(Integer propertyId);
}

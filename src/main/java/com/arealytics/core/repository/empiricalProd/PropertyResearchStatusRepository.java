package com.arealytics.core.repository.empiricalProd;

import org.springframework.data.jpa.repository.JpaRepository;

import com.arealytics.core.domain.empiricalProd.PropertyResearchStatus;

import java.util.List;
import java.util.Optional;

public interface PropertyResearchStatusRepository extends JpaRepository<PropertyResearchStatus, Integer> {

    Optional<PropertyResearchStatus> findByPropertyPropertyIDAndPropertyResearchType_PropertyResearchTypeIdAndIsActive(
            Integer propertyId, Integer propertyResearchTypeId, Boolean isActive);

    Optional<PropertyResearchStatus> findFirstByPropertyPropertyIDAndIsActiveOrderByModifiedDateDesc(
        Integer propertyId, Boolean isActive);

    List<PropertyResearchStatus> findByProperty_PropertyIDAndIsActiveTrueOrderByPropertyResearchType_Sequence(Integer propertyId);
    
}

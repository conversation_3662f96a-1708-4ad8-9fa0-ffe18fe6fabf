package com.arealytics.core.repository.empiricalProd;

import java.util.List;
import java.util.Optional;

import com.arealytics.core.enumeration.AddressType;
import com.arealytics.core.enumeration.ParentTable;

import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;

import com.arealytics.core.domain.empiricalProd.Address;

public interface AddressRepository extends JpaRepository<Address, Integer> {

    Optional<Address> findFirstByParentIdAndIsActiveTrueAndSequenceOrderByAddressIdDesc(Integer parentId, Integer sequence);


    List<Address> findByParentTableIdAndParentIdAndSequenceNotAndIsActiveTrue(ParentTable parentTableId, Integer parentId, Integer sequence);

    @EntityGraph(attributePaths = {
        "location",
        "city",
        "state"
    })
    List<Address> findByParentIdAndParentTableIdAndAddressTypeIdOrderBySequenceAsc(Integer parentId, ParentTable parentTableId, AddressType addressTypeId);

    @EntityGraph(attributePaths = {})
    Optional<Address> findByAddressIdAndIsActiveTrue(Integer addressId);

       Optional<Address> findByParentIdAndParentTableIdAndIsActiveAndSequence(Integer parentId, ParentTable parentTableId, Boolean isActive, Integer sequence);
}



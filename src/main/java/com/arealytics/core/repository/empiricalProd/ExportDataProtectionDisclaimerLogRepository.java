package com.arealytics.core.repository.empiricalProd;

import com.arealytics.core.domain.empiricalProd.ExportDataProtectionDisclaimerLog;
import org.springframework.data.jpa.repository.JpaRepository;

import com.arealytics.core.enumeration.ParentTable;

public interface ExportDataProtectionDisclaimerLogRepository extends JpaRepository<ExportDataProtectionDisclaimerLog, Integer> {
  Boolean existsByEntity_EntityIdAndParentTableId(Integer entityId, ParentTable parentTableId);
}

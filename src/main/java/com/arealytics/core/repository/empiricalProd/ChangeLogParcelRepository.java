package com.arealytics.core.repository.empiricalProd;

import com.arealytics.core.domain.empiricalProd.ChangeLogParcel;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ChangeLogParcelRepository extends JpaRepository<ChangeLogParcel, Integer> {
    @EntityGraph(attributePaths = {
            "changeLogField",
            "application"
    })
    List<ChangeLogParcel> findByParcelIdOrderByParcelChangeLogIdDesc(Integer parcelId);
}

package com.arealytics.core.repository.empiricalProd;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.arealytics.core.domain.empiricalProd.CompanyRelationship;

@Repository
public interface CompanyRelationshipRepository extends JpaRepository<CompanyRelationship, Integer> {
  @EntityGraph(attributePaths = {
          "parentCompany"
  })
  List<CompanyRelationship> findByChildCompany_CompanyIdInAndIsActive(
      List<Integer> childCompanyId, Boolean isActive);
  Optional<CompanyRelationship> findByChildCompany_CompanyIdAndIsActiveTrue(Integer childCompanyId);
}

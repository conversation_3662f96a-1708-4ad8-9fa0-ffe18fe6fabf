package com.arealytics.core.repository.empiricalProd;

import java.util.List;

import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;

import com.arealytics.core.domain.empiricalProd.EntityModel;

public interface EntityRepository extends JpaRepository<EntityModel, Integer> {
     @EntityGraph(attributePaths = {
          "person",
          "company"
     })
     List<EntityModel> findByCompanyCompanyId(Integer companyId);
}

package com.arealytics.core.repository.empiricalProd;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.arealytics.core.domain.empiricalProd.ChangeLogFields;
import com.arealytics.core.enumeration.ParentTable;

@Repository
public interface ChangeLogFieldsRepository extends JpaRepository<ChangeLogFields, Integer> {
    Optional<ChangeLogFields> findByFieldNameAndIsActiveAndParentTableId(
            String fieldName, Boolean active, ParentTable tableName);

    List<ChangeLogFields> findByParentTableIdAndShowInSavedSearchTrueAndIsActiveTrue(ParentTable parentTableId);
}

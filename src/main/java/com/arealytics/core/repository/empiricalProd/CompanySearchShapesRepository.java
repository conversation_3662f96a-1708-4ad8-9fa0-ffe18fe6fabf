package com.arealytics.core.repository.empiricalProd;

import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;

import com.arealytics.core.domain.empiricalProd.CompanySearchShapes;
import java.util.List;


public interface CompanySearchShapesRepository extends JpaRepository<CompanySearchShapes, Integer> {
    @EntityGraph("person")
    List<CompanySearchShapes> findByCompanyIdAndIsActiveTrue(Integer companyId);
}

package com.arealytics.core.domain.empiricalDataStage;

import com.arealytics.core.enumeration.FeedFormats;
import com.arealytics.core.enumeration.HideReasons;
import com.arealytics.core.enumeration.RecordType;
import com.arealytics.core.enumeration.SuiteStatus;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.arealytics.core.converter.FeedFormatConverter;

@Entity
@Table(name = "Suite_Stage", schema = "Empirical_DataStage")
@Getter
@Setter
public class SuiteStage extends ALDataStageBaseEntity{
        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        @Column(name = "SuiteStageID")
        private Integer suiteStageID;

        @Column(name = "AgentID", length = 12, columnDefinition = "CHAR(12)")
        private String agentID;

        @Column(name = "UniqueID", length = 12, columnDefinition = "CHAR(12)")
        private String uniqueID;

        @Column(name = "Address", length = 100)
        private String address;

        @Column(name = "CityID")
        private Integer cityID;

        @Column(name = "StateID")
        private Integer stateID;

        @Column(name = "ZipCode", length = 10, columnDefinition = "CHAR(10)")
        private String zipCode;

        @Column(name = "CountryID")
        private Integer countryID;

        @Column(name = "RawData", columnDefinition = "json")
        private String rawData;

        @Column(name = "DataJSON", columnDefinition = "json")
        private String dataJSON;

        @Column(name = "PropertyID")
        private Integer propertyID;

        @Column(name = "BranchID")
        private Integer branchID;

        @Column(name = "SuiteID")
        private Integer suiteID;

        @Column(name = "LeaseListingID")
        private Integer leaseListingID;

        @Column(name = "SaleListingID")
        private Integer saleListingID;

        @Column(name = "RecordTypeID")
        private RecordType recordTypeID;

        @Column(name = "ListingStatusID")
        private Integer listingStatusID;

        @Column(name = "SuiteStatusID")
        private SuiteStatus suiteStatusID;

        @Column(name = "Latitude", precision = 21, scale = 14)
        private BigDecimal latitude;

        @Column(name = "Longitude", precision = 21, scale = 14)
        private BigDecimal longitude;

        @Column(name = "ProviderID")
        private Integer providerID;

        @Convert(converter = FeedFormatConverter.class)
        @Column(name = "FeedFormatTypeID")
        private Integer feedFormatTypeID;

        @Column(name = "HasAgentException")
        private Boolean hasAgentException;

        @Column(name = "HasLeaseListingException")
        private Boolean hasLeaseListingException;

        @Column(name = "OverrideLeaseListingException")
        private Boolean overrideLeaseListingException;

        @Column(name = "HasSaleListingException")
        private Boolean hasSaleListingException;

        @Column(name = "OverrideSaleListingException")
        private Boolean overrideSaleListingException;

        @Column(name = "HasDataException")
        private Boolean hasDataException;

        @Column(name = "ImportStatusID")
        private Integer importStatusID;

        @Column(name = "HasBranchException")
        private Boolean hasBranchException;

        @Column(name = "MergedJSON", columnDefinition = "json")
        private String mergedJSON;

        @Column(name = "IsHidden")
        private Boolean isHidden;

        @Column(name = "IsDeleted")
        private Boolean isDeleted;

        @Column(name = "HidedBy", length = 255)
        private String hidedBy;

        @Column(name = "HidedDate")
        private LocalDateTime hidedDate;

        @Column(name = "HideReasonID")
        private HideReasons hideReasonID;

        @Column(name = "HideReasonComments", columnDefinition = "text")
        private String hideReasonComments;

        @Column(name = "IsHold")
        private Boolean isHold;

        @Column(name = "HoldBy")
        private Integer holdBy;

        @Column(name = "HoldDate")
        private LocalDateTime holdDate;

        @Column(name = "HoldComments", columnDefinition = "text")
        private String holdComments;

        @Column(name = "HoldReasonID")
        private Integer holdReasonID;

        @Column(name = "HoldRevokeBy")
        private Integer holdRevokeBy;

}

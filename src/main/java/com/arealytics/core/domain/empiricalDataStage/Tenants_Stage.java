package com.arealytics.core.domain.empiricalDataStage;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "Tenants_Stage", schema = "Empirical_DataStage")
@Getter
@Setter
public class Tenants_Stage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Tenant_Stage_Id")
    private Integer tenantStageId;

    @Column(name = "VendorID", length = 200)
    private String vendorId;

    @Column(name = "TenantName", length = 225)
    private String tenantName;

    @Column(name = "Address1", length = 255)
    private String address1;

    @Column(name = "Address2", length = 255)
    private String address2;

    @Column(name = "City", length = 255)
    private String city;

    @Column(name = "State", length = 50)
    private String state;

    @Column(name = "StateAbbr", length = 50)
    private String stateAbbr;

    @Column(name = "CountryCode", length = 50)
    private String countryCode;

    @Column(name = "PostalCode", length = 50)
    private String postalCode;

    @Column(name = "NationalID", length = 255)
    private String nationalId;

    @Column(name = "OfficePhone", length = 50)
    private String officePhone;

    @Column(name = "Fax", length = 50)
    private String fax;

    @Column(name = "CEOName", length = 255)
    private String ceoName;

    @Column(name = "CEOTitle", length = 50)
    private String ceoTitle;

    @Column(name = "LineOfBusiness", length = 50)
    private String lineOfBusiness;

    @Column(name = "SICCode", length = 50)
    private String sicCode;

    @Column(name = "Revenue", precision = 14, scale = 2)
    private BigDecimal revenue;

    @Column(name = "EmployeesAtLocation")
    private Integer employeesAtLocation;

    @Column(name = "EmployeeCount")
    private Integer employeeCount;

    @Column(name = "LegalStatus", length = 50)
    private String legalStatus;

    @Column(name = "StatusCode", length = 50)
    private String statusCode;

    @Column(name = "SubsidiaryCode", length = 50)
    private String subsidiaryCode;

    @Column(name = "IsProcessed", columnDefinition = "TINYINT(1)")
    private Boolean isProcessed;

    @Column(name = "ConfirmedTenantID")
    private Integer confirmedTenantId;

    @Column(name = "PropertyID")
    private Integer propertyId;

    @Column(name = "MatchingScore")
    private Integer matchingScore;

    @Column(name = "CreatedDate")
    private LocalDateTime createdDate;

    @Column(name = "ModifiedDate")
    private LocalDateTime modifiedDate;

    @Column(name = "Latitude", precision = 21, scale = 14)
    private BigDecimal latitude;

    @Column(name = "Longitude", precision = 21, scale = 14)
    private BigDecimal longitude;

    @Column(name = "ParentCompanyID")
    private Integer parentCompanyId;

    @Column(name = "BranchID")
    private Integer branchId;

    @Column(name = "BatchID")
    private Integer batchId;

    @Column(name = "ProviderID")
    private Integer providerId;

    @Column(name = "IsDefault", columnDefinition = "TINYINT(1)")
    private Boolean isDefault;

    @Column(name = "NAICSCode", length = 255)
    private String naicsCode;

    @Column(name = "NACECode", length = 255)
    private String naceCode;

    @Column(name = "Email", length = 255)
    private String email;

    @Column(name = "WebsiteURL", length = 255)
    private String websiteUrl;

    @Column(name = "ModifiedBy")
    private Integer modifiedBy;

    @Column(name = "IsHidden", columnDefinition = "TINYINT(1)")
    private Boolean isHidden;

    @Column(name = "IsDeleted", columnDefinition = "TINYINT(1)")
    private Boolean isDeleted;

    @Column(name = "HidedBy")
    private Integer hidedBy;

    @Column(name = "HidedDate")
    private LocalDateTime hidedDate;

    @Column(name = "HideReasonID")
    private Integer hideReasonId;

    @Column(name = "HideReasonComments", length = 1000)
    private String hideReasonComments;

    @Column(name = "ASICEntityStatus", length = 45)
    private String asicEntityStatus;

    @Column(name = "ASICEntityType", length = 45)
    private String asicEntityType;

    @Column(name = "ASICEntityClass", length = 45)
    private String asicEntityClass;

    @Column(name = "ABNStatus", length = 45)
    private String abnStatus;

    @Column(name = "ABN_StatusFromDate", length = 45)
    private String abnStatusFromDate;

    @Column(name = "GST_Status", length = 45)
    private String gstStatus;

    @Column(name = "GST_StatusFromDate", length = 45)
    private String gstStatusFromDate;

    @Column(name = "RegistrationOrIncorporationDate", length = 45)
    private String registrationOrIncorporationDate;

    @Column(name = "EntityAge", length = 10)
    private String entityAge;

    @Column(name = "EmployeeIndicator", length = 45)
    private String employeeIndicator;

    @Column(name = "RevenueIndicator", length = 45)
    private String revenueIndicator;

    @Column(name = "HQ_ID", length = 45)
    private String hqId;

    @Column(name = "HQ_CompanyName", length = 100)
    private String hqCompanyName;

    @Column(name = "NumberofMembersinHierarchy", length = 45)
    private String numberOfMembersInHierarchy;

    @Column(name = "ImmediateParentDUNS", length = 45)
    private String immediateParentDuns;

    @Column(name = "ImmediateParentName", length = 100)
    private String immediateParentName;

    @Column(name = "ImmediateParentCountry", length = 45)
    private String immediateParentCountry;

    @Column(name = "DomesticParentDUNS", length = 45)
    private String domesticParentDuns;

    @Column(name = "DomesticParentName", length = 100)
    private String domesticParentName;

    @Column(name = "DomesticParentCountry", length = 45)
    private String domesticParentCountry;

    @Column(name = "GlobalUltimateParentDUNS", length = 45)
    private String globalUltimateParentDuns;

    @Column(name = "GlobalUltimateParentName", length = 100)
    private String globalUltimateParentName;

    @Column(name = "GlobalUltimateParentCountry", length = 45)
    private String globalUltimateParentCountry;

    @Column(name = "PrimarySICDesc", length = 45)
    private String primarySICDesc;

    @Column(name = "PrimarySIC3Digit", length = 45)
    private String primarySIC3Digit;

    @Column(name = "PrimarySIC3DigitDesc", length = 45)
    private String primarySIC3DigitDesc;

    @Column(name = "PrimarySIC2Digit", length = 45)
    private String primarySIC2Digit;

    @Column(name = "PrimarySIC2DigitDesc", length = 45)
    private String primarySIC2DigitDesc;

    @Column(name = "PrimarySICDivision", length = 45)
    private String primarySICDivision;

    @Column(name = "PrimarySICDivisionDesc", length = 45)
    private String primarySICDivisionDesc;

    @Column(name = "SubHideReasonID")
    private Integer subHideReasonId;

    @Column(name = "ANZSICCode")
    private Integer anzsicCode;
}

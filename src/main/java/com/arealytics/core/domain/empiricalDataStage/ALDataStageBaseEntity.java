package com.arealytics.core.domain.empiricalDataStage;

import com.arealytics.core.utils.UserContext;
import jakarta.persistence.*;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;

@Data
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public class ALDataStageBaseEntity {
    @Column(name = "CreatedBy")
    private Integer createdBy;

    @Column(name = "CreatedDate", updatable = false)
    private Instant createdDate;

    @Column(name = "ModifiedBy")
    private Integer modifiedBy;

    @Column(name = "ModifiedDate")
    private Instant modifiedDate;

    @PrePersist
    public void prePersist() {
        // Set createdBy during POST
        this.createdDate = Instant.now();
        this.createdBy = UserContext.getLoginEntity().getEntityId();
    }

    @PreUpdate
    public void preUpdate() {
        // Set modifiedBy during put
        this.modifiedDate = Instant.now();
        this.modifiedBy = UserContext.getLoginEntity().getEntityId();
    }
}

package com.arealytics.core.domain.empiricalTenants;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "ConfirmedTenants", schema = "Empirical_Tenants")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ConfirmedTenants extends ALTenantsBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ConfirmedTenantID")
    private Integer confirmedTenantId;

    @Column(name = "TenantName")
    private String tenantName;

    @Column(name = "CompanyName")
    private String companyName;

    @Column(name = "AlternateCompanyName")
    private String alternateCompanyName;

    @Column(name = "Address1")
    private String address1;

    @Column(name = "Address2")
    private String address2;

    @Column(name = "AddressStreetNumber")
    private String addressStreetNumber;

    @Column(name = "AddressStreetName")
    private String addressStreetName;

    @Column(name = "CityID")
    private Integer cityId;

    @Column(name = "StateID")
    private Integer stateId;

    @Column(name = "ZipCode")
    private String zipCode;

    @Column(name = "CountyID")
    private Integer countyId;

    @Column(name = "CountryID")
    private Integer countryId;

    @Column(name = "AddressText")
    private String addressText;

    @Column(name = "ExtVendorID")
    private String extVendorId;

    @Column(name = "ProviderID")
    private Integer providerId;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "MetroID")
    private Integer metroId;

    @Column(name = "OfficePhone")
    private String officePhone;

    @Column(name = "CompanyID")
    private Integer companyId;

    @Column(name = "SaleComp_Stage_ID")
    private Integer saleCompStageId;

    @Column(name = "Fax")
    private String fax;

    @Column(name = "CEOName")
    private String ceoName;

    @Column(name = "CEOTitle")
    private String ceoTitle;

    @Column(name = "LineOfBusiness")
    private String lineOfBusiness;

    @Column(name = "SICCode")
    private String sicCode;

    @Column(name = "Revenue")
    private String revenue;

    @Column(name = "EmployeesAtLocation")
    private String employeesAtLocation;

    @Column(name = "EmployeeCount")
    private String employeeCount;

    @Column(name = "LegalStatus")
    private String legalStatus;

    @Column(name = "StatusCode")
    private String statusCode;

    @Column(name = "SubsidiaryCode")
    private String subsidiaryCode;

    @Column(name = "NAICSCode")
    private String naicsCode;

    @Column(name = "NACECode")
    private String naceCode;

    @Column(name = "NationalID")
    private String nationalId;

    @Column(name = "Email")
    private String email;

    @Column(name = "WebsiteUrl")
    private String websiteUrl;

    @Column(name = "FloorNumber")
    private String floorNumber;

    @Column(name = "PrimarySICDivisionDesc")
    private String primarySicDivisionDesc;

    @Column(name = "PrimarySIC2DigitDesc")
    private String primarySic2DigitDesc;

    @Column(name = "PrimarySIC3DigitDesc")
    private String primarySic3DigitDesc;

    @Column(name = "RegistrationOrIncorporationDate")
    private String registrationOrIncorporationDate;

    @Column(name = "ANZSICCode")
    private Integer anzsicCode;

    @Column(name = "RevenueIndicator")
    private String revenueIndicator;
}

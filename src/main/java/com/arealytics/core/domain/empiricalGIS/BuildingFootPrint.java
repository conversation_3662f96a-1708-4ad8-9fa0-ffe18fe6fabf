package com.arealytics.core.domain.empiricalGIS;

import org.hibernate.envers.Audited;
import org.locationtech.jts.geom.Geometry;

import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Audited(withModifiedFlag = true)
@Table(name = "BuildingFootPrints", schema = "Empirical_GIS")
public class BuildingFootPrint extends ALGISBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "BuildingFootPrintID")
    private Integer buildingFootPrintId;

    @Column(name = "CRE_PropertyID")
    private Integer crePropertyId;

    @Column(name = "AL_Building_ID")
    private Integer AL_Building_Id;

    @Column(name = "Floors")
    private Integer floors;

    @Column(name = "PropertyMinFloor")
    private Integer propertyMinFloor;

    @Column(name = "PropertyMaxFloor")
    private Integer propertyMaxFloor;

    @Lob
    @Column(name = "Shape", columnDefinition = "geometry")
    private Geometry shape;

    @Column(name = "SizeInSF")
    private Double sizeInSF;

    @Column(name = "UseTypeId")
    private Integer useTypeId;

    @Column(name = "AdditionalUseTypeId")
    private Integer additionalUseTypeId;

    @Column(name = "MainSpecificUseTypeId")
    private Integer mainSpecificUseTypeId;

    @Column(name = "AdditionalSpecificUseTypeId")
    private Integer additionalSpecificUseTypeId;

    @Column(name = "Description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "IsDefault")
    private Boolean isDefault;

    @Column(name = "isActive", columnDefinition = "TINYINT(1)")
    private Boolean isActive;

    // Won’t reassign the field if it’s exactly the same geometry.
    public void setShape(Geometry newShape) {
        if (this.shape == null || !this.shape.equalsExact(newShape)) {
            this.shape = newShape;
        }
    }
}

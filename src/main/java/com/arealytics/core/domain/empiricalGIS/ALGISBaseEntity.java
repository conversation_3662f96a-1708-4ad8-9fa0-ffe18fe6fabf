package com.arealytics.core.domain.empiricalGIS;

import java.time.Instant;

import com.arealytics.core.utils.UserContext;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import lombok.Data;

@Data
@MappedSuperclass
public abstract class ALGISBaseEntity {

    @Column(name = "CreatedBy")
    private Integer CreatedBy;

    @Column(name = "CreatedDate", updatable = false)
    private Instant createdDate;

    @Column(name = "ModifiedBy")
    private Integer modifiedBy;

    @Column(name = "ModifiedDate")
    private Instant modifiedDate;

    @PrePersist
    public void prePersist() {
      // Set createdBy during POST
      this.createdDate = Instant.now();
      this.CreatedBy = UserContext.getLoginEntity().getEntityId();
    }

    @PreUpdate
    public void preUpdate() {
      // Set modifiedBy during put
      this.modifiedDate = Instant.now();
      this.modifiedBy = UserContext.getLoginEntity().getEntityId();
    }
}

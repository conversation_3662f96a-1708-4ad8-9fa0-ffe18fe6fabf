package com.arealytics.core.domain.empiricalProd;

import java.math.BigDecimal;

import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "ContactRoleInfo")
public class ContactRoleInfo extends ALTransactionBaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ContactRoleInfoID")
  private Integer contactRoleInfoId;

  @ManyToOne
  @JoinColumn(name = "EntityID")
  private EntityModel entity;

  @Column(name = "Title", length = 255)
  private String title;

  @Column(name = "AccountLockID", columnDefinition = "TINYINT(1)")
  private Boolean accountLockId;

  @Column(name = "IsActive", columnDefinition = "TINYINT(1)")
  private Boolean isActive;

  @Column(name = "Website", length = 255)
  private String website;

  @Column(name = "ForcefullyResetPassword", columnDefinition = "TINYINT(1)")
  private Boolean forcefullyResetPassword;

  @Column(name = "IsUnknownContact", columnDefinition = "TINYINT(1)")
  private Boolean isUnknownContact;

  @Column(name = "LeaseCredits", precision = 10, scale = 5)
  private BigDecimal leaseCredits;

  @Column(name = "LeaseInitialCredits", precision = 10, scale = 5)
  private BigDecimal leaseInitialCredits;
}

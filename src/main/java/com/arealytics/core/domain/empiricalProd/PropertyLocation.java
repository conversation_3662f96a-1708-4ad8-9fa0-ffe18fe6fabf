package com.arealytics.core.domain.empiricalProd;

import org.hibernate.envers.Audited;
import org.javers.core.metamodel.annotation.DiffIgnore;

import com.arealytics.core.converter.ZoningClassConverter;
import com.arealytics.core.enumeration.ZoningClass;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Embeddable
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PropertyLocation {

    @Column(name = "MetroId")
    private Integer metroId;

    @Column(name = "MarketId")
    private Integer marketId;

    @ManyToOne(
         cascade = {
                CascadeType.PERSIST,
                CascadeType.MERGE,
                CascadeType.REFRESH,
                CascadeType.DETACH
            }
    )
    @Audited(targetAuditMode = org.hibernate.envers.RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "SubMarketID", nullable = false,  insertable = false,
            updatable = false)
    @DiffIgnore
    private SubMarket subMarket;

    @Column(name = "SubMarketID")
    private Integer subMarketID;

    @Column(name = "ParkComplexName", length = 100)
    private String parkComplexName;

    @Column(name = "Address", length = 100)
    private String address;

    @ManyToOne(
            cascade = {
                CascadeType.PERSIST,
                CascadeType.MERGE,
                CascadeType.REFRESH,
                CascadeType.DETACH
            })
    @JoinColumn(name = "AddressID", nullable = false)
    @DiffIgnore
    private Address addressId;

    @ManyToOne(
            cascade = {
                CascadeType.PERSIST,
                CascadeType.MERGE,
                CascadeType.REFRESH,
                CascadeType.DETACH
            })
    @DiffIgnore
    @JoinColumn(name = "LocationID", nullable = false)
    private Location location;

    @Column(name = "Zoning", length = 100)
    private String zoning;

    @Convert(converter = ZoningClassConverter.class)
    @Column(name = "ZoningClassID")
    private ZoningClass zoningClassID;

    @Column(name = "ZoningCode", length = 100)
    private String zoningCode;

    @Column(name = "LGA", length = 100)
    private String lga;

    @Column(name = "GeoscapePropertyID", length = 20)
    private String geoscapePropertyID;

    @Column(name = "CounsilTaxID", length = 50)
    private String counsilTaxID;

    @Column(name = "ValuerGeneralID", length = 50)
    private String valuerGeneralID;

    @Convert(converter = ZoningClassConverter.class)
    @Column(name = "SurroundingLandUse")
    private ZoningClass surroundingLandUse;

    @Column(name = "LegalDescription", length = 2000)
    private String legalDescription;

    @Column(name = "UseAddressAsPropertyName")
    private Boolean useAddressAsPropertyName;
}

package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.ParentTableConverter;
import com.arealytics.core.enumeration.ParentTable;
import jakarta.persistence.*;
import lombok.*;

import java.time.Instant;

@Entity
@Table(name = "ExportSettings")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ExportSettings {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ExportSettingsID")
  private Integer exportSettingsId;

  @Convert(converter = ParentTableConverter.class)
  @Column(name = "ParentTableID")
  private ParentTable parentTableId;

  @Column(name = "SettingsJSON", columnDefinition = "json")
  private String settingsJson;

  @Column(name = "IsActive")
  private Boolean isActive = true;

  @Column(name = "CreatedDt", updatable = false, insertable = false)
  private Instant createdDate;

  @Column(name = "ModifiedDt", insertable = false)
  private Instant modifiedDate;
}

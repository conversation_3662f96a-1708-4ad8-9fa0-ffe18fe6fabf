package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "CompanyRelationship")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CompanyRelationship extends ALTransactionBaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "CompanyRelationshipID")
  private Integer companyRelationshipId;

  @ManyToOne
  @JoinColumn(name = "ParentCompanyID", nullable = false)
  private Company parentCompany;

  @ManyToOne
  @JoinColumn(name = "ChildCompanyID", nullable = false)
  private Company childCompany;

  @ManyToOne
  @JoinColumn(name = "UltimateCompanyID")
  private Company ultimateCompany;

  @Column(name = "IsActive")
  private Boolean isActive = true;
}

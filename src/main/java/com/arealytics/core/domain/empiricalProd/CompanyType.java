package com.arealytics.core.domain.empiricalProd;

import java.time.Instant;
import java.time.LocalDateTime;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "CompanyType")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CompanyType {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CompanyTypeID")
    private Integer companyTypeId;

    @Column(name = "CompanyTypeName")
    private String companyTypeName;

    @Column(name = "IsActive")
    private boolean isActive;

    @Column(name = "CreatedDate")
    private Instant createdDate;

}

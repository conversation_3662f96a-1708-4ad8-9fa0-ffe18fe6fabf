package com.arealytics.core.domain.empiricalProd;

import java.time.Instant;

import org.locationtech.jts.geom.Geometry;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "SubMarket")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SubMarket {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SubMarketID")
    private Integer subMarketId;

    @Column(name = "MarketID", nullable = false)
    private Integer marketId;

    @Column(name = "SubMarketName", length = 45, nullable = false)
    private String subMarketName;

    @Column(name = "GeoShape", columnDefinition = "geometry")
    private Geometry geoShape;

    @Column(name = "MarketName", length = 100)
    private String marketName;

    @Column(name = "DisplayColor", length = 15)
    private String displayColor;

    @Column(name = "IsActive")
    private Boolean isActive;

    @CreatedDate
    @Column(name = "CreatedDate", updatable = false)
    private Instant createdDate;

    @LastModifiedDate
    @Column(name = "ModifiedDate")
    private Instant modifiedDate;
}

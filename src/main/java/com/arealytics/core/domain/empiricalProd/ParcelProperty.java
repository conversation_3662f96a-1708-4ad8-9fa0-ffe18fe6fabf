package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "ParcelProperty")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ParcelProperty {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ParcelPropertyID", nullable = false)
    private Integer parcelPropertyId;

    @ManyToOne(
            cascade = {
                CascadeType.PERSIST,
                CascadeType.MERGE,
                CascadeType.REFRESH,
                CascadeType.DETACH
            })
    @JoinColumn(name = "ParcelID", nullable = false)
    private Parcel parcel;

    @Column(name = "PropertyID", nullable = false)
    private Integer propertyId;

    @Column(name = "Sequence")
    private Integer sequence;
}

package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "SpecificUses")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SpecificUses {

    @Id
    @Column(name = "SpecificUsesID")
    private Integer specificUsesId;

    @Column(name = "SpecificUsesName")
    private String specificUsesName;

    @Column(name = "isActive", columnDefinition = "TINYINT(1)")
    private Boolean isActive;

    @Column(name = "UseTypeID")
    private Integer useTypeId;

    @Column(name = "DisplayColor", length = 15)
    private String displayColor;

    @Column(name = "Sequence")
    private Integer sequence;
}

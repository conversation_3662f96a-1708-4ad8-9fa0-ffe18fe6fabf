package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.FeedFormatConverter;
import com.arealytics.core.enumeration.FeedFormats;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "BranchDataFeedInbound")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BranchDataFeedInbound extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "BranchInboundID")
    private Integer branchInboundId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "BranchID")
    private Company branch;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ProviderID")
    private Providers providers;

    @Convert(converter = FeedFormatConverter.class)
    @Column(name = "FeedFormatID")
    private FeedFormats feedFormatId;

    @Column(name = "UsingEmpiricalFTP")
    private Boolean usingEmpiricalFTP;

    @Column(name = "FTPProtocol")
    private Integer ftpProtocol;

    @Column(name = "FTPHostName", length = 100, columnDefinition = "CHAR(100)")
    private String ftpHostName;

    @Column(name = "FTPUserName", length = 25, columnDefinition = "CHAR(25)")
    private String ftpUserName;

    @Column(name = "FTPPassword", length = 50, columnDefinition = "CHAR(50)")
    private String ftpPassword;

    @Column(name = "FTPPort")
    private Integer ftpPort;

    @Column(name = "FTPBasePath", length = 45)
    private String ftpBasePath;

    @Column(name = "FTPInboundPath", length = 255)
    private String ftpInboundPath;

    @Column(name = "APIBaseURL", length = 255)
    private String apiBaseURL;

    @Lob
    @Column(name = "APIEndPointJSON", columnDefinition = "json")
    private String apiEndPointJSON;

    @Column(name = "APIKey", length = 255)
    private String apiKey;

    @Column(name = "ExternalID", length = 20, columnDefinition = "CHAR(20)")
    private String externalId;

    @Column(name = "IsActive")
    private Boolean isActive = true;
}

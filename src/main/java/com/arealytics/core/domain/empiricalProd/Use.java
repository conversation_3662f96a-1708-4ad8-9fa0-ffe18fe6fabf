package com.arealytics.core.domain.empiricalProd;

import java.math.BigDecimal;
import java.util.List;

import org.hibernate.envers.Audited;

import com.arealytics.core.converter.ParentTableConverter;

import com.arealytics.core.enumeration.ParentTable;
import com.arealytics.core.domain.empiricalProd.UseType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.NotAudited;

@Entity
@Table(name = "\"Use\"")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Audited(withModifiedFlag = true)
public class Use extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "UseID", nullable = false, updatable = false)
    private Integer useId;

    @Convert(converter = ParentTableConverter.class)
    @Column(name = "ParentTableID", nullable = false)
    private ParentTable parentTableId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ParentID", nullable = false)
    private Property property;

    @Column(name = "ParentID", updatable = false, insertable = false)
    @NotAudited
    private Integer parentId;

    @ManyToOne
    @JoinColumn(name = "UseTypeID", nullable = false)
    @Audited(targetAuditMode = org.hibernate.envers.RelationTargetAuditMode.NOT_AUDITED, withModifiedFlag = true)
    private UseType useType;

    @ManyToOne
    @JoinColumn(name = "SpecificUsesID")
    @Audited(targetAuditMode = org.hibernate.envers.RelationTargetAuditMode.NOT_AUDITED, withModifiedFlag = true)
    private SpecificUses specificUses;

    @Column(name = "SpecificUsesID", updatable = false, insertable = false)
    @NotAudited
    private Integer SpecificUsesID;

    @Column(name = "Sequence")
    private Integer sequence;

    @Column(name = "IsActive", columnDefinition = "TINYINT(1)")
    private Boolean isActive;

    @Column(name = "Floors", length = 11)
    private String floors;

    @Column(name = "AvgFloorSizeSF", precision = 12, scale = 4)
    private BigDecimal avgFloorSizeSF;

    @Lob
    @Column(name = "Notes", columnDefinition = "TEXT")
    private String notes;
}

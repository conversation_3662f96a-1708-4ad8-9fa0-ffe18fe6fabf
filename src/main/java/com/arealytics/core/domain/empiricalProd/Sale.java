package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.*;
import com.arealytics.core.enumeration.*;
import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "Sale")
public class Sale extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SaleID")
    private int saleId;

    @Convert(converter = ArmsLengthTypeConverter.class)
    @Column(name = "ArmsLengthTypeID")
    private ArmsLengthType armsLengthTypeId;

    @Convert(converter = RightsIncludedTypeConverter.class)
    @Column(name = "RightsIncludedTypeID")
    private RightsIncludedType rightsIncludedTypeId;

    @Convert(converter = ConveyedConverter.class)
    @Column(name = "ConveyedID")
    private Conveyed conveyedId;

    @Column(name = "LotSizeSF", precision = 12, scale = 3)
    private BigDecimal lotSizeSF;

    @Column(name = "LotSizeAc", precision = 12, scale = 3)
    private BigDecimal lotSizeAc;

    @Column(name = "LotSizeHec", precision = 12, scale = 3)
    private BigDecimal lotSizeHec;

    @Column(name = "LegalDescription", columnDefinition = "text")
    private String legalDescription;

    @Convert(converter = CondoTypeConverter.class)
    @Column(name = "IsCondoSale")
    private CondoType isCondoSale;

    @Column(name = "CondoUnit", length = 20)
    private String condoUnit;

    @Convert(converter = EncumbrancesTypeConverter.class)
    @Column(name = "EncumbranceTypeID")
    private EncumbrancesType encumbranceTypeId;

    @Column(name = "EncumbranceNote", columnDefinition = "text")
    private String encumbranceNote;

    @Column(name = "DeedOrSaleDate")
    private LocalDateTime deedOrSaleDate;

    @Column(name = "DateRecorded")
    private LocalDateTime dateRecorded;

    @Convert(converter = InstrumentDocTypeConverter.class)
    @Column(name = "InstrumentDocTypeID")
    private InstrumentDocType instrumentDocTypeId;

    @Column(name = "InstrumentNotes", columnDefinition = "text")
    private String instrumentNotes;

    @Column(name = "DeedDocNumber", length = 25)
    private String deedDocNumber;

    @Column(name = "TransferTax", precision = 12, scale = 2)
    private BigDecimal transferTax;

    @Column(name = "SoldPrice", precision = 14, scale = 2)
    private BigDecimal soldPrice;

    @Convert(converter = SaleTypeConverter.class)
    @Column(name = "SaleTypeID")
    private SaleType saleTypeId;

    @Column(name = "TransactionNotes", columnDefinition = "text")
    private String transactionNotes;

    @Column(name = "Units")
    private Integer units;

    @Column(name = "PercentageOccuppied", precision = 5, scale = 2)
    private BigDecimal percentageOccupied;

    @Column(name = "IsPortfolio", columnDefinition = "TINYINT(1)")
    private Boolean isPortfolio;

    @Column(name = "PortfolioSalePrice", precision = 14, scale = 2)
    private BigDecimal portfolioSalePrice;

    @Column(name = "PortfolioID")
    private Integer portfolioId;

    @Column(name = "PortfolioNotes", columnDefinition = "text")
    private String portfolioNotes;

    @Column(name = "ListingNotes", columnDefinition = "text")
    private String listingNotes;

    @Column(name = "AskingPrice", precision = 12, scale = 2)
    private BigDecimal askingPrice;

    @Column(name = "ListingDate")
    private LocalDateTime listingDate;

    @Column(name = "DaysOnMarket")
    private Integer daysOnMarket;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "ScheduledGrossIncome", precision = 12, scale = 2)
    private BigDecimal scheduledGrossIncome;

    @Column(name = "VacancyAllowancePercentage", precision = 5, scale = 2)
    private BigDecimal vacancyAllowancePercentage;

    @Column(name = "EffectiveGrossIncome", precision = 12, scale = 2)
    private BigDecimal effectiveGrossIncome;

    @Column(name = "Expenses", precision = 12, scale = 2)
    private BigDecimal expenses;

    @Convert(converter = IncomeSourceConverter.class)
    @Column(name = "IncomeSourceID")
    private IncomeSource incomeSourceId;

    @Convert(converter = CapRateSourceConverter.class)
    @Column(name = "CapRateSourceID")
    private CapRateSource capRateSourceId;

    @Column(name = "CapRatePercentage", precision = 5, scale = 2)
    private BigDecimal capRatePercentage;

    @Convert(converter = CapRateTypeConverter.class)
    @Column(name = "CapRateTypeID")
    private CapRateType capRateTypeId;

    @Column(name = "IncomeNotes", columnDefinition = "text")
    private String incomeNotes;

    @Convert(converter = CashFlowTypeConverter.class)
    @Column(name = "CashflowTypeID")
    private CashFlowType cashflowTypeId;

    @Convert(converter = NOISourceConverter.class)
    @Column(name = "NOISourceID")
    private NOISource noiSourceId;

    @Convert(converter = NOIMethodConverter.class)
    @Column(name = "NOITypeID")
    private NOIMethod noiTypeId;

    @Column(name = "SalePricePerSF", precision = 10, scale = 2)
    private BigDecimal salePricePerSF;

    @Column(name = "SalePricePerHec", precision = 10, scale = 2)
    private BigDecimal salePricePerHec;

    @Column(name = "SalePricePerUnit", precision = 10, scale = 2)
    private BigDecimal salePricePerUnit;

    @Column(name = "PortfolioName", length = 255)
    private String portfolioName;

    @Column(name = "SoldSF", precision = 14, scale = 2)
    private BigDecimal soldSF;

    @Column(name = "PlanNumber", length = 45)
    private String planNumber;

    @Column(name = "UCV", columnDefinition = "text")
    private String ucv;

    @Column(name = "UCVDate")
    private LocalDateTime ucvDate;

    @Column(name = "VerificationComments", columnDefinition = "text")
    private String verificationComments;

    @Column(name = "SharesSoldPercentage", precision = 5, scale = 2)
    private BigDecimal sharesSoldPercentage;

    @Column(name = "NetScheduledIncome", precision = 12, scale = 2)
    private BigDecimal netScheduledIncome;

    @Column(name = "VGZone", columnDefinition = "text")
    private String vgZone;

    @Column(name = "ZoneDesc", columnDefinition = "text")
    private String zoneDesc;

    @Column(name = "VGPurpose", columnDefinition = "text")
    private String vgPurpose;

    @Column(name = "JurisdictionId", length = 45)
    private String jurisdictionId;

    @Convert(converter = TransactionOriginationTypeConverter.class)
    @Column(name = "TransactionOriginationTypeID")
    private TransactionOriginationType transactionOriginationTypeId;

    @Column(name = "SalePricePerLotSizeSF", precision = 14, scale = 2)
    private BigDecimal salePricePerLotSizeSF;

    @Column(name = "Yield", precision = 5, scale = 2)
    private BigDecimal yield;

    @Column(name = "NetOperatingIncome", precision = 12, scale = 2)
    private BigDecimal netOperatingIncome;

    @Column(name = "GrossLeaseActualIncome", precision = 12, scale = 2)
    private BigDecimal grossLeaseActualIncome;

    @Column(name = "NetLeaseActualIncome", precision = 12, scale = 2)
    private BigDecimal netLeaseActualIncome;

    @Column(name = "OutgoingsExpensesPerSF", precision = 12, scale = 2)
    private BigDecimal outgoingsExpensesPerSF;

    @Column(name = "IsDeedOrSaleDateConfirmed")
    private Boolean isDeedOrSaleDateConfirmed;

    @Column(name = "VolumeNumber", length = 45)
    private String volumeNumber;

    @Column(name = "Folio", length = 15)
    private String folio;

    @Convert(converter = SaleMethodConverter.class)
    @Column(name = "SaleMethodID")
    private SaleMethod saleMethodId;

    @Convert(converter = IncomeClassConverter.class)
    @Column(name = "IncomeClassID")
    private IncomeClass incomeClassId;

    @Convert(converter = LeaseIncomeClassConverter.class)
    @Column(name = "LeaseIncomeClassID")
    private LeaseIncomeClass leaseIncomeClassId;

    @Column(name = "Income", precision = 12, scale = 2)
    private BigDecimal income;

    @Column(name = "LeaseIncome", precision = 12, scale = 2)
    private BigDecimal leaseIncome;

    @Column(name = "LeaseIncomeTypeID")
    private Integer leaseIncomeTypeId;

    @Column(name = "YieldSourceID")
    private Integer yieldSourceId;

    @Column(name = "YieldTypeID")
    private Integer yieldTypeId;

    @Column(name = "YieldCashflowTypeID")
    private Integer yieldCashflowTypeId;

    @Column(name = "BuyerPendingConfirmation")
    private Boolean buyerPendingConfirmation;

    @Column(name = "SellerPendingConfirmation")
    private Boolean sellerPendingConfirmation;

    @Column(name = "DateOnMarket")
    private LocalDateTime dateOnMarket;
}

package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.FeatureConverter;
import com.arealytics.core.enumeration.Features;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "PropertyFeatures")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Feature extends ALTransactionBaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PropertyFeatureID")
    private Integer propertyFeatureID;

    @Column(name = "PropertyID")
    private Integer propertyId;

    @Convert(converter = FeatureConverter.class)
    @Column(name = "FeatureID")
    private Features featureId;

    @Column(name = "IsActive")
    private Boolean isActive = true;
}

package com.arealytics.core.domain.empiricalProd;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Data
@Entity
@Table(name = "Application")
public class Application {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ApplicationID")
    private int applicationId;

    @Column(name = "ApplicationName", length = 100)
    private String applicationName;

    @Column(name = "ApplicationDescription", length = 255)
    private String applicationDescription;

    @Column(name = "CreatedDate", columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private LocalDateTime createdDate;

    @Column(name = "CanSSO", columnDefinition = "TINYINT")
    private Boolean canSSO;
}

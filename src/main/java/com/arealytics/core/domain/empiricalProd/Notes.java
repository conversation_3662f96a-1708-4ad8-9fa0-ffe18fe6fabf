package com.arealytics.core.domain.empiricalProd;

import java.util.List;

import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import com.arealytics.core.converter.NoteTypeConverter;
import com.arealytics.core.converter.ParentTableConverter;
import com.arealytics.core.enumeration.NoteType;
import com.arealytics.core.enumeration.ParentTable;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "Notes")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Audited(withModifiedFlag = true)
public class Notes extends ALTransactionBaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "NoteID", nullable = false, updatable = false)
    private Integer noteId;

    @Convert(converter = NoteTypeConverter.class)
    @Column(name = "NoteTypeID")
    private NoteType noteTypeId;

    @Column(name = "NoteTitle", length = 100)
    private String noteTitle;

    @Lob
    @Column(name = "NoteDescription", columnDefinition = "MEDIUMTEXT")
    private String noteDescription;

    @Convert(converter = ParentTableConverter.class)
    @Column(name = "ParentTableID")
    private ParentTable parentTableId;

    @Column(name = "ParentID")
    private Integer parentId;

    @Column(name = "IsActive", columnDefinition = "TINYINT", nullable = false)
    private Boolean isActive = true;

    @OneToMany(mappedBy = "notes")
    @NotAudited
    private List<NotesMediaRelationship> notesMedia;
}



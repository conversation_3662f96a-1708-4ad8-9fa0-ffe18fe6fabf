package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.ActionConverter;
import com.arealytics.core.enumeration.Action;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;

@Entity
@Table(name = "ChangeLogParcel")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ChangeLogParcel {

    @Id
    @Column(name = "ParcelChangeLogID")
    private Integer parcelChangeLogId;

    @Column(name = "ParcelID")
    private Integer parcelId;

    @ManyToOne
    @JoinColumn(name = "ChangeLogFieldID")
    private ChangeLogFields changeLogField;

    @Column(name = "OldValue")
    private String oldValue;

    @Column(name = "NewValue")
    private String newValue;

    @Column(name = "ChangedBy")
    private Integer changedBy;

    @Column(name = "ChangedDate")
    private Instant changedDate;

    @ManyToOne
    @JoinColumn(name = "ApplicationID")
    private Application application;

    @Convert(converter = ActionConverter.class)
    @Column(name = "ActionID")
    private Action action;

}

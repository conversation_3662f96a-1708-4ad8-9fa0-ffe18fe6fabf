package com.arealytics.core.domain.empiricalProd;

import java.math.BigDecimal;

import com.arealytics.core.converter.SizeSourceConverter;
import com.arealytics.core.converter.TypicalFloorPlateConverter;
import com.arealytics.core.enumeration.SizeSource;
import com.arealytics.core.enumeration.TypicalFloorPlate;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Embeddable
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PropertySize {

    @Convert(converter = SizeSourceConverter.class)
    @Column(name = "SizeSourceID")
    private SizeSource sizeSourceID;

    @Column(name = "ClearHeightMin", precision = 12, scale = 3)
    private BigDecimal clearHeightMin;

    @Column(name = "ClearHeightMax", precision = 12, scale = 3)
    private BigDecimal clearHeightMax;

    @Column(name = "RetailFrontage", precision = 12, scale = 3)
    private BigDecimal retailFrontage;

    @Column(name = "TypicalFloorSize", precision = 12, scale = 3)
    private BigDecimal typicalFloorSize;

    @Column(name = "HardstandArea", precision = 10, scale = 2)
    private BigDecimal hardstandArea;

    @Convert(converter = SizeSourceConverter.class)
    @Column(name = "HardstandAreaSourceID")
    private SizeSource hardstandAreaSourceID;

    @Convert(converter = SizeSourceConverter.class)
    @Column(name = "ContributedGBASizeSourceID")
    private SizeSource contributedGBASizeSourceID;

    @Convert(converter = SizeSourceConverter.class)
    @Column(name = "GLASizeSourceID")
    private SizeSource glasSizeSourceID;

    @Convert(converter = SizeSourceConverter.class)
    @Column(name = "GLARSizeSourceID")
    private SizeSource glarSizeSourceID;

    @Column(name = "Mezzanine")
    private Boolean mezzanine;

    @Column(name = "Awnings")
    private Boolean awnings;

    @Column(name = "AwningsCount")
    private Integer awningsCount;

    @Column(name = "BuildingSize", precision = 12, scale = 3)
    private BigDecimal buildingSize;

    @Column(name = "LotSize", precision = 12, scale = 3)
    private BigDecimal lotSize;

    @Column(name = "MinFloorSize", precision = 12, scale = 3)
    private BigDecimal minFloorSize;

    @Column(name = "MaxFloorSize", precision = 12, scale = 3)
    private BigDecimal maxFloorSize;

    @Column(name = "OfficeSize", precision = 12, scale = 3)
    private BigDecimal officeSize;

    @Column(name = "RetailSize", precision = 12, scale = 3)
    private BigDecimal retailSize;

    @Column(name = "TotalAvailableSF", precision = 14, scale = 3)
    private BigDecimal totalAvailableSF;

    @Column(name = "BuildingSizeSF", precision = 14, scale = 3)
    private BigDecimal buildingSizeSF;

    @Column(name = "LotSizeSF", precision = 14, scale = 3)
    private BigDecimal lotSizeSF;

    @Column(name = "TotalSaleSizeSF", precision = 14, scale = 3)
    private BigDecimal totalSaleSizeSF;

    @Column(name = "ContributedGBASizeSF", precision = 14, scale = 3)
    private BigDecimal contributedGBASizeSF;

    @Column(name = "GLASizeSF", precision = 14, scale = 3)
    private BigDecimal glaSizeSF;

    @Column(name = "GLARSizeSF", precision = 14, scale = 3)
    private BigDecimal glarSizeSF;

    @Column(name = "MezzanineSizeSF", precision = 14, scale = 3)
    private BigDecimal mezzanineSizeSF;

    @Column(name = "AwningsSizeSF", precision = 14, scale = 3)
    private BigDecimal awningsSizeSF;

    @Column(name = "LotSizeAC", precision = 12, scale = 3)
    private BigDecimal lotSizeAC;

    @Column(name = "GBASizeSource", length = 45)
    private String GBASizeSource;

    @Convert(converter = SizeSourceConverter.class)
    @Column(name = "LotSizeSourceID")
    private SizeSource lotSizeSourceID;

    @Convert(converter = SizeSourceConverter.class)
    @Column(name = "NRASizeSourceID")
    private SizeSource NRASizeSourceID;

    @Column(name = "SmallestFloor")
    private Integer smallestFloor;

    @Column(name = "LargestFloor")
    private Integer largestFloor;

    @Column(name = "NLA", precision = 12, scale = 3)
    private BigDecimal nla;

    @Column(name = "NLAAC", precision = 12, scale = 3)
    private BigDecimal nlaac;

    @Column(name = "ContributedGBA", precision = 12, scale = 3)
    private BigDecimal contributedGBA;

    @Column(name = "GLA", precision = 12, scale = 3)
    private BigDecimal gla;

    @Column(name = "GLAR", precision = 12, scale = 3)
    private BigDecimal glar;

    @Convert(converter = TypicalFloorPlateConverter.class)
    @Column(name = "TypicalFloorSizeSourceID")
    private TypicalFloorPlate typicalFloorSizeSourceId;
}

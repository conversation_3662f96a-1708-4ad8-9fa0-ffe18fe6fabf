package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "PersonLogin")
@Getter
@Setter
@NoArgsConstructor
public class PersonLogin extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "LoginID")
    private Integer loginId;

    @Column(name = "EntityID")
    private Integer entityId;

    @Column(name = "Password", length = 255)
    private String password;

    @Column(name = "PasswordSalt", nullable = false, length = 255)
    private String passwordSalt;

    @Column(name = "Username", nullable = false, length = 255)
    private String username;

    @Column(name = "FailedAttemptsCount")
    private Integer failedAttemptsCount;
}

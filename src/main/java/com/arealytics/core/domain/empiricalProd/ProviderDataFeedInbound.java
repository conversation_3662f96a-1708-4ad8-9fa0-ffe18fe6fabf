package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.FeedFormatConverter;
import com.arealytics.core.enumeration.FeedFormats;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "ProviderDataFeedInbound")
@Getter
@Setter
public class ProviderDataFeedInbound {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ProviderInboundID")
    private Integer providerInboundId;

    @ManyToOne (fetch = FetchType.LAZY)
    @JoinColumn(name = "ProviderID")
    private Providers providers;

    @Convert(converter = FeedFormatConverter.class)
    @Column(name = "FeedFormatID")
    private FeedFormats feedFormatId;

    @Column(name = "UsingEmpiricalFTP")
    private Boolean usingEmpiricalFTP;

    @Column(name = "FTPProtocol")
    private Integer ftpProtocol;

    @Column(name = "FTPHostName", length = 100, columnDefinition = "CHAR(100)")
    private String ftpHostName;

    @Column(name = "FTPUserName", length = 25, columnDefinition = "CHAR(25)")
    private String ftpUserName;

    @Column(name = "FTPPassword", length = 50, columnDefinition = "CHAR(50)")
    private String ftpPassword;

    @Column(name = "FTPPort")
    private Integer ftpPort;

    @Column(name = "FTPBasePath", length = 45)
    private String ftpBasePath;

    @Column(name = "FTPInboundPath", length = 255)
    private String ftpInboundPath;

    @Column(name = "APIBaseURL", length = 255)
    private String apiBaseURL;

    @Lob
    @Column(name = "APIEndPointJSON", columnDefinition = "json")
    private String apiEndPointJSON;

    @Column(name = "APIKey", length = 255)
    private String apiKey;

    @Column(name = "IsActive")
    private Boolean isActive = true;

}

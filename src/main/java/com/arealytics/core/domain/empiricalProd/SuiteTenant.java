package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.TenantStatusConverter;
import com.arealytics.core.enumeration.TenantStatus;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Table(name = "SuiteTenant")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SuiteTenant extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SuiteTenantID")
    private Integer suiteTenantId;

    @ManyToOne
    @JoinColumn(name = "SuiteID")
    private Suite suite;

    @Column(name = "FloorID")
    private Integer floorId;

    @ManyToOne
    @JoinColumn(name = "PropertyID", nullable = false)
    private Property property;

    @Column(name = "ConfirmedTenantID", nullable = false)
    private Integer confirmedTenantId;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "MoveInDate")
    private LocalDateTime moveInDate;

    @Column(name = "MoveOutDate")
    private LocalDateTime moveOutDate;

    @Convert(converter = TenantStatusConverter.class)
    @Column(name = "TenantStatusID")
    private TenantStatus tenantStatusId;

    @Column(name = "Comments", columnDefinition = "TEXT")
    private String comments;
}

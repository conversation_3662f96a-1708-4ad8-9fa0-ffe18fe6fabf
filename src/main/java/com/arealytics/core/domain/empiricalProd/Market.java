package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.*;
import org.locationtech.jts.geom.Geometry;

import java.time.Instant;

@Entity
@Table(name = "Market")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Market {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MarketID")
    private Integer marketId;

    @Column(name = "MarketName", length = 45)
    private String marketName;

    @Column(name = "IsActive", columnDefinition = "TINYINT(1)")
    private Boolean isActive = true;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "MetroID", insertable = false, updatable = false)
    private Metro metro;

    @Column(name = "MetroID")
    private Integer metroId;

    @Column(name = "Geography", length = 45)
    private String geography;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "UseTypeID", insertable = false, updatable = false)
    private UseType useType;

    @Column(name = "UseTypeID")
    private Integer useTypeId;

    @Column(name = "GeoShape", columnDefinition = "geometry NOT NULL")
    private Geometry geoShape;

    @Column(name = "CreatedDate")
    private Instant createdDate;

    @Column(name = "ModifiedDate")
    private Instant modifiedDate;

    @Column(name = "DisplayColor", length = 15)
    private String displayColor;
}

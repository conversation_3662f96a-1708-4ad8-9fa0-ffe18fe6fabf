package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "Suffix")
@Getter
@Setter
@NoArgsConstructor
public class Suffix {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SuffixID")
    private Integer suffixId;

    @Column(name = "Suffix", length = 10)
    private String suffix;

    @Column(name = "IsActive")
    private Boolean isActive;
}

package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "Person")
@Getter
@Setter
@NoArgsConstructor
public class Person extends ALTransactionBaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PersonID")
    private Integer personId;

    @Column(name = "FirstName", length = 50)
    private String firstName;

    @Column(name = "MiddleName", length = 50)
    private String middleName;

    @Column(name = "LastName", length = 100)
    private String lastName;

    @Column(name = "Suffix", length = 25)
    private String suffix;

    @Column(name = "Email", length = 60)
    private String email;

    @Column(name = "TitleID")
    private Byte titleId;

    @Column(name = "NickName", length = 90)
    private String nickName;

    @Column(name = "WebSite", length = 255)
    private String webSite;

    @Column(name = "LinkedInProfile", length = 255)
    private String linkedInProfile;

    @Column(name = "PhotoURL", length = 255)
    private String photoUrl;
}

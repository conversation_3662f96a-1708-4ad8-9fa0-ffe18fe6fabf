package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.*;
import java.time.LocalDateTime;

import com.arealytics.core.converter.ParentTableConverter;
import com.arealytics.core.enumeration.ParentTable;

@Entity
@Table(name = "ProductActivityLog")
@Getter
@Setter
@NoArgsConstructor
public class ProductActivityLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ProductActivityLogID")
    private Integer productActivityLogID;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ProductID")
    private Products product;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "EntityID")
    private EntityModel entity;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PropertyID")
    private Property property;

    @Column(name = "DoNotShowAck")
    private Boolean doNotShowAck;

    @Column(name = "CreatedDate")
    private LocalDateTime createdDate;

    @Convert(converter = ParentTableConverter.class)
    @Column(name = "ParentTableID")
    private ParentTable parentTableID;

    @Column(name = "ParentID")
    private Integer parentID;

    @Column(name = "AdditionalInfo")
    private String additionalInfo;
}

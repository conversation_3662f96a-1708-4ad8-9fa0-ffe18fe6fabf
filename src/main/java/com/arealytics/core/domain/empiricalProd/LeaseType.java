package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "LeaseType")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class LeaseType {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "LeaseTypeID")
    private Integer leaseTypeId;

    @Column(name = "LeaseTypeName", length = 45)
    private String leaseTypeName;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "DisplayOrder")
    private Integer displayOrder;

    @Column(name = "IsRetired")
    private Boolean isRetired;
}

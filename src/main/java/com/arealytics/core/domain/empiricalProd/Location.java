package com.arealytics.core.domain.empiricalProd;

import java.math.BigDecimal;
import java.util.List;

import org.hibernate.envers.Audited;
import org.locationtech.jts.geom.Point;

import com.arealytics.core.converter.RoofTopSourceConverter;
import com.arealytics.core.enumeration.RoofTopSource;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "Location")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Audited(withModifiedFlag = true)
public class Location {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "LocationID", nullable = false, updatable = false)
    private Integer locationID;

    @Column(name = "Latitude", precision = 21, scale = 14, nullable = false)
    private BigDecimal latitude;

    @Column(name = "Longitude", precision = 21, scale = 14, nullable = false)
    private BigDecimal longitude;

    @Column(name = "ZCoordinate", precision = 15, scale = 8)
    private BigDecimal zCoordinate;

    @Convert(converter = RoofTopSourceConverter.class)
    @Column(name = "RoooftopSourceID")
    private RoofTopSource rooftopSourceID;

    @Column(name = "GISShapeID")
    private Integer gisShapeID;

    @Column(name = "LocationPoint", nullable = false)
    private Point locationPoint;

    @OneToMany(mappedBy = "location", fetch = FetchType.LAZY)
    private List<Address> addresses;

     // Won’t reassign the field if it’s exactly the same geometry.
    public void setLocationPoint(Point newPoint) {
        if (this.locationPoint == null || !this.locationPoint.equalsExact(newPoint)) {
            this.locationPoint = newPoint;
        }
    }
}

package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name="PropertyResearchType")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PropertyResearchType {


    @Id
    @Column(name = "PropertyResearchTypeID")
    private Integer propertyResearchTypeId;

    @Column(name = "PropertyResearchTypeName")
    private String propertyResearchTypeName;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "Sequence")
    private Integer sequence;
    
}

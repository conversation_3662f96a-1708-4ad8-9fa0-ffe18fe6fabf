package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.*;
import com.arealytics.core.enumeration.*;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "Listing")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Listing extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ListingID")
    private Integer listingId;

    @Convert(converter = RecordTypeConverter.class)
    @Column(name = "RecordTypeID")
    private RecordType recordTypeId;

    @Column(name = "TotalAvailable")
    private BigDecimal totalAvailable;

    @Column(name = "Vacant")
    private Boolean vacant;

    @Convert(converter = ListingTypeConverter.class)
    @Column(name = "ListingTypeID")
    private ListingType listingTypeId;

    @ManyToOne
    @JoinColumn(name = "ListingStatusID")
    private ListingStatus listingStatus;

    @Column(name = "MarketingURL")
    private String marketingUrl;

    @Column(name = "TotalExpenses")
    private BigDecimal totalExpenses;

    @Column(name = "TaxYear")
    private Integer taxYear;

    @Column(name = "ListingNotes", columnDefinition = "TEXT")
    private String listingNotes;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "ListingDate")
    private LocalDateTime listingDate;

    @Column(name = "ExpirationDate")
    private LocalDateTime expirationDate;

    @Convert(converter = ShareLevelConverter.class)
    @Column(name = "ShareLevelID")
    private ShareLevel shareLevelId;

    @Column(name = "ShareToPublic", columnDefinition = "TINYINT(1)")
    private Boolean shareToPublic;

    @Column(name = "TotalVacant")
    private BigDecimal totalVacant;

    @Column(name = "FeeCode")
    private String feeCode;

    @Convert(converter = SpecialConditionConverter.class)
    @Column(name = "SpecialConditionID")
    private SpecialCondition specialConditionId;

    @Convert(converter = AgreementTypeConverter.class)
    @Column(name = "AgreementTypeID")
    private AgreementType agreementTypeId;

    @Column(name = "OptionToExtend")
    private String optionToExtend;

    @Column(name = "SpecialBrokerNotes", columnDefinition = "TEXT")
    private String specialBrokerNotes;

    @Column(name = "InternalComments", columnDefinition = "TEXT")
    private String internalComments;

    @Column(name = "ResearchLastModifiedDate")
    private LocalDateTime researchLastModifiedDate;

    @Column(name = "YearBuilt")
    private Integer yearBuilt;

    @Column(name = "PercentageOccupied")
    private BigDecimal percentageOccupied;

    @Column(name = "NoOfFloors")
    private Integer noOfFloors;

    @Column(name = "ParkingRatio")
    private BigDecimal parkingRatio;

    @Column(name = "DateOnMarket")
    private LocalDateTime dateOnMarket;

    @Column(name = "SaleFee")
    private String saleFee;

    @Column(name = "MarketingAvailable")
    private BigDecimal marketingAvailable;

    @Column(name = "IsPublicListing")
    private Boolean isPublicListing;

    @Column(name = "HiddenByRule")
    private Boolean hiddenByRule;

    @Column(name = "ListingContactJSON", columnDefinition = "json")
    private String listingContactJson;

    @Column(name = "ListingURL")
    private String listingUrl;

    @Column(name = "ListingCreatedFrom")
    private Integer listingCreatedFrom;

    @Column(name = "InternalFlag", columnDefinition = "ENUM('In Review','Need Review','Reviewed')")
    private String internalFlag;
}

package com.arealytics.core.domain.empiricalProd;

import org.hibernate.envers.Audited;

import com.arealytics.core.converter.MediaSourceConverter;
import com.arealytics.core.enumeration.MediaSource;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "Media")
@Getter
@Setter
@NoArgsConstructor
@Audited(withModifiedFlag = true)
public class Media extends ALTransactionBaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "MediaID")
  private Integer mediaId;

  @Column(name = "MediaName", length = 255, nullable = false)
  private String mediaName;

  @Column(name = "Height")
  private Integer height;

  @Column(name = "Width")
  private Integer width;

  @Column(name = "Size")
  private Integer size;

  @Column(name = "Path", length = 255, nullable = false)
  private String path;

  @Column(name = "Ext", length = 45)
  private String ext;

  @Column(name = "Description", length = 255)
  private String description;

  @Column(name = "IsOwnMedia")
  private Boolean isOwnMedia;

  @Convert(converter = MediaSourceConverter.class)
  @Column(name = "MediaSourceID")
  private MediaSource mediaSourceId;

  @Lob
  @Column(name = "SourceComments", columnDefinition = "TEXT")
  private String sourceComments;
}

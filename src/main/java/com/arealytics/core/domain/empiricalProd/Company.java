package com.arealytics.core.domain.empiricalProd;

import java.math.BigDecimal;
import java.time.Instant;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "Company")
@AllArgsConstructor
public class Company extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CompanyID")
    private Integer companyId;

    @Column(name = "CompanyName", length = 200)
    private String companyName;

    @Column(name = "VendorTenantID_Del", length = 20)
    private String vendorTenantIdDel;

    @Column(name = "Website", length = 255)
    private String website;

    @Column(name = "CompanyTypeID")
    private Integer companyTypeId;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "IsPublic")
    private Boolean isPublic;

    @Column(name = "IsNationalCompany")
    private Boolean isNationalCompany;

    @Column(name = "Ticker", length = 20)
    private String ticker;

    @Column(name = "AltCompanyName", length = 150)
    private String altCompanyName;

    @Column(name = "IsCountryHeadQuarter")
    private Boolean isCountryHeadQuarter;

    @Column(name = "IsNationalBrokerageFirm")
    private Boolean isNationalBrokerageFirm;

    @Column(name = "IsGlobalHeadQuarter")
    private Boolean isGlobalHeadQuarter;

    @ManyToOne
    @JoinColumn(name = "CountryID")
    private Country country;

    @Column(name = "NAICSCode", length = 20)
    private String naicsCode;

    @Column(name = "IsMember")
    private Boolean isMember;

    @Column(name = "MetroID")
    private Integer metroId;

    @Column(name = "ISIC", length = 30)
    private String isic;

    @Column(name = "RatingTierID")
    private Integer ratingTierId;

    @Column(name = "IsDelinquent")
    private Boolean isDelinquent;

    @Column(name = "OccupiedSF", precision = 12, scale = 2)
    private BigDecimal occupiedSF;

    @Column(name = "IsAddressUnknown")
    private Boolean isAddressUnknown;

    @Column(name = "TenantResearchStatusID")
    private Integer tenantResearchStatusId;

    @Column(name = "FloorNumber", length = 45)
    private String floorNumber;

    @Column(name = "NationalID", length = 45)
    private String nationalId;

    @Column(name = "NoOfEmployees")
    private Integer noOfEmployees;

    @Column(name = "Revenue", length = 50)
    private String revenue;

    @Column(name = "EstSpaceOccupied", precision = 12, scale = 3)
    private BigDecimal estSpaceOccupied;

    @Column(name = "FloorStatusID")
    private Integer floorStatusId;

    @Column(name = "EstTimeAtLocation")
    private Instant estTimeAtLocation;

    @Column(name = "HidedBy")
    private Integer hidedBy;

    @Column(name = "HidedDate")
    private Instant hidedDate;

    @Column(name = "HideReasonID")
    private Integer hideReasonId;

    @Column(name = "HideReasonComments", length = 1000)
    private String hideReasonComments;

    @Column(name = "IsHidden")
    private Boolean isHidden;

    @Column(name = "HasJointVenture")
    private Boolean hasJointVenture;

    @Column(name = "UseAltCoAsCompanyName")
    private Boolean useAltCoAsCompanyName;

    @Column(name = "IsRegisteredAddress")
    private Boolean isRegisteredAddress;

    @Column(name = "ApplicationID")
    private Integer applicationId;

    @Column(name = "HideAgentsInPublic")
    private Boolean hideAgentsInPublic;

    @Column(name = "IsListingManagedByProvider")
    private Boolean isListingManagedByProvider;

    @Column(name = "IsTenantRepFirm")
    private Boolean isTenantRepFirm;

    @Column(name = "ExcludeFromAnalytics")
    private Boolean excludeFromAnalytics;

    @Column(name = "HasExchangeOwnerLock")
    private Boolean hasExchangeOwnerLock;

    @Column(name = "CompanyTierID")
    private Integer companyTierId;

    @Column(name = "LeaseCredits", precision = 10, scale = 5)
    private BigDecimal leaseCredits;

    @Column(name = "LeaseInitialCredits", precision = 10, scale = 5)
    private BigDecimal leaseInitialCredits;

    @Column(name = "SubHideReasonID")
    private Integer subHideReasonId;

    @Column(name = "PrimarySICDivisionDesc", length = 45)
    private String primarySICDivisionDesc;

    @Column(name = "PrimarySIC2DigitDesc", length = 45)
    private String primarySIC2DigitDesc;

    @Column(name = "PrimarySIC3DigitDesc", length = 45)
    private String primarySIC3DigitDesc;

    @Column(name = "RegistrationOrIncorporationDate")
    private Instant registrationOrIncorporationDate;

    @Column(name = "ANZSICCode")
    private Integer anzsicCode;
}

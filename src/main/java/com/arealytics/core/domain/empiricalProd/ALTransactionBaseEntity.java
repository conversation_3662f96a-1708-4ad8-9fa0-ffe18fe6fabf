package com.arealytics.core.domain.empiricalProd;

import java.time.Instant;

import com.arealytics.core.utils.UserContext;
import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import lombok.Data;

@Data
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public abstract class ALTransactionBaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CreatedBy")
    private EntityModel createdBy;

    @Column(name = "CreatedDate", updatable = false)
    private Instant createdDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ModifiedBy")
    private EntityModel modifiedBy;

    @Column(name = "ModifiedDate")
    private Instant modifiedDate;

    @PrePersist
    public void prePersist() {
      // Set createdBy during POST
      this.createdDate = Instant.now();
      this.createdBy = UserContext.getLoginEntity();
    }

    @PreUpdate
    public void preUpdate() {
      // Set modifiedBy during POST
      this.modifiedDate = Instant.now();
      this.modifiedBy = UserContext.getLoginEntity();
    }
}

package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "GroupRelationship")
@Getter
@Setter
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupRelationship {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "GroupRelationshipID")
    private Integer groupRelationshipId;

    @ManyToOne
    @JoinColumn(name = "GroupID", nullable = false)
    private Group group;

    @Column(name = "StructureID")
    private Integer structureId;

    @Column(name = "RightID")
    private Integer rightId;

    @ManyToOne
    @JoinColumn(name = "PropertyID")
    private Property property;

    @Column(name = "ParcelID")
    private Integer parcelId;

    @Column(name = "LotID")
    private Integer lotId;
}

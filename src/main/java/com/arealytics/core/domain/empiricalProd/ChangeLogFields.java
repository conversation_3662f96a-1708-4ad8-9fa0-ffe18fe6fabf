package com.arealytics.core.domain.empiricalProd;

import java.math.BigDecimal;

import com.arealytics.core.converter.ParentTableConverter;
import com.arealytics.core.enumeration.ParentTable;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "ChangeLogFields")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChangeLogFields {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ChangeLogFieldID")
    private Integer changeLogFieldId;

   @Convert(converter = ParentTableConverter.class)
    @Column(name = "ParentTableID")
    private ParentTable parentTableId;

    @Column(name = "FieldName")
    private String fieldName;

    @Column(name = "DisplayText")
    private String displayText;

    @Column(name = "IsActive", columnDefinition = "TINYINT")
    private Boolean isActive;

    @Column(name = "DataTypeID")
    private Integer dataTypeId;

    @Column(name = "ShowInSavedSearch", columnDefinition = "TINYINT")
    private Boolean showInSavedSearch;

    @Column(name = "ShowInLeaseExchange", columnDefinition = "TINYINT")
    private Boolean showInLeaseExchange;

    @Column(name = "DisplayOrder")
    private Integer displayOrder;

    @Column(name = "DisplayTextLeaseExchange")
    private String displayTextLeaseExchange;

    @Column(name = "IsRequiredLeaseExchange", columnDefinition = "TINYINT")
    private Boolean isRequiredLeaseExchange;

    @Column(name = "LookupSQL", columnDefinition = "TEXT")
    private String lookupSql;

    @Column(name = "Weight", precision = 5, scale = 2)
    private BigDecimal weight;

    @Column(name = "IsRatePerSqm", columnDefinition = "TINYINT")
    private Boolean isRatePerSqm;

    @Column(name = "ClusteringBuffer", precision = 6, scale = 2)
    private BigDecimal clusteringBuffer;
}

package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import org.hibernate.envers.Audited;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.RelationTargetAuditMode;

@Entity
@Table(name = "PropertyResearchStatus")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Audited(withModifiedFlag = true)
public class PropertyResearchStatus extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PropertyResearchStatusID")
    private Integer propertyResearchStatusId;

    @ManyToOne
    @JoinColumn(name = "PropertyID")
    private Property property;

    @ManyToOne
    @JoinColumn(name = "PropertyResearchTypeID")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED, withModifiedFlag = true)
    private PropertyResearchType propertyResearchType;

    @Column(name = "IsActive", columnDefinition = "TINYINT(1)")
    private Boolean isActive;
}

package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

@Entity
@Table(name = "PropertyAuditLog")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PropertyAuditLog extends ALTransactionBaseEntity{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PropertyAuditLogID")
    private Integer propertyAuditLogId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PropertyID", nullable = false)
    private Property property;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "StatusDefinationID", nullable = false)
    private StatusDefination statusDefination;

    @Column(name = "IsActive")
    private Boolean isActive;
}

package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Table(name = "SuiteContigBlocks")
public class SuiteContigBlocks extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ContigBlockID")
    private Integer contigBlockID;

    @ManyToOne
    @JoinColumn(name = "ListingID")
    private Listing listingID;

    @Column(name = "MinSize", precision = 12, scale = 3)
    private BigDecimal minSize;

    @Column(name = "MaxSize", precision = 12, scale = 3)
    private BigDecimal maxSize;

    @Column(name = "IsVirtual")
    private Boolean isVirtual;

    @Column(name = "IsActive")
    private Boolean isActive;
}

package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "PropertyFeatures")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PropertyFeature extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PropertyFeatureID")
    private Integer propertyFeatureId;

    @ManyToOne
    @JoinColumn(name = "PropertyID")
    private Property property;

    @Column(name = "PropertyID", insertable = false, updatable = false)
    private Integer propertyId;

    @Column(name = "FeatureID")
    private Integer featureId;

    @Column(name = "IsActive")
    private Boolean isActive = true;
}
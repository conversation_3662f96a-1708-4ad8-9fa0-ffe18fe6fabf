
package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "GeneralSettings")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class GeneralSettings {

    @Id
    @Column(name = "SettingID")
    private Integer settingsID;

    @Column(name = "SettingsGroup")
    private String settingsGroup;

    @Column(name = "Key")
    private String key;

    @Column(name = "FilterJSON")
    private String filterJSON;

    @Column(name = "IsActive")
    private boolean isActive;
}

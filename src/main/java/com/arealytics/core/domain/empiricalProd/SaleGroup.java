package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "SaleGroup")
public class SaleGroup extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SaleGroupID")
    private Integer saleGroupId;

    @ManyToOne
    @JoinColumn(name = "SaleID")
    private Sale sale;

    @ManyToOne
    @JoinColumn(name = "GroupID")
    private Group group;

    @ManyToOne
    @JoinColumn(name = "ListingID")
    private Listing listing;

    @Column(name = "IsActive", columnDefinition = "TINYINT(1)")
    private Boolean isActive;
}

package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "ListingEntity")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ListingEntity extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ListingEntityID")
    private Integer listingEntityId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ListingID", insertable = false, updatable = false)
    private Listing listing;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "EntityID", insertable = false, updatable = false)
    private EntityModel entity;

    @Column(name = "Sequence")
    private Integer sequence;

    @Column(name = "IsActive")
    private Boolean isActive;
}

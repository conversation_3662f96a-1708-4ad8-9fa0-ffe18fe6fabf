package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.AmenitiesTypeConverter;
import com.arealytics.core.enumeration.AmenitiesType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Entity
@Table(name = "PropertyAmenities")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Amenities extends ALTransactionBaseEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "AmenitiesID")
    private Integer amenitiesId;

    @Column(name = "PropertyID")
    private Integer propertyId;

    @Convert(converter = AmenitiesTypeConverter.class)
    @Column(name = "AmenitiesTypeID")
    private AmenitiesType amenitiesTypeId;

    @Column(name = "IsActive")
    private Boolean isActive = true;
}

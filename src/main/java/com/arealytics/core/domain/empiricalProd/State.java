package com.arealytics.core.domain.empiricalProd;

import java.time.LocalDateTime;
import java.util.List;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "State")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class State {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "StateID", nullable = false, updatable = false)
    private Integer stateId;

    @Column(name = "StateName", length = 100)
    private String stateName;

    @Column(name = "StateAbbr", length = 5)
    private String stateAbbr;

    @Column(name = "CountryID", nullable = false)
    private Integer countryId;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "CreatedDate")
    private LocalDateTime createdDate;

    @Column(name = "ShowInFilter")
    private Boolean showInFilter;

    @OneToMany(mappedBy = "state", fetch = FetchType.LAZY)
    private List<Address> addresses;
}

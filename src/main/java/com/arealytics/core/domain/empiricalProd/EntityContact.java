package com.arealytics.core.domain.empiricalProd;

import java.time.LocalDateTime;

import com.arealytics.core.converter.PhoneTypeConverter;
import com.arealytics.core.enumeration.PhoneType;
import jakarta.persistence.*;

import lombok.Data;

@Data
@Entity
@Table(name = "EntityContact")
public class EntityContact extends ALTransactionBaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "EntityContactID")
  private int entityContactId;

  @ManyToOne
  @JoinColumn(name = "EntityID")
  private EntityModel entity;

  @Convert(converter = PhoneTypeConverter.class)
  @Column(name = "ContactTypeID")
  private PhoneType contactTypeId;

  @Column(name = "ContactValue", length = 255)
  private String contactValue;

  @Column(name = "IsActive", columnDefinition = "TINYINT(1)")
  private Boolean isActive;

  @Column(name = "Sequence")
  private Integer sequence;
}

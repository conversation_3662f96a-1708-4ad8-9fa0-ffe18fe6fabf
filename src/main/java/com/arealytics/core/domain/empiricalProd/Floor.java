package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Table(name = "Floor")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Floor {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "FloorID", nullable = false)
    private Integer floorId;

    @Column(name = "FloorNumber", length = 45)
    private String floorNumber;

    @Column(name = "IsActive", columnDefinition="TINYINT(1)")
    private Boolean isActive;

    @Column(name = "Sequence")
    private Integer sequence;
}

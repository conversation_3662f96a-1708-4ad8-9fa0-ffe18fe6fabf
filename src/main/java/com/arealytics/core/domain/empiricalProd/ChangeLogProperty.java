package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.ActionConverter;
import com.arealytics.core.converter.NoteTypeConverter;
import com.arealytics.core.enumeration.Action;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;

@Entity
@Table(name = "ChangeLogProperty")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ChangeLogProperty {
    @Id
    @Column(name = "PropertyChangeLogID")
    private Integer propertyChangeLogId;

    @Column(name = "PropertyID")
    private Integer propertyId;

    @ManyToOne
    @JoinColumn(name = "ChangeLogFieldID")
    private ChangeLogFields changeLogField;

    @Column(name = "OldValue")
    private String oldValue;

    @Column(name = "NewValue")
    private String newValue;

    private Integer changedBy;

    @Column(name = "ChangedDate")
    private Instant changedDate;

    @ManyToOne
    @JoinColumn(name = "ApplicationID")
    private Application application;

    @Convert(converter = ActionConverter.class)
    @Column(name = "ActionID")
    private Action action;
}

package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.ActionConverter;
import com.arealytics.core.enumeration.Action;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;

import jakarta.persistence.*;
import java.time.Instant;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "ChangeLogAddress")
public class ChangeLogAddress {

    @Id
    @Column(name = "AddressChangeLogID")
    private Integer addressChangeLogId;

    @Column(name = "AddressID")
    private Integer addressId;

    @ManyToOne
    @JoinColumn(name = "ChangeLogFieldID")
    private ChangeLogFields changeLogField;

    @Column(name = "OldValue")
    private String oldValue;

    @Column(name = "NewValue")
    private String newValue;

    @Column(name = "ChangedBy")
    private Integer changedBy;

    @Column(name = "ChangedDate")
    private Instant changedDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "AddressID", insertable = false, updatable = false)
    private Address address;

    @ManyToOne
    @JoinColumn(name = "ApplicationID")
    private Application application;

    @Convert(converter = ActionConverter.class)
    @Column(name = "ActionID")
    private Action action;
}

package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Table(name = "Providers")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class Providers {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ProviderID")
    private Integer providerId;

    @Column(name = "ProviderName", length = 45)
    private String providerName;

    @Column(name = "ProviderGroup", length = 45)
    private String providerGroup;

    @Column(name = "IsActive", columnDefinition = "TINYINT(1)")
    private Boolean isActive;

    @Column(name = "CreatedDate", columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private LocalDateTime createdDate;

    @Column(name = "IsTenantProvider", columnDefinition = "TINYINT(1)")
    private Boolean isTenantProvider;

    @Column(name = "IsSaleCompProvider", columnDefinition = "TINYINT(1)")
    private Boolean isSaleCompProvider;

    @Column(name = "IsLeaseCompProvider", columnDefinition = "TINYINT(1)")
    private Boolean isLeaseCompProvider;

    @Column(name = "IsListingProvider", columnDefinition = "TINYINT(1)")
    private Boolean isListingProvider;

    @Column(name = "DisplayOrder")
    private Integer displayOrder;

    @Column(name = "DisplayText", length = 45)
    private String displayText;

    @Column(name = "LabelColor", length = 12)
    private String labelColor;

    @Column(name = "MonthlyExportLimit")
    private Integer monthlyExportLimit;

    @Column(name = "LeaseAutoEnrichmentPriority", nullable = false)
    private Integer leaseAutoEnrichmentPriority = 0;

    @Column(name = "TenantAutoEnrichmentPriority")
    private Integer tenantAutoEnrichmentPriority;

    @Column(name = "TenantVendorIDAutoEnrichmentPriority")
    private Integer tenantVendorIDAutoEnrichmentPriority;
}

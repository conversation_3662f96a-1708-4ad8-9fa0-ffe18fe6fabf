package com.arealytics.core.domain.empiricalProd;

import java.time.Instant;

import com.arealytics.core.converter.AlertScheduleTypeConverter;
import com.arealytics.core.converter.ParentTableConverter;
import com.arealytics.core.enumeration.AlertScheduleType;
import com.arealytics.core.enumeration.ParentTable;

import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "SavedSearch")
public class SavedSearch extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "Name", length = 100, nullable = false)
    private String name;

    @Column(name = "IsActive", columnDefinition = "TINYINT(1)")
    private boolean isActive;

    @ManyToOne
    @JoinColumn(name = "PortfolioID")
    private SavedSearchPortfolio savedSearchPortfolio;

    @Column(name = "SaveType", columnDefinition = "CHAR(6)")
    private String saveType;

    @Convert(converter = ParentTableConverter.class)
    @Column(name = "SearchTypeID")
    private ParentTable searchParentTable;

    @Column(name = "Criteria", columnDefinition = "TEXT")
    private String criteria;

    @Column(name = "Result", columnDefinition = "json")
    private String result;

    @Convert(converter = AlertScheduleTypeConverter.class)
    @Column(name = "AlertScheduleTypeID")
    private AlertScheduleType alertScheduleType;

    @Column(name = "IncludeMedia", columnDefinition = "TINYINT(1)")
    private boolean includeMedia;

    @Column(name = "LastRunDate")
    private Instant lastRunDate;

    @Column(name = "ExpirationDate")
    private Instant expirationDate;
}

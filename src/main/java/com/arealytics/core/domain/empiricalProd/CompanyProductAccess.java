package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Table(name = "CompanyProductAccess")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CompanyProductAccess extends ALTransactionBaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "CompanyProductAccessID")
  private Integer companyProductAccessId;

  @ManyToOne
  @JoinColumn(name = "CompanyID")
  private Company company;

  @ManyToOne
  @JoinColumn(name = "ProductID")
  private Products product;

  @Column(name = "HasAccess")
  private Boolean hasAccess;

  @Column(name = "IsBillable")
  private Boolean isBillable;

  @Column(name = "IsTrial")
  private Boolean isTrial;

  @Column(name = "TrialStartDate")
  private LocalDateTime trialStartDate;

  @Column(name = "TrialEndDate")
  private LocalDateTime trialEndDate;

  @Column(name = "IsActive")
  private Boolean isActive;

  @Column(name = "IsLimitedAccess")
  private Boolean isLimitedAccess;

  @Column(name = "EnforceTenantExportLimit")
  private Boolean enforceTenantExportLimit;

  @Column(name = "EnableSaleExport")
  private Boolean enableSaleExport;

  @Column(name = "EnableLeaseExport")
  private Boolean enableLeaseExport;

  @Column(name = "EnforceProExportLimit")
  private Boolean enforceProExportLimit;

  @Column(name = "EnableV2Search")
  private Boolean enableV2Search;
}

package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.*;
import com.arealytics.core.enumeration.*;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.Instant;

@Entity
@Table(name = "ListingForSale")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ListingforSale extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ForSaleID")
    private Integer forSaleId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ListingID", insertable = false, updatable = false)
    private Listing listing;

    @Column(name = "HasNegotiableSalePrice", columnDefinition = "TINYINT")
    private Boolean hasNegotiableSalePrice;

    @Column(name = "ListingForSalecol", length = 255)
    private String listingForSaleCol;

    @Column(name = "AskingSalePrice", precision = 18, scale = 2)
    private BigDecimal askingSalePrice;

    @Convert(converter = SaleTypeConverter.class)
    @Column(name = "SaleTypeID")
    private SaleType saleTypeId;

    @Convert(converter = EncumbrancesTypeConverter.class)
    @Column(name = "EncumbrancesTypeID")
    private EncumbrancesType encumbrancesTypeId;

    @Column(name = "EscrowOpenDate")
    private Instant escrowOpenDate;

    @Column(name = "ToShowID")
    private Integer toShowId;

    @Column(name = "ListingForSalecol1", length = 255)
    private String listingForSaleCol1;

    @Column(name = "ScheduledGrossIncome", precision = 18, scale = 2)
    private BigDecimal scheduledGrossIncome;

    @Column(name = "VacancyPercent", precision = 5, scale = 2)
    private BigDecimal vacancyPercent;

    @Column(name = "VacancyAllowance", precision = 18, scale = 2)
    private BigDecimal vacancyAllowance;

    @Column(name = "EffectiveGrossIncome", precision = 18, scale = 2)
    private BigDecimal effectiveGrossIncome;

    @Column(name = "Expenses", precision = 18, scale = 2)
    private BigDecimal expenses;

    @Column(name = "NetOperatingIncome", precision = 18, scale = 2)
    private BigDecimal netOperatingIncome;

    @Column(name = "GrossLeaseActualIncome", precision = 18, scale = 2)
    private BigDecimal grossLeaseActualIncome;

    @Column(name = "ProjectedIncome", precision = 18, scale = 2)
    private BigDecimal projectedIncome;

    @Column(name = "ExpenseRatio", precision = 5, scale = 2)
    private BigDecimal expenseRatio;

    @Column(name = "ExpensePerSF", precision = 10, scale = 2)
    private BigDecimal expensePerSF;

    @Column(name = "OperatingExpensePerSF", precision = 10, scale = 2)
    private BigDecimal operatingExpensePerSF;

    @Column(name = "TaxesPerSF", precision = 10, scale = 2)
    private BigDecimal taxesPerSF;

    @Column(name = "TaxYear")
    private Integer taxYear;

    @Column(name = "ReplacementAllowance", precision = 18, scale = 2)
    private BigDecimal replacementAllowance;

    @Convert(converter = NOIMethodConverter.class)
    @Column(name = "NoiMethodID")
    private NOIMethod noiMethodId;

    @Column(name = "CapRate", precision = 5, scale = 2)
    private BigDecimal capRate;

    @Column(name = "GrossIncomeMultiplier", precision = 10, scale = 2)
    private BigDecimal grossIncomeMultiplier;

    @Column(name = "HasNetLeasedInv", columnDefinition = "TINYINT")
    private Boolean hasNetLeasedInv;

    @Column(name = "NetLeaseActualIncome", precision = 18, scale = 2)
    private BigDecimal netLeaseActualIncome;

    @Column(name = "HasRightsIncluded", columnDefinition = "TINYINT")
    private Boolean hasRightsIncluded;

    @Convert(converter = PossessionTypeConverter.class)
    @Column(name = "PossesionTypeID")
    private PossessionType possesionTypeId;

    @Column(name = "SalePricePerSF", precision = 10, scale = 2)
    private BigDecimal salePricePerSF;

    @Column(name = "OccupacyPercent", precision = 5, scale = 2)
    private BigDecimal occupacyPercent;

    @Column(name = "IsCondoSale", columnDefinition = "TINYINT")
    private Boolean isCondoSale;

    @Column(name = "CondoUnit", length = 50)
    private String condoUnit;

    @Convert(converter = RightsIncludedTypeConverter.class)
    @Column(name = "RightsIncludedTypeID")
    private RightsIncludedType rightsIncludedTypeId;

    @Lob
    @Column(name = "EncumbrancesNotes", columnDefinition = "TEXT")
    private String encumbrancesNotes;

    @Column(name = "SalePricePerHec", precision = 18, scale = 2)
    private BigDecimal salePricePerHec;

    @Column(name = "MultiFamilyUnits")
    private Integer multiFamilyUnits;

    @Column(name = "SalePricePerUnit", precision = 18, scale = 2)
    private BigDecimal salePricePerUnit;

    @Column(name = "IsPortfolio", columnDefinition = "TINYINT")
    private Boolean isPortfolio;

    @Column(name = "PorfolioID")
    private Integer porfolioId;

    @Column(name = "PorfolioName", length = 255)
    private String porfolioName;

    @Column(name = "PortfolioSalePrice", precision = 18, scale = 2)
    private BigDecimal portfolioSalePrice;

    @Lob
    @Column(name = "PortFolioNotes", columnDefinition = "TEXT")
    private String portFolioNotes;

    @Convert(converter = NOISourceConverter.class)
    @Column(name = "NoiSourceID")
    private NOISource noiSourceId;

    @Column(name = "OverrideOccupancy", columnDefinition = "TINYINT")
    private Boolean overrideOccupancy;

    @Column(name = "EOIClosingDate")
    private Instant eoiClosingDate;

    @Column(name = "SaleSize", precision = 18, scale = 2)
    private BigDecimal saleSize;

    @Convert(converter = SaleMethodConverter.class)
    @Column(name = "SaleMethodID")
    private SaleMethod saleMethodId;

    @Column(name = "Income", precision = 18, scale = 2)
    private BigDecimal income;

    @Column(name = "IncomeClassID")
    private IncomeClass incomeClassId;

    @Column(name = "IncomeTypeID")
    private Integer incomeTypeId;

    @Convert(converter = IncomeSourceConverter.class)
    @Column(name = "IncomeSourceID")
    private IncomeSource incomeSourceId;

    @Column(name = "LeaseIncome", precision = 18, scale = 2)
    private BigDecimal leaseIncome;

    @Convert(converter = LeaseIncomeClassConverter.class)
    @Column(name = "LeaseIncomeClassID")
    private LeaseIncomeClass leaseIncomeClassId;

    @Column(name = "LeaseIncomeTypeID")
    private Integer leaseIncomeTypeId;

    @Convert(converter = CapRateSourceConverter.class)
    @Column(name = "CapRateSourceID")
    private CapRateSource capRateSourceId;

    @Convert(converter = CapRateTypeConverter.class)
    @Column(name = "CapRateTypeID")
    private CapRateType capRateTypeId;

    @Convert(converter = CashFlowTypeConverter.class)
    @Column(name = "CashflowTypeID")
    private CashFlowType cashflowTypeId;

    @Column(name = "Yield", precision = 10, scale = 2)
    private BigDecimal yield;

    @Column(name = "YieldSourceID")
    private Integer yieldSourceId;

    @Column(name = "YieldTypeID")
    private Integer yieldTypeId;

    @Column(name = "YieldCashflowTypeID")
    private Integer yieldCashflowTypeId;

    @Lob
    @Column(name = "IncomeNotes", columnDefinition = "TEXT")
    private String incomeNotes;

    @Column(name = "AuctionDate")
    private Instant auctionDate;

    @Column(name = "ExclFromAvailability", columnDefinition = "TINYINT")
    private Boolean exclFromAvailability;
}

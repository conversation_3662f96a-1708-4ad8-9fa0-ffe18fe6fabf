package com.arealytics.core.domain.empiricalProd;

import java.time.LocalDateTime;

import com.arealytics.core.converter.RoleConverter;
import com.arealytics.core.enumeration.Role;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "Entity")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EntityModel {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "EntityID")
  private Integer entityId;

  @ManyToOne
  @JoinColumn(name = "PersonID")
  private Person person;

  @Column(name = "PersonID", insertable = false, updatable = false)
  private Integer personId;

  @ManyToOne
  @JoinColumn(name = "CompanyID", insertable = false, updatable = false)
  private Company company;

  @Column(name = "CompanyID")
  private Integer companyId;

  @Column(name = "TenantID")
  private Integer tenantId;

  @Convert(converter = RoleConverter.class)
  @Column(name = "RoleID")
  private Role roleId;

  @Column(name = "StartDate")
  private LocalDateTime startDate;

  @Column(name = "EndDate")
  private LocalDateTime endDate;

  @Column(name = "ApplicationID")
  private Integer applicationId;
}

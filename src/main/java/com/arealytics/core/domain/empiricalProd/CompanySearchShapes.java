package com.arealytics.core.domain.empiricalProd;

import java.time.LocalDateTime;

import org.geolatte.geom.Geometry;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "CompanySearchShapes")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CompanySearchShapes extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CompanySearchShapeID")
    private Integer companySearchShapeId;

    @Column(name = "CompanyID")
    private Integer companyId;

    @Column(name = "Name")
    private String name;

    @Column(name = "FileName")
    private String fileName;

    @Column(name = "Polygon")
    private Geometry polygon;

    @Column(name = "Type")
    private String type;

    @Column(name = "IsActive")
    private Boolean isActive;
}

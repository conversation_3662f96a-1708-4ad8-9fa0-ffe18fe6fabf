package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.MediaSubTypeConverter;
import com.arealytics.core.converter.MediaTypeConverter;
import com.arealytics.core.enumeration.MediaRelationType;
import com.arealytics.core.enumeration.MediaSubType;
import com.arealytics.core.enumeration.MediaType;

import org.hibernate.envers.Audited;
import org.javers.core.metamodel.annotation.DiffIgnore;

import com.arealytics.core.converter.MediaRelationTypeConverter;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "MediaRelationship")
@Getter
@Setter
@NoArgsConstructor
@Audited(withModifiedFlag = true)
public class MediaRelationship {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "MediaRelationshipID")
  private Integer mediaRelationshipId;

  @OneToOne(
    cascade = {
      CascadeType.PERSIST,
      CascadeType.MERGE,
      CascadeType.REFRESH,
      CascadeType.DETACH
  })
  @JoinColumn(name = "MediaID", nullable = false)
  @DiffIgnore
  private Media media;

  @Column(name = "MediaID", insertable = false, updatable = false)
  private Integer mediaId;

  @Convert(converter = MediaRelationTypeConverter.class)
  @Column(name = "MediaRelationTypeID")
  private MediaRelationType mediaRelationTypeId;

  @Convert(converter = MediaTypeConverter.class)
  @Column(name = "MediaTypeID")
  private MediaType mediaTypeId;

  @Convert(converter = MediaSubTypeConverter.class)
  @Column(name = "MediaSubTypeID")
  private MediaSubType mediaSubTypeId;

  @Column(name = "RelationID")
  private Integer relationId;

  @ManyToOne
  @JoinColumn(name = "PropertyID")
  @DiffIgnore
  private Property property;

  @Column(name = "IsDefault")
  private Boolean isDefault;

  @Column(name = "IsActive")
  private Boolean isActive;
}

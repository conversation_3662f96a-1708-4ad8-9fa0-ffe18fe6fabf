package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.HideReasonsConverter;
import com.arealytics.core.enumeration.HideReasons;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "SubHideReasons") // Replace with actual table name if different
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SubHideReasons {

    @Id
    @Column(name = "SubHideReasonID")
    private Integer subHideReasonId;

    @Column(name = "SubHideReasonName", length = 100)
    private String subHideReasonName;

    @Convert(converter = HideReasonsConverter.class)
    @Column(name = "HideReasonID")
    private HideReasons hideReasonId;

    @Column(name = "IsActive")
    private Boolean isActive;
}

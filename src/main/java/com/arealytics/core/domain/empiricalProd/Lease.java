package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.*;
import com.arealytics.core.enumeration.*;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "Lease")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Lease extends ALTransactionBaseEntity {
    @Id
    @Column(name = "LeaseID")
    private Integer leaseId;

    @Column(name = "ConfirmedTenantID")
    private Integer confirmedTenantId;

    @Convert(converter = ListingTypeConverter.class)
    @Column(name = "ListingTypeID")
    private ListingType listingTypeId;

    @Column(name = "FloorID")
    private Integer floorId;

    @ManyToOne
    @JoinColumn(name = "PropertyID", nullable = false)
    private Property property;

    @Column(name = "SuiteUseID")
    private Integer suiteUseId;

    @Column(name = "AskingRateLow", precision = 10, scale = 2)
    private BigDecimal askingRateLow;

    @Column(name = "AskingRateHigh", precision = 12, scale = 4)
    private BigDecimal askingRateHigh;

    @ManyToOne
    @JoinColumn(name = "SpaceTypeID")
    private SpaceType spaceTypeId;

    @Convert(converter = SuiteStatusConverter.class)
    @Column(name = "SuiteStatusID")
    private SuiteStatus suiteStatusId;

    @Column(name = "SuiteNumber", length = 255)
    private String suiteNumber;

    @Column(name = "Vacant", precision = 10, scale = 2)
    private BigDecimal vacant;

    @Column(name = "DateVacant")
    private LocalDateTime dateVacant;

    @Convert(converter = PossessionTypeConverter.class)
    @Column(name = "PossessionTypeID")
    private PossessionType possessionTypeId;

    @Column(name = "DateAvailable")
    private LocalDateTime dateAvailable;

    @Column(name = "DateOnMarket")
    private LocalDateTime dateOnMarket;

    @Column(name = "IsCondo", columnDefinition = "TINYINT(1)")
    private Boolean isCondo;

    @Column(name = "ShowType", length = 45)
    private String showType;

    @Column(name = "DockHighDoors")
    private Integer dockHighDoors;

    @Column(name = "DockHighDoorsNotes", length = 1000)
    private String dockHighDoorsNotes;

    @Column(name = "DriveInDoors")
    private Integer driveInDoors;

    @Column(name = "DriveInDoorsNotes", length = 1000)
    private String driveInDoorsNotes;

    @Column(name = "TruckwellDoors")
    private Integer truckwellDoors;

    @Column(name = "TruckwellDoorsNotes", length = 1000)
    private String truckwellDoorsNotes;

    @Column(name = "ClearHeightMin", precision = 10, scale = 2)
    private BigDecimal clearHeightMin;

    @Column(name = "BayDepth", precision = 10, scale = 2)
    private BigDecimal bayDepth;

    @Column(name = "BayWidth", precision = 10, scale = 2)
    private BigDecimal bayWidth;

    @Column(name = "ColumnSpacing1", precision = 10, scale = 2)
    private BigDecimal columnSpacing1;

    @Column(name = "ColumnSpacing2", precision = 10, scale = 2)
    private BigDecimal columnSpacing2;

    @Convert(converter = SprinklerTypeConverter.class)
    @Column(name = "SprinklerTypeID")
    private SprinklerType sprinklerTypeId;

    @Column(name = "HasYardFenced", columnDefinition = "TINYINT(1)")
    private Boolean hasYardFenced;

    @Column(name = "TotalMonthlyRateMin", precision = 10, scale = 2)
    private BigDecimal totalMonthlyRateMin;

    @Column(name = "IsNegotiable", columnDefinition = "TINYINT(1)")
    private Boolean isNegotiable;

    @Convert(converter = LeaseTypeConverter.class)
    @Column(name = "LeaseRateTypeID")
    private LeaseType leaseRateTypeId;

    @Column(name = "LeaseTerms", length = 200)
    private String leaseTerms;

    @Column(name = "TIAllowance", precision = 10, scale = 2)
    private BigDecimal tiAllowance;

    @Column(name = "NumberOfRestRooms")
    private Integer numberOfRestRooms;

    @Column(name = "HasYardUnfenced", columnDefinition = "TINYINT(1)")
    private Boolean hasYardUnfenced;

    @Column(name = "HasYardPaved", columnDefinition = "TINYINT(1)")
    private Boolean hasYardPaved;

    @Column(name = "ParkingSpaces", precision = 10, scale = 2)
    private BigDecimal parkingSpaces;
 
    @Column(name = "ParkingRatio", precision = 10, scale = 2)
    private BigDecimal parkingRatio;

    @Column(name = "PercentageRents", precision = 10, scale = 2)
    private BigDecimal percentageRents;

    @Column(name = "IsFullFloor", columnDefinition = "TINYINT(1)")
    private Boolean isFullFloor;

    @Column(name = "NumberOfOffices")
    private Integer numberOfOffices;

    @Column(name = "OfficeAC")
    private Integer officeAc;

    @Column(name = "OfficeHeat")
    private Integer officeHeat;

    @Column(name = "WhseHVAC")
    private Integer whseHvac;

    @Column(name = "Phase", length = 10)
    private String phase;

    @Column(name = "Volts", precision = 10, scale = 2)
    private BigDecimal volts;

    @Column(name = "Amps", precision = 10, scale = 2)
    private BigDecimal amps;

    @Column(name = "SuiteComments", length = 1000)
    private String suiteComments;

    @Column(name = "SpecialBrokerNotes", length = 1000)
    private String specialBrokerNotes;

    @Column(name = "TransactionComments", length = 1000)
    private String transactionComments;

    @Column(name = "ConfirmationNotes", length = 1000)
    private String confirmationNotes;

    @Column(name = "TotalMonthlyRateMax", precision = 10, scale = 2)
    private BigDecimal totalMonthlyRateMax;

    @Column(name = "FinishedOfcMezz", columnDefinition = "TINYINT(1)")
    private Boolean finishedOfcMezz;

    @Column(name = "FinishedOfcMezzSF", precision = 10, scale = 2)
    private BigDecimal finishedOfcMezzSf;

    @Column(name = "UnFinishedOfcMezz", columnDefinition = "TINYINT(1)")
    private Boolean unFinishedOfcMezz;

    @Column(name = "UnFinishedOfcMezzSF", precision = 10, scale = 2)
    private BigDecimal unFinishedOfcMezzSf;

    @Column(name = "ClearHeightMax", precision = 10, scale = 2)
    private BigDecimal clearHeightMax;

    @Column(name = "AskingLeaseRatePerMonthMin", precision = 12, scale = 4)
    private BigDecimal askingLeaseRatePerMonthMin;

    @Column(name = "AskingLeaseRatePerMonthMax", precision = 12, scale = 4)
    private BigDecimal askingLeaseRatePerMonthMax;

    @Column(name = "GradeLevelDoorsTypeID")
    private Integer gradeLevelDoorsTypeId;

    @Column(name = "GradeLevelDoorsNotes", length = 1000)
    private String gradeLevelDoorsNotes;

    @Column(name = "OfficeSpace", precision = 10, scale = 2)
    private BigDecimal officeSpace;

    @Column(name = "IsActive", columnDefinition = "TINYINT(1) DEFAULT 1")
    private Boolean isActive;

    @Column(name = "IsVacant", columnDefinition = "TINYINT(1)")
    private Boolean isVacant;

    @Column(name = "Fitout", columnDefinition = "TINYINT(1)")
    private Boolean fitout;

    @Column(name = "FloorNumber", length = 255)
    private String floorNumber;

    @Column(name = "AvailableSpaceSF", precision = 14, scale = 3)
    private BigDecimal availableSpaceSf;

    @Column(name = "MinDivSF", precision = 14, scale = 3)
    private BigDecimal minDivSf;

    @Column(name = "ExecutionDate")
    private LocalDateTime executionDate;

    @Column(name = "DateOccupied")
    private LocalDateTime dateOccupied;

    @Column(name = "EffectiveRate", precision = 10, scale = 2)
    private BigDecimal effectiveRate;

    @Column(name = "StartingRate", precision = 10, scale = 2)
    private BigDecimal startingRate;

    @Column(name = "EscalationRate", length = 45)
    private String escalationRate;

    @Column(name = "EscalationType", length = 45)
    private String escalationType;

    @ManyToOne
    @JoinColumn(name = "SuiteID")
    private Suite suite;

    @Column(name = "SignDate")
    private LocalDateTime signDate;

    @Column(name = "DealingNumber", length = 200)
    private String dealingNumber;

    @Column(name = "LeaseFolio", length = 200)
    private String leaseFolio;

    @Convert(converter = LeaseTransTypeConverter.class)
    @Column(name = "LeaseTransTypeID")
    private LeaseTransType leaseTransTypeId;

    @Column(name = "ExpirationDate")
    private LocalDateTime expirationDate;

    @Convert(converter = TransactionOriginationTypeConverter.class)
    @Column(name = "TransactionOriginationTypeID")
    private TransactionOriginationType transactionOriginationTypeId;

    @Column(name = "Folio", length = 200)
    private String folio;

    @Column(name = "HeadTitle", length = 200)
    private String headTitle;

    @Column(name = "AltTenantName", length = 200)
    private String altTenantName;

    @Convert(converter = LeaseResearchStatusConverter.class)
    @Column(name = "LeaseResearchStatusID")
    private LeaseResearchStatus leaseResearchStatusId;

    @Column(name = "RecordedDate")
    private LocalDateTime recordedDate;

    @Column(name = "RenewalOption")
    private Integer renewalOption;

    @Column(name = "BankGuaranteeAmount", precision = 10, scale = 2)
    private BigDecimal bankGuaranteeAmount;

    @Column(name = "AutoCalculateEscalation", columnDefinition = "TINYINT(1)")
    private Boolean autoCalculateEscalation;

    @Column(name = "ExcludeFromAnalytics", columnDefinition = "TINYINT(1)")
    private Boolean excludeFromAnalytics;

    @Convert(converter = ShareLevelConverter.class)
    @Column(name = "ShareLevelID")
    private ShareLevel shareLevelId;

    @Column(name = "Score", precision = 5, scale = 2)
    private BigDecimal score;

    @Column(name = "PricePoints", precision = 7, scale = 5)
    private BigDecimal pricePoints;

    @Column(name = "UserConfirmations")
    private Integer userConfirmations;

    @Column(name = "TenantName", length = 200)
    private String tenantName;

    @ManyToOne
    @JoinColumn(name = "ParentLeaseID")
    private Lease parentLease;

    @Column(name = "IsExpDateConfirmed", columnDefinition = "TINYINT(1)")
    private Boolean isExpDateConfirmed;

    @Column(name = "IsOccupDateConfirmed", columnDefinition = "TINYINT(1)")
    private Boolean isOccupDateConfirmed;

    @Column(name = "FaceRentPerSqm", precision = 10, scale = 3)
    private BigDecimal faceRentPerSqm;

    @Convert(converter = LeaseStatusConverter.class)
    @Column(name = "LeaseStatusID")
    private LeaseStatus leaseStatusId;

    @Column(name = "IsTermsConfirmed", columnDefinition = "TINYINT(1)")
    private Boolean isTermsConfirmed;

    @Column(name = "NoOfFloors")
    private Integer noOfFloors;

    @Column(name = "RentFreePeriod", length = 100)
    private String rentFreePeriod;

    @Column(name = "IsAvailableSpaceSFConfirmed", columnDefinition = "TINYINT(1)")
    private Boolean isAvailableSpaceSfConfirmed;

    @Column(name = "IsStartingRateConfirmed", columnDefinition = "TINYINT(1)")
    private Boolean isStartingRateConfirmed;

    @Column(name = "IsFaceRentPerSqmConfirmed", columnDefinition = "TINYINT(1)")
    private Boolean isFaceRentPerSqmConfirmed;

    @Column(name = "MakeGoodClause", columnDefinition = "TINYINT(1)")
    private Boolean makeGoodClause;

    @Column(name = "DemolitionClause", columnDefinition = "TINYINT(1)")
    private Boolean demolitionClause;

    @Column(name = "GSTIncluded", columnDefinition = "TINYINT(1)")
    private Boolean gstIncluded;

    @Column(name = "BreakClauseDate")
    private LocalDateTime breakClauseDate;

    @Column(name = "NoticePeriod", length = 200)
    private String noticePeriod;

    @Column(name = "NoticePeriodMin", precision = 10, scale = 2)
    private BigDecimal noticePeriodMin;

    @Column(name = "NoticePeriodMax", precision = 10, scale = 2)
    private BigDecimal noticePeriodMax;

    @ManyToOne
    @JoinColumn(name = "StatusDefinationID")
    private StatusDefination statusDefination;

    @Column(name = "HidedBy")
    private Integer hidedBy;

    @Column(name = "IsHidden", columnDefinition = "TINYINT(1)")
    private Boolean isHidden;

    @Column(name = "HidedDate")
    private LocalDateTime hidedDate;

    @Column(name = "HideReasonID")
    private Integer hideReasonId;

    @Column(name = "HideReasonComments", columnDefinition = "TEXT")
    private String hideReasonComments;

    @Column(name = "SubHideReasonID")
    private Integer subHideReasonId;

    @Column(name = "Url", length = 255)
    private String url;

    @ManyToOne
    @JoinColumn(name = "ProviderID")
    private Providers providerId;

    @Convert(converter = AvailableSpaceSFMethodsConverter.class)
    @Column(name = "AvailableSpaceSFMethod")
    private AvailableSpaceSFMethods availableSpaceSfMethod;

    @Convert(converter = DateOccupiedMethodsConverter.class)
    @Column(name = "DateOccupiedMethod")
    private DateOccupiedMethods dateOccupiedMethod;

    @Convert(converter = ExpirationDateMethodsConverter.class)
    @Column(name = "ExpirationDateMethod")
    private ExpirationDateMethods expirationDateMethod;

    @Convert(converter = LeaseTermsMethodsConverter.class)
    @Column(name = "LeaseTermsMethod")
    private LeaseTermsMethods leaseTermsMethod;

    @Convert(converter = FaceRentPerSqmMethodsConverter.class)
    @Column(name = "FaceRentPerSqmMethod")
    private FaceRentPerSqmMethods faceRentPerSqmMethod;

    @Convert(converter = StartingRateMethodsConverter.class)
    @Column(name = "StartingRateMethod")
    private StartingRateMethods startingRateMethod;

    @Column(name = "LesseeABN", length = 15)
    private String lesseeAbn;

    @Column(name = "LesseeACN", length = 15)
    private String lesseeAcn;

    @Column(name = "LessorName", length = 200)
    private String lessorName;

    @Column(name = "LessorABN", length = 15)
    private String lessorAbn;

    @Column(name = "LessorACN", length = 15)
    private String lessorAcn;

    @Column(name = "SuiteTypeID")
    private Integer suiteTypeId;

    @Column(name = "BankGuaranteeGST", columnDefinition = "TINYINT(1)")
    private Boolean bankGuaranteeGst;

    @Column(name = "BankGuaranteeComments", length = 1000)
    private String bankGuaranteeComments;

    @Column(name = "IsEscalationConfirmed", columnDefinition = "TINYINT(1)")
    private Boolean isEscalationConfirmed;

    @Column(name = "EscalationComments", length = 1000)
    private String escalationComments;

    @Column(name = "RentFreePeriodComments", length = 1000)
    private String rentFreePeriodComments;

    @Column(name = "BreakClausePeriod")
    private Integer breakClausePeriod;

    @Column(name = "MakeGoodPayment", precision = 10, scale = 2)
    private BigDecimal makeGoodPayment;

    @Column(name = "TotalCleaningChargePerSQM", precision = 10, scale = 2)
    private BigDecimal totalCleaningChargePerSqm;

    @Column(name = "IsHardstandArea", columnDefinition = "TINYINT(1)")
    private Boolean isHardstandArea;

    @Column(name = "HardstandStartingRatePerSQM", precision = 10, scale = 2)
    private BigDecimal hardstandStartingRatePerSqm;

    @Column(name = "PromotionContributionPerAnnum", precision = 10, scale = 2)
    private BigDecimal promotionContributionPerAnnum;

    @Column(name = "PercentageRentComments", length = 1000)
    private String percentageRentComments;

    @Column(name = "IsCarParksAllocatedToThisTenancy", columnDefinition = "TINYINT(1)")
    private Boolean isCarParksAllocatedToThisTenancy;

    @Column(name = "CarParkRatePerSpacePerYear", precision = 10, scale = 2)
    private BigDecimal carParkRatePerSpacePerYear;

    @Column(name = "IsCarParkRatePerSpacePerYearGST", columnDefinition = "TINYINT(1)")
    private Boolean isCarParkRatePerSpacePerYearGst;

    @Column(name = "LicensedAreasCount")
    private Integer licensedAreasCount;

    @Column(name = "LicensedAreasRate", precision = 10, scale = 2)
    private BigDecimal licensedAreasRate;

    @Column(name = "LeaseOutgoingPercentage", precision = 5, scale = 2)
    private BigDecimal leaseOutgoingPercentage;

    @Column(name = "LeaseOutgoingComments", length = 500)
    private String leaseOutgoingComments;

    @Column(name = "EscalationTypeID")
    private Integer escalationTypeId;
}
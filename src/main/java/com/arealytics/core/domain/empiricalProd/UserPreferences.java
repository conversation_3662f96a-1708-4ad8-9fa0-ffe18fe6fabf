package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.utils.UserContext;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;

@Entity
@Table(name = "UserPreferences")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class UserPreferences {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "UserPreferencesID")
  private Integer userPreferencesId;

  @Column(name = "UserID")
  private Integer userId;

  @Column(name = "Type", length = 45)
  private String type;

  @Column(name = "Data", columnDefinition = "json")
  private String data;

  @Column(name = "Screen", length = 45)
  private String screen;

  @Column(name = "CreatedBy")
  private Integer createdBy;

  @Column(name = "CreatedDate", updatable = false)
  private Instant createdDate;

  @Column(name = "ModifiedBy")
  private Integer modifiedBy;

  @Column(name = "ModifiedDate")
  private Instant modifiedDate;

  @PrePersist
  public void prePersist() {
    // Set createdBy during POST
    this.createdDate = Instant.now();
    this.createdBy = UserContext.getLoginEntity().getEntityId();
  }

  @PreUpdate
  public void preUpdate() {
    // Set modifiedBy during POST
    this.modifiedDate = Instant.now();
    this.modifiedBy = UserContext.getLoginEntity().getEntityId();
  }
}

package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "ReleaseUpdates")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ReleaseUpdates extends ALTransactionBaseEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ReleaseID", nullable = false, updatable = false)
  private Integer releaseId;

  @ManyToOne
  @JoinColumn(name = "ApplicationID", nullable = false)
  private Application application;

  @Column(name = "Version", length = 50)
  private String version;

  @Column(name = "Updates", columnDefinition = "TEXT")
  private String updates;

  @Column(name = "ReleaseNoteLink", length = 255)
  private String releaseNoteLink;

  @Column(name = "IsActive")
  private Boolean isActive;
}

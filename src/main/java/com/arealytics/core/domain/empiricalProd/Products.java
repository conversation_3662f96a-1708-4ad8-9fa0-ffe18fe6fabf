package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "Products")
@Getter
@Setter
@NoArgsConstructor
public class Products {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ProductID")
    private Integer productID;

    @Column(name = "ProductName", length = 255)
    private String productName;

    @Column(name = "ProductDescription", length = 1000)
    private String productDescription;

    @Column(name = "IsActive")
    private boolean isActive;

    @Column(name = "ImageUrl", length = 255)
    private String imageUrl;

    @Column(name = "CreatedDate")
    private LocalDateTime createdDate;

}

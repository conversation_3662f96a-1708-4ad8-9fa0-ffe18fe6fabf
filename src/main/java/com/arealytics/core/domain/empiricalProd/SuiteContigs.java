package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Table(name = "SuiteContigs")
public class SuiteContigs extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SuiteContigID")
    private Integer suiteContigID;

    @Column(name = "ContigBlockID")
    private Integer contigBlockID;

    @Column(name = "SuiteID")
    private Integer suiteID;

    @Column(name = "IsActive")
    private Boolean isActive;
}

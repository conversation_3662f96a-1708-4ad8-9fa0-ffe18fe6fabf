package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.enumeration.SupportRoleType;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "CompanySupport")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CompanySupport {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CompanySupportID")
    private Integer companySupportId;

    @ManyToOne
    @JoinColumn(name = "CompanyID")
    private Company company;

    @Column(name = "SupportRoleTypeID")
    private SupportRoleType supportRoleTypeId;

    @ManyToOne
    @JoinColumn(name = "SupportEntityID")
    private EntityModel supportEntityId;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "Sequence")
    private Integer sequence;
}

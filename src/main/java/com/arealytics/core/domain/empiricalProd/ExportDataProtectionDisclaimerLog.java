package com.arealytics.core.domain.empiricalProd;

import java.time.Instant;

import com.arealytics.core.converter.ParentTableConverter;
import com.arealytics.core.enumeration.ParentTable;

import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

import org.apache.logging.log4j.CloseableThreadContext;

@Entity
@Table(name = "ExportDataProtectionDisclaimerLog")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ExportDataProtectionDisclaimerLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ExportDataProtectionDisclaimerLogID")
    private Integer exportDataProtectionDisclaimerLogId;

    @ManyToOne
    @JoinColumn(name = "EntityID")
    private EntityModel entity;

    @Convert(converter = ParentTableConverter.class)
    @Column(name = "ParentTableID")
    private ParentTable parentTableId;

    @Column(name = "IP", length = 45)
    private String ip;

    @Column(name = "CreatedDate")
    private Instant createdDate;
}

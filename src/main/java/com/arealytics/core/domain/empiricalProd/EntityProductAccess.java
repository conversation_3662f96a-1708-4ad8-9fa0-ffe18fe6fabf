package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "EntityProductAccess")
@NoArgsConstructor
public class EntityProductAccess extends ALTransactionBaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "EntityProductAccessID", nullable = false, updatable = false)
  private Integer entityProductAccessId;

  @ManyToOne
  @JoinColumn(name = "EntityID")
  private EntityModel entity;

  @ManyToOne
  @JoinColumn(name = "ProductID")
  private Products product;

  @Column(name = "HasAccess")
  private Boolean hasAccess;

  @Column(name = "IsBillable")
  private Boolean isBillable;

  @Column(name = "IsActive")
  private Boolean isActive;

  @Column(name = "IsLimitedAccess")
  private Boolean isLimitedAccess;

  @Column(name = "EnforceTenantExportLimit")
  private Boolean enforceTenantExportLimit;

  @Column(name = "EnableSaleExport")
  private Boolean enableSaleExport;

  @Column(name = "EnableLeaseExport")
  private Boolean enableLeaseExport;

  @Column(name = "EnableV2Search")
  private Boolean enableV2Search;

  @Column(name = "EnforceProExportLimit")
  private Boolean enforceProExportLimit;
}

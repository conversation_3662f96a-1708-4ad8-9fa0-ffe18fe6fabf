package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Table(name = "PropertyRole")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PropertyRole {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PropertyRoleID")
    private Integer propertyRoleId;

    @Column(name = "PropertyRoleName", length = 100)
    private String propertyRoleName;

    @Column(name = "IsActive", columnDefinition = "TINYINT(1)")
    private Boolean isActive;

    @Column(name = "CreatedDate", columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private LocalDateTime createdDate;
}

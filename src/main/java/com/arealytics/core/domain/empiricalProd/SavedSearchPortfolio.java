package com.arealytics.core.domain.empiricalProd;

import java.time.Instant;

import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.arealytics.core.utils.UserContext;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "SavedSearchPortfolio")
@EntityListeners(AuditingEntityListener.class)
public class SavedSearchPortfolio {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PortfolioID")
    private Integer portfolioID;

    @Column(name = "PortfolioName", length = 100, nullable = false)
    private String portfolioName;

    @Column(name = "IsActive", columnDefinition = "TINYINT(1)")
    private boolean isActive;

    @ManyToOne
    @JoinColumn(name = "CreatedBy")
    private EntityModel createdBy;

    @Column(name = "CreatedDate")
    private Instant createdDate;

    @PrePersist
    public void prePersist() {
        // Set createdBy during POST
        this.createdDate = Instant.now();
        this.createdBy = UserContext.getLoginEntity();
    }

}

package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "ListingStatus")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ListingStatus {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ListingStatusID")
    private Integer listingStatusId;

    @Column(name = "ListingStatusName", unique = true, length = 45)
    private String listingStatusName;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "ListingType")
    private Integer listingType;

    @Column(name = "Sequence")
    private Integer sequence;
}

package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Entity
@Table(name = "GroupEntity")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class GroupEntity extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "GroupEntityID")
    private Integer groupEntityId;

    @Column(name = "EntityID")
    private Integer entityId;

    @Column(name = "GroupID", nullable = false)
    private Integer groupId;

    @ManyToOne
    @JoinColumn(name = "PropertyRoleID", nullable = false)
    private PropertyRole propertyRole;

    @Column(name = "IsActive", columnDefinition = "TINYINT(1)")
    private Boolean isActive;

    @ManyToOne
    @JoinColumn(name = "CompanyID")
    private Company company;

    @Column(name = "OwnershipPercentage", precision = 10, scale = 2)
    private BigDecimal ownershipPercentage;
}

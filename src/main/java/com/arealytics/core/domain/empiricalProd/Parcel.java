package com.arealytics.core.domain.empiricalProd;

import java.math.BigDecimal;

import org.hibernate.envers.Audited;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "Parcel")
@Getter
@Setter
@NoArgsConstructor
@Audited(withModifiedFlag = true)
public class Parcel extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ParcelID")
    private Integer parcelId;

    @Column(name = "ParcelNo", length = 100)
    private String parcelNo;

    @Column(name = "Lot", length = 100)
    private String lot;

    @Column(name = "Block", length = 100)
    private String block;

    @Column(name = "SubDivision", length = 100)
    private String subDivision;

    @Column(name = "ParcelNoFix", length = 100)
    private String parcelNoFix;

    @Column(name = "Section", length = 255)
    private String section;

    @Column(name = "Plan", length = 255)
    private String plan;

    @Column(name = "Plan Type", length = 255)
    private String planType;

    @Column(name = "IsActive", columnDefinition = "TINYINT(1)")
    private Boolean isActive;

    @Column(name = "ParcelSize", precision = 12, scale = 3)
    private BigDecimal parcelSize;
}

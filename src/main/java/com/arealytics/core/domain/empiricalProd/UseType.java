package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "UseType")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UseType {

    @Id
    @Column(name = "UseTypeID", nullable = false, updatable = false)
    private Integer useTypeId;

    @Column(name = "UseTypeName", length = 100)
    private String useTypeName;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "DisplayOrder")
    private Integer DisplayOrder;

    @Column(name = "DisplayColor", length = 10)
    private String DisplayColor;

    @Column(name = "ShowInPublic")
    private Boolean showInPublic;
}

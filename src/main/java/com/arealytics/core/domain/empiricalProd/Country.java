package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.UnitConverter;
import com.arealytics.core.enumeration.Unit;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "Country")
@NoArgsConstructor
public class Country {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CountryID", nullable = false, updatable = false)
    private Integer countryId;

    @Column(name = "CountryName", length = 45)
    private String countryName;

    @Column(name = "Alpha-2Code", length = 2)
    private String alpha2Code;

    @Column(name = "Alpha-3Code", length = 3)
    private String alpha3Code;

    @Column(name = "UNCountryCode", length = 5)
    private String unCountryCode;

    @Column(name = "CallingCountryCode", length = 11)
    private String callingCountryCode;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Convert(converter = UnitConverter.class)
    @Column(name = "UnitID")
    private Unit unitId;

    @Column(name = "DateFormat", length = 45)
    private String dateFormat;

    @Column(name = "DateFormatSave", length = 45)
    private String dateFormatSave;

    @Column(name = "DateFormatRead", length = 45)
    private String dateFormatRead;

    @Column(name = "UnitDisplayTextSize", length = 45)
    private String unitDisplayTextSize;

    @Column(name = "UnitDisplayTextLength", length = 45)
    private String unitDisplayTextLength;

    @Column(name = "MobileNumberPrefix", columnDefinition = "CHAR(3)")
    private String mobileNumberPrefix;

    @Column(name = "MobileNumberMask", length = 45)
    private String mobileNumberMask;

    @Column(name = "MobileNumberRegEx", length = 45)
    private String mobileNumberRegEx;

    @Column(name = "MobileNumberPattern", length = 45)
    private String mobileNumberPattern;

    @Column(name = "PhoneNumberMask", length = 45)
    private String phoneNumberMask;

    @Column(name = "PhoneNumberRegEx", length = 45)
    private String phoneNumberRegEx;

    @Column(name = "PhoneNumberPattern", length = 45)
    private String phoneNumberPattern;
}

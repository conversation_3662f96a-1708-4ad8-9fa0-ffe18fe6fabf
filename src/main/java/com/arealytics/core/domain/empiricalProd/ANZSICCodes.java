package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "ANZSICCodes") // Change name if actual table differs
@Getter
@Setter
public class ANZSICCodes {

    @Id
    @Column(name = "Code")
    private Integer code;

    @Column(name = "ClassDescription", length = 255)
    private String classDescription;

    @ManyToOne
    @JoinColumn(name = "GroupID")
    private ANZSICGroupCodes group;

    @Column(name = "IsActive", columnDefinition = "TINYINT(1)")
    private Boolean isActive;
}

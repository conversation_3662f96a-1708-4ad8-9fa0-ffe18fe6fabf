package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "ANZSICGroupCodes")
@Getter
@Setter
public class ANZSICGroupCodes {

    @Id
    @Column(name = "GroupID")
    private Integer groupId;

    @Column(name = "Group", length = 255)
    private String group;

    @Column(name = "GroupCodeNameAlt", length = 255)
    private String groupCodeNameAlt;

    @Column(name = "SubdivisionID")
    private Integer subdivisionId;

    @Column(name = "Subdivision", length = 255)
    private String subdivision;

    @Column(name = "SubDivisionCodeNameAlt", length = 255)
    private String subDivisionCodeNameAlt;

    @Column(name = "Division", length = 45)
    private String division;

    @Column(name = "DivisionDescription", length = 255)
    private String divisionDescription;

    @Column(name = "DivisionCodeNameAlt", length = 255)
    private String divisionCodeNameAlt;

    @Column(name = "IsActive", length = 45)
    private String isActive;
}

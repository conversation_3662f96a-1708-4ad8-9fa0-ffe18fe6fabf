package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.ParentTableConverter;
import com.arealytics.core.enumeration.ParentTable;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.*;

import java.time.Instant;

@Entity
@Table(name = "UserActivity")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class UserActivity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "UserActivityID", nullable = false, updatable = false)
    private Integer userActivityId;

    @Convert(converter = ParentTableConverter.class)
    @Column(name = "ParentTableID")
    private ParentTable parentTable;

    @Column(name = "UserActivityType", length = 45)
    private String userActivityType;

    @Column(name = "UserCriteria", columnDefinition = "json")
    private JsonNode userCriteria;

    @Column(name = "Details", columnDefinition = "json")
    private JsonNode details;

    @Column(name = "CreatedBy")
    private Integer createdBy;

    @Column(name = "CreatedDate", columnDefinition = "datetime")
    private Instant createdDate;

    @Column(name = "HasUsedInAnalytics")
    private Boolean hasUsedInAnalytics;
}

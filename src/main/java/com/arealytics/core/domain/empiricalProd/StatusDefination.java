package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

@Entity
@Table(name = "StatusDefination")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class StatusDefination {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "StatusDefinationID")
    private Integer statusDefinationID;

    @Column(name = "StatusName")
    private String statusName;

    @Column(name = "IsTenantStatus")
    private Boolean isTenantStatus;

    @Column(name = "IsLeaseStatus")
    private Boolean isLeaseStatus;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "IsPropertyAuditStatus")
    private String isPropertyAuditStatus;
}

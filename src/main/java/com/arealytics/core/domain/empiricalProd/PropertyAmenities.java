package com.arealytics.core.domain.empiricalProd;

import java.math.BigDecimal;

import com.arealytics.core.converter.BuildSpecStatusConverter;
import com.arealytics.core.converter.HVACTypeConverter;
import com.arealytics.core.converter.ZoningClassConverter;
import com.arealytics.core.enumeration.BuildSpecStatus;
import com.arealytics.core.enumeration.HVACType;
import com.arealytics.core.enumeration.ZoningClass;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Embeddable
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PropertyAmenities {

    @Column(name = "Amenities", columnDefinition = "TEXT")
    private String amenities;

    @Column(name = "ParkingSpaces")
    private Integer parkingSpaces;

    @Column(name = "ParkingRatio", length = 25)
    private String parkingRatio;

    @Column(name = "PassengerElevators")
    private Integer passengerElevators;

    @Column(name = "ParkingElevators")
    private Integer parkingElevators;

    @Column(name = "FreighElevators")
    private Integer freighElevators;

    @Column(name = "DockHigh")
    private Integer dockHigh;

    @Column(name = "GradeLevelIn")
    private Integer gradeLevelIn;

    @Column(name = "TruckWell")
    private Integer truckWell;

    @Column(name = "Phase")
    private Integer phase;

    @Column(name = "Volts")
    private Integer volts;

    @Column(name = "Amps")
    private Integer amps;

    @Column(name = "PowerComments", length = 2000)
    private String powerComments;

    @Column(name = "BayWidth", precision = 13, scale = 2)
    private BigDecimal bayWidth;

    @Column(name = "BayDepth", precision = 13, scale = 2)
    private BigDecimal bayDepth;

    @Column(name = "IncludeInAnalytics")
    private Boolean includeInAnalytics;

    @Convert(converter = HVACTypeConverter.class)
    @Column(name = "OfficeAC")
    private HVACType officeAC;

    @Convert(converter = ZoningClassConverter.class)
    @Column(name = "PotentialZoningID")
    private ZoningClass potentialZoningID;

    @Column(name = "RailServed")
    private Boolean railServed;

    @Column(name = "IsFloodPlain")
    private Boolean isFloodPlain;

    @Convert(converter = BuildSpecStatusConverter.class)
    @Column(name = "BuildSpecStatusID")
    private BuildSpecStatus buildSpecStatusID;

    @Column(name = "HasYardFenced")
    private Boolean hasYardFenced;

    @Column(name = "HasYardUnfenced")
    private Boolean hasYardUnfenced;

    @Column(name = "YardPaved")
    private Boolean yardPaved;

    @Column(name = "HasResCoveredParking")
    private Boolean hasResCoveredParking;

    @Column(name = "ReservedParkingSpaces")
    private Integer reservedParkingSpaces;

    @Column(name = "ReservedParkingSpacesRatePerMonth", precision = 10, scale = 2)
    private BigDecimal reservedParkingSpacesRatePerMonth;

    @Column(name = "HasReservedParkingSpaces")
    private Boolean hasReservedParkingSpaces;

    @Column(name = "Depth", precision = 10, scale = 2)
    private BigDecimal depth;

    @Column(name = "Width", precision = 10, scale = 2)
    private BigDecimal width;

    @Column(name = "InternalComments", columnDefinition = "TEXT")
    private String internalComments;

    @Column(name = "NoOfAnchor")
    private Integer noOfAnchor;

    @Column(name = "Adding")
    private Boolean adding;

    @Column(name = "HasSprinkler")
    private Boolean hasSprinkler;

    @Column(name = "HasPortAccess")
    private Boolean hasPortAccess;

    @Column(name = "HasYard")
    private Boolean hasYard;

    @Column(name = "UnreservedParkingSpaces")
    private Integer unreservedParkingSpaces;

    @Column(name = "UnreservedParkingSpacesRatePerMonth", precision = 10, scale = 2)
    private BigDecimal unreservedParkingSpacesRatePerMonth;

    @Column(name = "HasUnreservedParkingSpaces")
    private Boolean hasUnreservedParkingSpaces;

    @Column(name = "PropertyComments", columnDefinition = "TEXT")
    private String propertyComments;

    @Column(name = "UtilityComments", columnDefinition = "TEXT")
    private String utilityComments;

    @Column(name = "NoOfUnits")
    private Integer noOfUnits;

    @Column(name = "TotalAnchor", precision = 12, scale = 3)
    private BigDecimal totalAnchor;

    @Column(name = "HVAC")
    private Boolean hvac;

    @Column(name = "Lifts")
    private Boolean lifts;

    @Column(name = "LiftsCount")
    private Integer liftsCount;

    @Column(name = "PowerType")
    private Integer powerType;

    @Column(name = "Vacancy")
    private Integer vacancy;

    @Column(name = "LCount")
    private Integer lCount;

    @Column(name = "LLCount")
    private Integer llCount;

    @Column(name = "DLCount")
    private Integer dlCount;

    @Column(name = "SLCount")
    private Integer slCount;
}

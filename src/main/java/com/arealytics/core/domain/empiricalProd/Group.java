package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "Group")
public class Group {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "GroupID")
    private Integer groupId;

    @Column(name = "GroupName", length = 255)
    private String groupName;

    @Column(name = "DateModified")
    private LocalDateTime dateModified;
}

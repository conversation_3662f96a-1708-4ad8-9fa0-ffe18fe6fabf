package com.arealytics.core.domain.empiricalProd;

import org.javers.core.metamodel.annotation.DiffIgnore;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "NotesMediaRelationship")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NotesMediaRelationship extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "NotesMediaRelationshipID")
    private Integer notesMediaRelationshipId;

    @Column(name = "IsActive", columnDefinition = "TINYINT")
    private Boolean isActive;

    @OneToOne()
    @JoinColumn(name = "MediaID", nullable = false)
    @DiffIgnore
    private Media media;

    @ManyToOne()
    @JoinColumn(name = "NoteID", nullable = false)
    @DiffIgnore
    private Notes notes;
 
}



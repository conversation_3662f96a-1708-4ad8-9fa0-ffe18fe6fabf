package com.arealytics.core.domain.empiricalProd;

import java.time.LocalDateTime;

import com.arealytics.core.converter.PrefixConverter;
import com.arealytics.core.enumeration.Prefix;
import org.hibernate.envers.Audited;
import org.javers.core.metamodel.annotation.DiffIgnore;

import com.arealytics.core.converter.AddressTypeConverter;
import com.arealytics.core.converter.ComplexTypeConverter;
import com.arealytics.core.converter.ParentTableConverter;
import com.arealytics.core.converter.QuadrantConverter;
import com.arealytics.core.enumeration.AddressType;
import com.arealytics.core.enumeration.ComplexType;
import com.arealytics.core.enumeration.ParentTable;
import com.arealytics.core.enumeration.Quadrant;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "Address")
@Getter
@Setter
@NoArgsConstructor
@Audited(withModifiedFlag = true)
public class Address extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "AddressID")
    private Integer addressId;

    @Convert(converter = AddressTypeConverter.class)
    @Column(name = "AddressTypeID")
    private AddressType addressTypeId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ParentID", insertable = false, updatable = false)
    @DiffIgnore
    private Property property;

    @Column(name = "ParentID")
    private Integer parentId;

    @Convert(converter = ParentTableConverter.class)
    @Column(name = "ParentTableID")
    private ParentTable parentTableId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "LocationID",
            insertable = false,
            updatable = false)
    @DiffIgnore
    private Location location;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "CityID",
            insertable = false,
            updatable = false)
    @Audited(targetAuditMode = org.hibernate.envers.RelationTargetAuditMode.NOT_AUDITED)
    @DiffIgnore
    private City city;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "StateID",
            insertable = false,
            updatable = false)
    @Audited(targetAuditMode = org.hibernate.envers.RelationTargetAuditMode.NOT_AUDITED)
    @DiffIgnore
    private State state;

    @Column(name = "LocationID")
    private Integer locationId;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "Address1", length = 200)
    private String address1;

    @Column(name = "Address2", length = 50)
    private String address2;

    @Column(name = "StateID")
    private Integer stateId;

    @Column(name = "CityID")
    private Integer cityId;

    @Column(name = "ZipCode", length = 10)
    private String zipCode;

    @Column(name = "AddressText", length = 255)
    private String addressText;

    @Column(name = "AddressStreetNumber", length = 45)
    private String addressStreetNumber;

    @Column(name = "AddressStreetName", length = 45)
    private String addressStreetName;

    @Column(name = "SuffixID")
    private Integer suffixId;

    @Column(name = "Suffix2ID")
    private Integer suffix2Id;

    @Column(name = "CountyID")
    private Integer countyId;

    @Column(name = "StreetNumberMin", length = 20)
    private String streetNumberMin;

    @Column(name = "StreetNumberMax", length = 20)
    private String streetNumberMax;

    @Column(name = "Zip4", length = 4)
    private String zip4;

    @Column(name = "FloorNumber", length = 5)
    private String floorNumber;

    @Convert(converter = PrefixConverter.class)
    @Column(name = "PrefixID")
    private Prefix prefixId;

    @Convert(converter = PrefixConverter.class)
    @Column(name = "Prefix2ID")
    private Prefix prefix2Id;

    @Convert(converter = QuadrantConverter.class)
    @Column(name = "QuadrantID")
    private Quadrant quadrantId;

    @Column(name = "BuildingNumber", length = 45)
    private String buildingNumber;

    @Convert(converter = ComplexTypeConverter.class)
    @Column(name = "PartOfCenterComplex")
    private ComplexType partOfCenterComplex;

    @Column(name = "ComplexName", length = 100)
    private String complexName;

    @Column(name = "PrimaryStreet", length = 50)
    private String primaryStreet;

    @Column(name = "PrimaryAccess", length = 100)
    private String primaryAccess;

    @Column(name = "PrimaryTrafficCount", length = 10)
    private String primaryTrafficCount;

    @Column(name = "PrimaryTrafficCountDate")
    private LocalDateTime primaryTrafficCountDate;

    @Column(name = "PrimaryFrontage", length = 20)
    private String primaryFrontage;

    @Column(name = "SecondaryStreet", length = 50)
    private String secondaryStreet;

    @Column(name = "SecondaryAccess", length = 100)
    private String secondaryAccess;

    @Column(name = "SecondaryTrafficCount", length = 10)
    private String secondaryTrafficCount;

    @Column(name = "SecondaryTrafficCountDate")
    private LocalDateTime secondaryTrafficCountDate;

    @Column(name = "SecondaryFrontage", length = 20)
    private String secondaryFrontage;

    @Column(name = "Sequence")
    private Integer sequence;

    @Column(name = "EastWestSt", length = 100)
    private String eastWestSt;

    @Column(name = "NorthSouthSt", length = 100)
    private String northSouthSt;

    @Column(name = "CountryID")
    private Integer countryId;

    @Column(name = "IsIntersection")
    private Boolean isIntersection;

    @Column(name = "StreetNumberMinN")
    private Integer streetNumberMinN;

    @Column(name = "StreetNumberMaxN")
    private Integer streetNumberMaxN;

    @Column(name = "MarketStateID")
    private Integer marketStateId;
}

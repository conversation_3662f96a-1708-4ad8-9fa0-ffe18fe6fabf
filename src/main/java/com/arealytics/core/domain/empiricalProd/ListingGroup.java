package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "ListingGroup")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ListingGroup {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ListingGroupID")
    private Integer listingGroupId;

    @ManyToOne
    @JoinColumn(name = "ListingID", nullable = false)
    private Listing listing;

    @ManyToOne
    @JoinColumn(name = "SuiteID")
    private Suite suite;

    @Column(name = "LotID")
    private Integer lotId;

    @Column(name = "GroupID")
    private Integer groupId;
}

package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "County")
@NoArgsConstructor
public class County {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CountyID", nullable = false, updatable = false)
    private Integer countyId;

    @Column(name = "CountyName", length = 45)
    private String countyName;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "StateID")
    private Integer stateID;

    @Column(name = "MarketStateID")
    private Integer markertStateID;
}

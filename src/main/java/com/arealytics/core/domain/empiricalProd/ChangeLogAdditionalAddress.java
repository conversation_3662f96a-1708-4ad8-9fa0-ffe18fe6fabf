package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.ActionConverter;
import com.arealytics.core.enumeration.Action;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;

@Entity
@Table(name = "ChangeLogAdditionalAddress")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ChangeLogAdditionalAddress{

    @Id
    @Column(name = "AdditionalAddressChangeLogID")
    private Integer additionalAddressChangeLogId;

    @Column(name = "AddressID")
    private Integer addressId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "AddressID", insertable = false, updatable = false)
    private Address address;

    @ManyToOne
    @JoinColumn(name = "ChangeLogFieldID")
    private ChangeLogFields changeLogField;

    @Column(name = "OldValue")
    private String oldValue;

    @Column(name = "NewValue")
    private String newValue;

    private Integer changedBy;

    @Column(name = "ChangedDate")
    private Instant changedDate;

    @ManyToOne
    @JoinColumn(name = "ApplicationID")
    private Application application;

    @Convert(converter = ActionConverter.class)
    @Column(name = "Action")
    private Action action;
}

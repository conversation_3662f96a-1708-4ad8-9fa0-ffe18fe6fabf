package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "SpaceType")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SpaceType {

    @Id
    @Column(name = "SpaceTypeID")
    private Integer spaceTypeId;

    @Column(name = "SpaceTypeName")
    private String spaceTypeName;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "GeneralUseID")
    private Integer generalUseId;
}

package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "ListingForLease")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ListingforLease extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ForLeaseID")
    private Integer forLeaseId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ListingID", insertable = false, updatable = false)
    private Listing listing;

    @Column(name = "ListingForLeasecol", length = 45)
    private String listingForLeaseCol;

    @Column(name = "AskingRate", precision = 13, scale = 2)
    private BigDecimal askingRate;

    @Column(name = "OperatingExpensesPerSF", precision = 13, scale = 2)
    private BigDecimal operatingExpensesPerSF;

    @Column(name = "Sublessor", length = 45)
    private String sublessor;

    @Column(name = "TaxesPerSF", precision = 13, scale = 2)
    private BigDecimal taxesPerSF;

    @Column(name = "SubleaseExpirationDate")
    private LocalDateTime subleaseExpirationDate;

    @Column(name = "MinDiv", precision = 10, scale = 2)
    private BigDecimal minDiv;

    @Column(name = "MaxContig", precision = 10, scale = 2)
    private BigDecimal maxContig;

    @Column(name = "NoOfSpaces")
    private Integer noOfSpaces;

    @Column(name = "AskingLeaseRatePerMonthMin", precision = 10, scale = 2)
    private BigDecimal askingLeaseRatePerMonthMin;

    @Column(name = "AskingLeaseRatePerYearMin", precision = 10, scale = 2)
    private BigDecimal askingLeaseRatePerYearMin;

    @Column(name = "TotalMonthlyAskingMin", precision = 10, scale = 2)
    private BigDecimal totalMonthlyAskingMin;

    @Column(name = "YearCalculated")
    private Integer yearCalculated;

    @Column(name = "LeaseTerms", length = 1000)
    private String leaseTerms;

    @Column(name = "LeaseRatePerMonth", precision = 10, scale = 2)
    private BigDecimal leaseRatePerMonth;

    @Column(name = "LeaseRatePerYear", precision = 10, scale = 2)
    private BigDecimal leaseRatePerYear;

    @Column(name = "LeaseTypeID")
    private Integer leaseTypeId;

    @Column(name = "AskingLeaseRatePerMonthMax", precision = 10, scale = 2)
    private BigDecimal askingLeaseRatePerMonthMax;

    @Column(name = "AskingLeaseRatePerYearMax", precision = 10, scale = 2)
    private BigDecimal askingLeaseRatePerYearMax;

    @Column(name = "TotalMonthlyAskingMax", precision = 10, scale = 2)
    private BigDecimal totalMonthlyAskingMax;

    @Column(name = "TIAllowance", precision = 10, scale = 2)
    private BigDecimal tiAllowance;
}

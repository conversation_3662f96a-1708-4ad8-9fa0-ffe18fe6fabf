package com.arealytics.core.domain.empiricalProd;

import java.math.BigDecimal;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "Metro")
@Getter
@Setter
@NoArgsConstructor
public class Metro extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MetroID")
    private Integer metroId;

    @Column(name = "MetroName", nullable = false, length = 100)
    private String metroName;

    @Column(name = "IsActive", nullable = false)
    private Boolean isActive;

    @Column(name = "Geography", columnDefinition = "geometry")
    private String geography;

    @Column(name = "MsaID")
    private Integer msaId;

    @Column(name = "CenterLatitude", precision = 10, scale = 8)
    private BigDecimal centerLatitude;

    @Column(name = "CenterLongitude", precision = 11, scale = 8)
    private BigDecimal centerLongitude;

    @Column(name = "CountryID")
    private Integer countryId;

    @Column(name = "StateID")
    private Integer stateId;

    @Column(name = "SkylineImageUrl", length = 255)
    private String skylineImageUrl;

    @Column(name = "IsDefault")
    private Boolean isDefault;

    @Column(name = "EnableLeaseExchange")
    private Boolean enableLeaseExchange;

    @Column(name = "ShowRegisteredLease")
    private Boolean showRegisteredLease;
}

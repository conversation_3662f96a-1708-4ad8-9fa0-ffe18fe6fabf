package com.arealytics.core.domain.empiricalProd;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "ZipCode")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ZipCode {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ZipCodeID", nullable = false, updatable = false)
    private Integer zipCodeId;

    @Column(name = "ZipCode")
    private Integer zipCode;

    @Column(name = "CountyID")
    private Integer countyId;

    @Column(name = "StateID")
    private Integer stateId;

    @Column(name = "Latitude", precision = 10, scale = 8)
    private BigDecimal latitude;

    @Column(name = "Longitude", precision = 11, scale = 8)
    private BigDecimal longitude;

    @Column(name = "DST", length = 45)
    private String dst;

    @Column(name = "GMTOffset", precision = 10, scale = 2)
    private BigDecimal gmtOffset;

    @Column(name = "CityID")
    private Integer cityId;

    @Column(name = "CityName", length = 200)
    private String cityName;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Column(name = "CreatedBy")
    private Integer createdBy;

    @Column(name = "CountryID")
    private Integer countryId;

    @Column(name = "MetroID")
    private Integer metroId;

    @Column(name = "MarketStateID")
    private Integer marketStateId;
}

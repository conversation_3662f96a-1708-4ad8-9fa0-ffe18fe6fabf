package com.arealytics.core.converter;

import com.arealytics.core.enumeration.LTSLeaseResearchStatus;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = true)
public class LTSLeaseResearchStatusConverter
        implements AttributeConverter<LTSLeaseResearchStatus, String> {

    @Override
    public String convertToDatabaseColumn(LTSLeaseResearchStatus attribute) {
        return attribute != null ? attribute.getValue() : null;
    }

    @Override
    public LTSLeaseResearchStatus convertToEntityAttribute(String dbData) {
        return dbData != null ? LTSLeaseResearchStatus.fromValue(dbData) : null;
    }
}

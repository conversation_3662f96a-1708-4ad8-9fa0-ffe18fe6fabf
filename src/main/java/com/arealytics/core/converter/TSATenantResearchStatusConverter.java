package com.arealytics.core.converter;

import com.arealytics.core.enumeration.TSATenantResearchStatus;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = true)
public class TSATenantResearchStatusConverter
        implements AttributeConverter<TSATenantResearchStatus, String> {

    @Override
    public String convertToDatabaseColumn(TSATenantResearchStatus attribute) {
        return attribute != null ? attribute.getValue() : null;
    }

    @Override
    public TSATenantResearchStatus convertToEntityAttribute(String dbData) {
        return dbData != null ? TSATenantResearchStatus.fromValue(dbData) : null;
    }
}

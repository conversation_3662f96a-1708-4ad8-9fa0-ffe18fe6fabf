package com.arealytics.core.common.querydsl.query;

import com.arealytics.core.common.querydsl.join.JoinConditions;
import com.arealytics.core.common.querydsl.predicate.PredicateConditions;
import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.dto.request.CircleRadiusDTO;
import com.arealytics.core.dto.request.PropertySearchECRERequestDTO;
import com.arealytics.core.enumeration.ParentTable;
import com.arealytics.core.enumeration.SuiteStatus;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.PrecisionModel;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.arealytics.core.utils.UnitConversionUtil;

public class SubQueryBuilder {

    QListingGroup listingGroup = QListingGroup.listingGroup;
    QSuite suite = QSuite.suite;
    QSuiteContigBlocks suiteContigBlocks = QSuiteContigBlocks.suiteContigBlocks;
    QSuiteContigs suiteContigs = QSuiteContigs.suiteContigs;
    QUse use = QUse.use;
    QLocation location = QLocation.location;
    QProperty property = QProperty.property;
    QListing listing = QListing.listing;
    QGroupRelationship groupRelationship = QGroupRelationship.groupRelationship;
    QAddress address = QAddress.address;
    QListingEntity listingEntity = QListingEntity.listingEntity;
    QEntityModel listingAgent = QEntityModel.entityModel;
    QPerson person = QPerson.person;
    QCompany company = QCompany.company;

    private final PredicateConditions predicateBuilder = new PredicateConditions();


    public List<Tuple> tempSuites(JPAQueryFactory factory, BooleanBuilder predicate, PropertySearchECRERequestDTO searchCriteria) {

        JPQLQuery<Tuple> query = factory
                .select(listingGroup.listing.listingId, listingGroup.suite.suiteID, suite.fitout)
                .from(listingGroup)
                .join(suite).on(suite.suiteID.eq(listingGroup.suite.suiteID)
                        .and(suite.suiteStatusID.eq(SuiteStatus.AVAILABLE))
                        .and(suite.isActive.isTrue())
                );

        if ((searchCriteria.getTotalAvailableMin() == null || searchCriteria.getTotalAvailableMin().equals(BigDecimal.ZERO)) &&
                (searchCriteria.getTotalAvailableMax() == null || searchCriteria.getTotalAvailableMax().equals(BigDecimal.ZERO))) {
            // skip filter
        } else if (searchCriteria.getSuiteLevel() != null && searchCriteria.getSuiteLevel()) {
            if (searchCriteria.getTotalAvailableMin() != null && searchCriteria.getTotalAvailableMax() != null) {
                predicate.or(suite.availableSF.between(
                        searchCriteria.getTotalAvailableMin().subtract(BigDecimal.ONE),
                        searchCriteria.getTotalAvailableMax().add(BigDecimal.ONE)
                ));
                predicate.or(suite.minSF.between(
                        searchCriteria.getTotalAvailableMin().subtract(BigDecimal.ONE),
                        searchCriteria.getTotalAvailableMax().add(BigDecimal.ONE)
                ));
            }

            if (searchCriteria.getTotalAvailableMin() != null && searchCriteria.getTotalAvailableMin().compareTo(BigDecimal.ZERO) > 0 &&
                    (searchCriteria.getTotalAvailableMax() == null || searchCriteria.getTotalAvailableMax().compareTo(BigDecimal.ZERO) == 0)) {
                predicate.or(suite.availableSF.goe(searchCriteria.getTotalAvailableMin()));
                predicate.or(suite.minSF.goe(searchCriteria.getTotalAvailableMin()));
            }

            if ((searchCriteria.getTotalAvailableMin() == null || searchCriteria.getTotalAvailableMin().compareTo(BigDecimal.ZERO) == 0) &&
                    searchCriteria.getTotalAvailableMax() != null && searchCriteria.getTotalAvailableMax().compareTo(BigDecimal.ZERO) > 0) {
                predicate.or(suite.availableSF.loe(searchCriteria.getTotalAvailableMin()));
                predicate.or(suite.minSF.loe(searchCriteria.getTotalAvailableMin()));
            }
        }

        if (searchCriteria.getIsFullFloor() != null && searchCriteria.getIsFullFloor()) {
            predicate.and(suite.isFullFloor.eq(true));
        }

        if (searchCriteria.getFitout() != null) {
            if (searchCriteria.getFitout() == true) {
                predicate.and(
                        suite.fitout.eq(true).or(suite.fitout.isNull())
                );
            } else if (searchCriteria.getFitout() == false) {
                predicate.and(suite.fitout.eq(false));
            }
        }

        List<Tuple> tempSuites = query
                .where(predicate)
                .fetch();

        return tempSuites;
    }

    public List<Tuple> tempContigSuites(JPAQueryFactory factory, BooleanBuilder predicate, PropertySearchECRERequestDTO searchCriteria) {
        JPQLQuery<Tuple> query = factory
                .select(suiteContigBlocks.listingID.listingId, suiteContigBlocks.contigBlockID)
                .from(suiteContigBlocks)
                .join(use).on(
                        use.parentId.eq(suiteContigBlocks.listingID.listingId)
                                .and(use.parentTableId.eq(ParentTable.Listing))
                                .and(use.sequence.eq(1))
                );

        predicate.and(suiteContigBlocks.isActive.eq(true));

        if (searchCriteria.getPropertyTypes() != null) {
            predicate.and(use.useType.useTypeId.in(searchCriteria.getPropertyTypes()));
        }

        BigDecimal min = searchCriteria.getTotalAvailableMin();
        BigDecimal max = searchCriteria.getTotalAvailableMax();
        boolean hasMin = min != null && min.compareTo(BigDecimal.ZERO) > 0;
        boolean hasMax = max != null && max.compareTo(BigDecimal.ZERO) > 0;

        if (searchCriteria.getSuiteLevel() != null && searchCriteria.getSuiteLevel() && searchCriteria.getIsContiguous() != null &&  searchCriteria.getIsContiguous()) {
            BooleanBuilder sizeConditions = new BooleanBuilder();

            sizeConditions.and(suiteContigBlocks.maxSize.isNotNull());

            if (!hasMin && !hasMax) {
                // No size condition needed
            } else {
                BigDecimal adjustedMin = (min != null) ? min.subtract(BigDecimal.ONE) : BigDecimal.ZERO;
                BigDecimal adjustedMax = (max != null) ? max.add(BigDecimal.ONE) : BigDecimal.ZERO;

                // Case 1: Both Min and Max
                if (hasMin && hasMax) {
                    sizeConditions.or(suiteContigBlocks.minSize.goe(adjustedMin).and(suiteContigBlocks.maxSize.loe(adjustedMax))
                    );
                }

                // Case 2: Only Min
                if (hasMin && !hasMax) {
                    sizeConditions.or(suiteContigBlocks.minSize.goe(adjustedMin)
                    );
                }

                // Case 3: Only Max
                if (!hasMin && hasMax) {
                    sizeConditions.or(suiteContigBlocks.minSize.goe(adjustedMax).and(suiteContigBlocks.maxSize.loe(adjustedMax))
                    );
                }
            }

            predicate.and(sizeConditions);
        }

        List<Tuple> tempContigSuites = query
                .where(predicate)
                .fetch();

        return tempContigSuites;
    }

    public List<Integer> getTempSuitesFinalListingIds(JPAQueryFactory factory,PropertySearchECRERequestDTO searchCriteria, List<Integer> listingIds) {

        // First Part: from tempResults -> listingIds
        List<Tuple> tempAllListingSuites = factory.select(listing.listingId,listingGroup.suite.suiteID,listingGroup.groupId)
                .from(listing)
                .join(listingGroup).on(listingGroup.listing.listingId.eq(listing.listingId))
                .join(suite).on(suite.suiteID.eq(listingGroup.suite.suiteID)
                        .and(suite.suiteStatusID.eq(SuiteStatus.AVAILABLE))
                        .and(suite.isActive.isTrue()))
                .where(listing.listingId.in(listingIds)) // where suiteLevel = 0
                .fetch();

        List<Integer> tempAllListingSuiteIds = tempAllListingSuites.stream()
                .map(tuple -> tuple.get(1, Integer.class)) // suiteId is at index 1
                .toList();

        List<Integer> tempSuitesListingIDs = tempSuites(factory, new BooleanBuilder(), searchCriteria).stream()
                .map(tuple -> tuple.get(0, Integer.class)) // listingId is at index 0
                .toList();

        List<Integer> tempContigSuitesContigBlockIDs = tempContigSuites(factory, new BooleanBuilder(), searchCriteria).stream()
                .map(tuple -> tuple.get(1, Integer.class)) // listingId is at index 0
                .toList();

        // Second Part: vw_Suite etc.
        List<Integer> detailedSuiteIds = factory
                .select(suite.suiteID)
                .from(listing)
                .join(listingGroup).on(listingGroup.listing.listingId.eq(listing.listingId))
                .join(groupRelationship).on(groupRelationship.group.groupId.eq(listingGroup.groupId))
                .join(property).on(property.propertyID.eq(groupRelationship.property.propertyID))
                .join(address).on(address.addressId.eq(property.propertyLocation.addressId.addressId))
                .join(listingEntity).on(listingEntity.listing.listingId.eq(listing.listingId)
                        .and(listingEntity.isActive.isTrue())
                        .and(listingEntity.sequence.eq(1)))
                .join(listingAgent).on(listingAgent.entityId.eq(listingEntity.entity.entityId))
                .join(person).on(person.personId.eq(listingAgent.personId))
                .join(company).on(company.companyId.eq(listingAgent.companyId).and(company.isActive.isTrue()))
                .join(suite).on(suite.suiteID.eq(listingGroup.suite.suiteID)
                        .and(suite.suiteStatusID.eq(SuiteStatus.AVAILABLE))
                        .and(suite.isActive.isTrue()))
                .where(listing.listingId.in(listingIds).and(listing.listingId.in(tempSuitesListingIDs)))
                .fetch();

        List<Integer> listingSuiteIds = factory
                .select(suite.suiteID)
                .from(listingEntity)
                .join(listingAgent).on(listingAgent.entityId.eq(listingEntity.entity.entityId))
                .join(company).on(company.companyId.eq(listingAgent.company.companyId).and(company.isActive.isTrue()))
                .join(suite).on(suite.suiteStatusID.eq(SuiteStatus.AVAILABLE).and(suite.isActive.eq(true)))
                .where(listingEntity.isActive.isTrue().and(listingEntity.sequence.eq(1)).and(listingEntity.listing.listingId.in(listingIds)).and(suite.suiteID.in(tempAllListingSuiteIds)))
                .fetch();

        // Third Part: SuiteContigBlocks
        List<Integer> contigSuiteIds = factory
                .select(suiteContigBlocks.contigBlockID)
                .from(suiteContigBlocks)
                .join(suiteContigs).on(suiteContigs.contigBlockID.eq(suiteContigBlocks.contigBlockID).and(suiteContigs.isActive.isTrue()))
                .join(suite).on(suite.suiteID.eq(suiteContigs.suiteID).and(suite.suiteStatusID.eq(SuiteStatus.AVAILABLE)).and(suite.isActive.isTrue()))
                .where(suiteContigBlocks.isActive.isTrue().and(suiteContigBlocks.contigBlockID.in(tempContigSuitesContigBlockIDs)).and(suiteContigBlocks.listingID.listingId.in(listingIds)))
                .groupBy(suiteContigBlocks.contigBlockID)
                .fetch();

        // Combine all suite IDs
        Set<Integer> finalSuiteIds = new HashSet<>();
        finalSuiteIds.addAll(detailedSuiteIds);
        finalSuiteIds.addAll(listingSuiteIds);
        finalSuiteIds.addAll(contigSuiteIds);

        return new ArrayList<>(finalSuiteIds);
    }


    public List<Integer> circleRadiusArrayQuery(JPAQueryFactory factory, BooleanBuilder predicate, List<CircleRadiusDTO> circleList) {

        for (CircleRadiusDTO circle : circleList) {
            GeometryFactory geomFactory = new GeometryFactory(new PrecisionModel(), 0);

            Point center = geomFactory.createPoint(new Coordinate(circle.getLongitude(), circle.getLatitude()));
            double radiusMeters = UnitConversionUtil.milesToMeters(circle.getRadius()); //Miles to meters

            // ST_Distance_Sphere(POINT, locationPoint) <= radiusMeters
            predicate.or(
                    Expressions.booleanTemplate(
                            "ST_Distance_Sphere({0}, {1}) <= {2}",
                            center, location.locationPoint, radiusMeters
                    )
            );
        }

        List<Location> LocationInformation = factory
                .selectFrom(location)
                .join(property).on(JoinConditions.JoinLocationOnPropertyByLocationID(property,location))
                .where(predicate)
                .fetch();

        List<Integer> circleRadiusLocationIDs = LocationInformation.stream()
                .map(tuple -> tuple.getLocationID())
                .toList();

        return circleRadiusLocationIDs;
    }

}

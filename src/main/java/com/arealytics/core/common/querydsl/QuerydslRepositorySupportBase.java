package com.arealytics.core.common.querydsl;

import java.util.List;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;

public abstract class QuerydslRepositorySupportBase<T> {
    private final JPAQueryFactory queryFactory;
    private final Class<T> domainClass;

    protected QuerydslRepositorySupportBase(Class<T> domainClass, JPAQueryFactory queryFactory) {
        this.domainClass = domainClass;
        this.queryFactory = queryFactory;
    }

    protected JPAQueryFactory getQueryFactory() {
        return queryFactory;
    }

    protected <R> List<R> fetch(QueryBuilder<R> builder) {
        return builder.build(queryFactory).fetch();
    }

    protected <R> R fetchOne(QueryBuilder<R> builder) {
        return builder.build(queryFactory).fetchOne();
    }

    @FunctionalInterface
    public interface QueryBuilder<R> {
        JPAQuery<R> build(JPAQueryFactory factory);
    }
}

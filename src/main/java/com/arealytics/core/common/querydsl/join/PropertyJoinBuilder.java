package com.arealytics.core.common.querydsl.join;

import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.dto.request.PropertySearchRequestDTO;
import com.querydsl.jpa.impl.JPAQuery;

public class PropertyJoinBuilder {
  public static <T> JPAQuery<T> JoinSaleOnPropertyWithSaleGroup(QProperty property,
      QGroupRelationship groupRelationship, QSaleGroup saleGroup, QSale sale, JPAQuery<T> query) {
    return query
        .leftJoin(groupRelationship)
        .on(JoinConditions.JoinGroupRelationshipWithProperty(property, groupRelationship))
        .leftJoin(saleGroup).on(JoinConditions.JoinSaleGroupWithGroupRelationship(groupRelationship, saleGroup))
        .leftJoin(sale).on(JoinConditions.JoinSaleWithSaleGroup(saleGroup, sale));
  }

  public static <T> JPAQuery<?> JoinAddressOnProperty(JPAQuery<?> query, QAddress address, QProperty property, PropertySearchRequestDTO searchCriteria) {
    if (searchCriteria.getStateID() != null) {
      query.join(address).on(JoinConditions.JoinAddressOnProperty(address, property));
    }
    return query;
  }

  public static <T> JPAQuery<T> JoinListingForLeaseOnListing(JPAQuery<T> query, QListingforLease listingForLease, QListing listing, PropertySearchRequestDTO searchCriteria) {
    if (searchCriteria.getLeaseRateMin() != null
            || searchCriteria.getLeaseRateMax() != null
            || searchCriteria.getLeaseRateTypeIds() != null) {
      query.join(listingForLease).on(JoinConditions.JoinListingforleaseOnListing(listingForLease, listing));
    }
    return query;
  }

  public static <T> JPAQuery<T> JoinListingForSaleOnListing(JPAQuery<T> query, QListingforSale listingForSale, QListing listing, PropertySearchRequestDTO searchCriteria) {
    if (searchCriteria.getStrataFilter() != null
            || searchCriteria.getSalePriceMin() != null
            || searchCriteria.getSalePriceMax() != null
            || searchCriteria.getSalePricePerSFMax() != null
            || searchCriteria.getSalePricePerSFMin() != null) {
      query.join(listingForSale).on(JoinConditions.JoinListingForSaleWithListing(listingForSale, listing));
    }
    return query;
  }

  public static <T> JPAQuery<?> JoinListingEntityOnListingAgent(JPAQuery<T> query, QListingEntity listingEntity, QEntityModel listingAgent, QListing listing, PropertySearchRequestDTO searchCriteria) {
    if (searchCriteria.getCompanyIds() != null
            || searchCriteria.getAgentIds() != null
            || searchCriteria.getShowOnlyListingsAssignedToMe() != null) {
      query.join(listingEntity).on(JoinConditions.JoinListingEntityWithListing(listingEntity, listing));
      query.join(listingAgent).on(JoinConditions.JoinListingAgentOnListingEntity(listingAgent, listingEntity));
    }
    return query;
  }
}

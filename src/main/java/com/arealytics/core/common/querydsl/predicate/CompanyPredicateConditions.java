package com.arealytics.core.common.querydsl.predicate;

import com.arealytics.core.constants.ListingStatusConstants;
import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.enumeration.PhoneType;
import com.querydsl.core.BooleanBuilder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
public class CompanyPredicateConditions {
  public BooleanBuilder companySearchBuilder(Integer companyId, Integer branchId, String searchText,
      QCompany branch, QCompany parentCompany, QCompany ultimateCompany, QListing listing, QEntityModel entity) {

    BooleanBuilder predicateBuilder = new BooleanBuilder();

    predicateBuilder
        .and(listing.listingStatus.listingStatusId.in(ListingStatusConstants.ACTIVE,
            ListingStatusConstants.ACTIVE_FULLY_LEASED, ListingStatusConstants.PENDING))
        .and(entity.endDate.isNull().or(entity.endDate.goe(LocalDateTime.now())));

    // If companyId or branchId is present then filter by BranchId
    if (companyId != null) {
      predicateBuilder.and(branch.companyId.eq(companyId));
    } else if (branchId != null) {
      predicateBuilder.and(branch.companyId.eq(branchId));
      // If serchText is present then filter by branchName, parent CompanyName etc....
    } else if (searchText != null) {
      BooleanBuilder searchCondition = new BooleanBuilder();

      searchCondition.or(
          branch.altCompanyName.startsWith(searchText)
              .or(branch.companyName.startsWith(searchText))
              .or(parentCompany.altCompanyName.startsWith(searchText))
              .or(parentCompany.companyName.startsWith(searchText))
              .or(ultimateCompany.companyName.startsWith(searchText)));

      predicateBuilder.and(searchCondition);
    }

    return predicateBuilder;
  }

  public BooleanBuilder agentSearchBuilder(List<Integer> companyId, Boolean isListingConatct, QEntityModel entity,
      QCompanyRelationship companyRelationship, QEntityContact entityPhoneContact, QEntityContact entityEmailContact) {

    BooleanBuilder predicateBuilder = new BooleanBuilder();

    predicateBuilder
        .and(entityPhoneContact.contactTypeId.eq(PhoneType.DIRECT_WORK_PHONE))
        .and(entityEmailContact.contactTypeId.eq(PhoneType.EMAIL))
        .and(entity.endDate.isNull().or(entity.endDate.gt(LocalDateTime.now())));

    if (isListingConatct) {
      predicateBuilder.and(companyRelationship.ultimateCompany.companyId.in(companyId));
    } else {
      predicateBuilder.and(entity.company.companyId.in(companyId));
    }

    return predicateBuilder;
  }
}

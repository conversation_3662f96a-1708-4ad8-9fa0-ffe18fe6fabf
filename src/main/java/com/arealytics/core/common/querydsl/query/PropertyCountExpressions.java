package com.arealytics.core.common.querydsl.query;

import com.arealytics.core.common.querydsl.join.JoinConditions;
import com.arealytics.core.domain.empiricalProd.QListing;
import com.arealytics.core.domain.empiricalProd.QListingforLease;
import com.arealytics.core.domain.empiricalProd.QListingforSale;
import com.arealytics.core.domain.empiricalProd.QListingGroup;
import com.arealytics.core.domain.empiricalProd.QProperty;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberExpression;
import com.querydsl.jpa.JPAExpressions;

public class PropertyCountExpressions {

    public static NumberExpression<Long> getLCount(QProperty property, QListing listing, QListingGroup listingGroup) {
        return Expressions.asNumber(
                JPAExpressions.select(listing.count()).from(listing)
                        .join(listingGroup).on(JoinConditions.JoinListingWithListingGroup(listingGroup, listing))
                        .where(listingGroup.groupId.eq(property.propertyDetails.groupID))
        );
    }

    public static NumberExpression<Long> getLLCount(QProperty property, QListing listing, QListingGroup listingGroup, QListingforLease listingforLease) {
        return Expressions.asNumber(
                JPAExpressions.select(listingforLease.count()).from(listingforLease)
                        .join(listing).on(JoinConditions.JoinListingForLeaseWithListing(listingforLease, listing))
                        .join(listingGroup).on(JoinConditions.JoinListingWithListingGroup(listingGroup, listing))
                        .where(listingGroup.groupId.eq(property.propertyDetails.groupID))
        );
    }

    public static NumberExpression<Long> getSLCount(QProperty property, QListing listing, QListingGroup listingGroup, QListingforSale listingforSale) {
        return Expressions.asNumber(
                JPAExpressions.select(listingforSale.count()).from(listingforSale)
                        .join(listing).on(JoinConditions.JoinListingForSaleWithListing(listingforSale, listing))
                        .join(listingGroup).on(JoinConditions.JoinListingWithListingGroup(listingGroup, listing))
                        .where(listingGroup.groupId.eq(property.propertyDetails.groupID))
        );
    }

    public static NumberExpression<Long> getDLCount(QProperty property, QListing listing, QListingGroup listingGroup) {
        return Expressions.asNumber(
                JPAExpressions.select(listing.countDistinct()).from(listing)
                        .join(listingGroup).on(JoinConditions.JoinListingWithListingGroup(listingGroup, listing))
                        .where(listingGroup.groupId.eq(property.propertyDetails.groupID))
        );
    }

}

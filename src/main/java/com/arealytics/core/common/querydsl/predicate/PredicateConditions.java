package com.arealytics.core.common.querydsl.predicate;

import com.arealytics.core.constants.ListingTypeConstants;
import com.arealytics.core.constants.PropertySearchRequestConstants;
import com.arealytics.core.constants.StateConstants;
import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.dto.request.PropertyMapSearchRequestDTO;
import com.arealytics.core.dto.request.PropertySearchDetailsRequestDTO;
import com.arealytics.core.dto.request.PropertySearchECRERequestDTO;
import com.arealytics.core.dto.request.PropertySearchRequestDTO;
import com.arealytics.core.enumeration.*;
import com.arealytics.core.utils.NumberUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.springframework.stereotype.Component;
import com.querydsl.core.BooleanBuilder;

import org.locationtech.jts.geom.*;

import java.io.IOException;
import java.util.*;
import java.math.BigDecimal;
import java.util.stream.Collectors;

import static com.arealytics.core.domain.empiricalProd.QProperty.property;
import static com.arealytics.core.domain.empiricalProd.QUserActivity.userActivity;

import com.querydsl.core.types.dsl.NumberPath;
import software.amazon.awssdk.services.s3.endpoints.internal.Value;

@Component
public class PredicateConditions {

    public BooleanBuilder searchBuild(PropertySearchRequestDTO searchCriteria, QProperty property, QAddress address, QCity city, QState state, QStatusDefination statusDefination) {
        BooleanBuilder predicate = new BooleanBuilder();
        GeometryFactory geometryFactory = new GeometryFactory();

        if (searchCriteria.getPropertyId() != null) {
            predicate.and(property.propertyID.eq(searchCriteria.getPropertyId()));
        }

        if (searchCriteria.getPropertyTypes() != null && !searchCriteria.getPropertyTypes().isEmpty()) {
            predicate.and(property.propertyDetails.useTypeID.in(searchCriteria.getPropertyTypes()));
        }

        if (searchCriteria.getIsSkipped() != null && searchCriteria.getIsSkipped()) {
            predicate.and(property.internalToolFields.isSkipped.eq(searchCriteria.getIsSkipped()));
        }

        if (searchCriteria.getPropertyName() != null) {
            predicate.and(property.propertyName.lower().like("%" + searchCriteria.getPropertyName().toLowerCase() + "%"));
        }

        if (searchCriteria.getStreetName() != null && !searchCriteria.getStreetName().isEmpty()) {
            predicate.and(address.addressStreetName.containsIgnoreCase(searchCriteria.getStreetName()));
        }
        Integer inputMin = searchCriteria.getStreetMin() !=null && !searchCriteria.getStreetMin().isEmpty() ? Integer.valueOf(searchCriteria.getStreetMin()) : null;
        Integer inputMax = searchCriteria.getStreetMax() !=null && !searchCriteria.getStreetMax().isEmpty() ? Integer.valueOf(searchCriteria.getStreetMax()) : null;
        if (searchCriteria.getStreetMin() != null && searchCriteria.getStreetMax() != null ) {
            predicate = predicate.and(address.streetNumberMinN.loe(inputMax).and(address.streetNumberMinN.goe(inputMin)));
            predicate = predicate.and(address.streetNumberMaxN.goe(inputMin).and(address.streetNumberMaxN.loe(inputMax)));
        } else if (searchCriteria.getStreetMin() == null && searchCriteria.getStreetMax() != null) {
            predicate.and(address.streetNumberMaxN.loe(inputMax));
        } else if (searchCriteria.getStreetMin() != null) {
            predicate.and(address.streetNumberMinN.goe(inputMin));
        }

        if (searchCriteria.getStateID() != null) {
            predicate.and(state.stateId.eq(searchCriteria.getStateID()));
        }

        if (searchCriteria.getCityIds() != null && !searchCriteria.getCityIds().isEmpty()) {
            predicate.and(city.cityId.in(searchCriteria.getCityIds()));
        }

        if (searchCriteria.getZipCodes() != null && !searchCriteria.getZipCodes().isEmpty()) {
            predicate.and(address.zipCode.in(searchCriteria.getZipCodes()));
        }

        if (searchCriteria.getIsNotReviewed() != null && searchCriteria.getIsNotReviewed()) {
            predicate.and(property.propertyDetails.lastReviewedBy.isNull().and(property.propertyDetails.lastReviewedDate.isNull()));
        }

        if (searchCriteria.getExcludeHidden() != null && searchCriteria.getExcludeHidden()) {
            predicate.and(property.internalToolFields.researchTypeID.ne(4));
        }

        if (searchCriteria.getBuildingSizeMin() != null && searchCriteria.getBuildingSizeMax() == null) {
            predicate.and(property.propertySize.buildingSizeSF.goe(searchCriteria.getBuildingSizeMin()));
        } else if (searchCriteria.getBuildingSizeMin() == null && searchCriteria.getBuildingSizeMax() != null) {
            predicate.and(property.propertySize.buildingSizeSF.loe(searchCriteria.getBuildingSizeMax()));
        } else if (searchCriteria.getBuildingSizeMin() != null) {
            predicate.and(property.propertySize.buildingSizeSF.between(searchCriteria.getBuildingSizeMin(), searchCriteria.getBuildingSizeMax()));
        }

        if (searchCriteria.getLotSizeSFMin() != null && searchCriteria.getLotSizeSFMax() == null) {
            predicate.and(property.propertySize.lotSizeSF.goe(searchCriteria.getLotSizeSFMin()));
        } else if (searchCriteria.getLotSizeSFMin() == null && searchCriteria.getLotSizeSFMax() != null) {
            predicate.and(property.propertySize.lotSizeSF.loe(searchCriteria.getLotSizeSFMax()));
        } else if (searchCriteria.getLotSizeSFMin() != null) {
            predicate.and(property.propertySize.lotSizeSF.between(searchCriteria.getLotSizeSFMin(), searchCriteria.getLotSizeSFMax()));
        }

        if (searchCriteria.getResearchStatusIds() != null && !searchCriteria.getResearchStatusIds().isEmpty()) {
            predicate.and(property.internalToolFields.researchTypeID.in(searchCriteria.getResearchStatusIds()));
        }

        if (searchCriteria.getPropertyIds() != null && !searchCriteria.getPropertyIds().isEmpty()) {
            predicate.and(property.propertyID.in(searchCriteria.getPropertyIds()));
        }

        if (searchCriteria.getStrataTypeIds() != null && !searchCriteria.getStrataTypeIds().isEmpty()) {
            predicate.and(property.propertyDetails.condoTypeID.in(searchCriteria.getStrataTypeIds()));
        }

        if (searchCriteria.getSpecificUseIds() != null && !searchCriteria.getSpecificUseIds().isEmpty()) {
            predicate.and(property.propertyDetails.specificUseID.in(searchCriteria.getSpecificUseIds()));
        }

        if (searchCriteria.getBuildingClasses() != null && !searchCriteria.getBuildingClasses().isEmpty()) {
            predicate.and(property.propertyDetails.classTypeID.in(searchCriteria.getBuildingClasses()));
        }

        if (searchCriteria.getTenancy() != null && !searchCriteria.getTenancy().isEmpty()) {
            predicate.and(property.propertyDetails.tenancyTypeID.in(searchCriteria.getTenancy()));
        }

        if (searchCriteria.getStrataFilter() != null) {
            if (searchCriteria.getStrataFilter() == PropertySearchRequestConstants.EXCLUDE_STRATA) {
                predicate.and(property.propertyDetails.condoTypeID.ne(CondoType.STRATA));
            } else if (searchCriteria.getStrataFilter() == PropertySearchRequestConstants.ONLY_STRATA) {
                predicate.and(property.propertyDetails.condoTypeID.eq(CondoType.STRATA));
            }
        }

        if (searchCriteria.getConstructionStatus() != null && !searchCriteria.getConstructionStatus().isEmpty()) {
            predicate.and(property.propertyDetails.constructionStatusID.in(searchCriteria.getConstructionStatus()));
        }

        if (searchCriteria.getLastReviewedDateMin() != null && searchCriteria.getLastReviewedDateMax() != null) {
            predicate.and(property.propertyDetails.lastReviewedDate.between(searchCriteria.getLastReviewedDateMin(), searchCriteria.getLastReviewedDateMax()));
        } else if (searchCriteria.getLastReviewedDateMin() != null) {
            predicate.and(property.propertyDetails.lastReviewedDate.goe(searchCriteria.getLastReviewedDateMin()));
        } else if (searchCriteria.getLastReviewedDateMax() != null) {
            predicate.and(property.propertyDetails.lastReviewedDate.loe(searchCriteria.getLastReviewedDateMax()));
        }

        if (searchCriteria.getCreatedDateMin() != null && searchCriteria.getCreatedDateMax() != null) {
            predicate.and(property.createdDate.between(searchCriteria.getCreatedDateMin(), searchCriteria.getCreatedDateMax()));
        } else if (searchCriteria.getCreatedDateMin() != null) {
            predicate.and(property.createdDate.goe(searchCriteria.getCreatedDateMin()));
        } else if (searchCriteria.getCreatedDateMax() != null) {
            predicate.and(property.createdDate.loe(searchCriteria.getCreatedDateMax()));
        }

        if (searchCriteria.getModifiedDateMin() != null && searchCriteria.getModifiedDateMax() != null) {
            predicate.and(property.modifiedDate.between(searchCriteria.getModifiedDateMin(), searchCriteria.getModifiedDateMax()));
        } else if (searchCriteria.getModifiedDateMin() != null) {
            predicate.and(property.modifiedDate.goe(searchCriteria.getModifiedDateMin()));
        } else if (searchCriteria.getModifiedDateMax() != null) {
            predicate.and(property.modifiedDate.loe(searchCriteria.getModifiedDateMax()));
        }

        if (searchCriteria.getHasNoBuildingFootprints() != null && searchCriteria.getHasNoBuildingFootprints()) {
            predicate.and(property.propertyDetails.hasNoBuildingFootprints.eq(searchCriteria.getHasNoBuildingFootprints()));
        }

        if (searchCriteria.getHasNoExistingParcelInTileLayer() != null && searchCriteria.getHasNoExistingParcelInTileLayer()) {
            predicate.and(property.propertyDetails.hasNoExistingParcelInTileLayer.eq(searchCriteria.getHasNoExistingParcelInTileLayer()));
        }

        if (searchCriteria.getNotStrata() != null && searchCriteria.getNotStrata()) {
            predicate.and(property.propertyDetails.condoTypeID.eq(CondoType.NOT_STRATA));
        }

        if (searchCriteria.getAuditStatus() != null) {
            predicate.and(statusDefination.statusDefinationID.in(searchCriteria.getAuditStatus()));
        }

        if (searchCriteria.getResearcherIds() != null) {
            predicate.and(property.modifiedBy.entityId.in(searchCriteria.getResearcherIds()));
        }

        return predicate;
    }

    public BooleanBuilder mapSearchBuild(PropertyMapSearchRequestDTO searchCriteria, QProperty property, QAddress address, QLocation location) {
        BooleanBuilder predicate = new BooleanBuilder();
        GeometryFactory geometryFactory = new GeometryFactory();

        if (searchCriteria.getPropertyId() != null) {
            predicate.and(property.propertyID.eq(searchCriteria.getPropertyId()));
        }

        if (searchCriteria.getZipCode() != null && !searchCriteria.getZipCode().isEmpty()) {
            predicate.and(address.zipCode.eq(searchCriteria.getZipCode()));
        }

        if (searchCriteria.getNELat() != null && searchCriteria.getNELng() != null &&
                searchCriteria.getSWLat() != null && searchCriteria.getSWLng() != null) {

            // Filter properties within the bounding box using latitude and longitude ranges
            predicate.and(location.latitude.between(
                    searchCriteria.getSWLat(), searchCriteria.getNELat()));
            predicate.and(location.longitude.between(
                    searchCriteria.getSWLng(), searchCriteria.getNELng()));
        }

//        if (searchCriteria.getPolygonText() != null) {
//            String polygonText = searchCriteria.getPolygonText();
//            Geometry polygon = new WKTReader().read(polygonText);
//            predicate.and(JTSGeometryExpressions.geometry(location.locationPoint).mbrContains(polygon));
//        }

        return predicate;
    }

    public BooleanBuilder masterSearchBuilder(String searchText, String unit, CondoType strataType, QProperty property,
        QAddress address) {
      BooleanBuilder predicateBuilder = new BooleanBuilder();
      // predicateBuilder.or(property.propertyID.eq(Integer.parseInt(searchText)));

      Optional.ofNullable(searchText).filter(NumberUtils::isInteger).map(Integer::parseInt)
          .ifPresent(id -> predicateBuilder.or(property.propertyID.eq(id)));

      if (unit != null) {
        Optional.ofNullable(unit).filter(NumberUtils::isInteger).map(Integer::parseInt)
            .ifPresent(id -> predicateBuilder.or(property.propertyID.eq(id)));
        predicateBuilder.or(property.propertyDetails.condoUnit.eq(unit));
      }

      predicateBuilder.or(property.propertyName.containsIgnoreCase(searchText));
      predicateBuilder.or(address.addressText.containsIgnoreCase(searchText));

      if (strataType.equals(CondoType.STRATA)) {
        predicateBuilder.and(property.propertyDetails.condoTypeID.eq(CondoType.MASTER_STRATA_RECORD));
      } else if (strataType.equals(CondoType.CHILD_FREEHOLD)) {
        predicateBuilder.and(property.propertyDetails.condoTypeID.eq(CondoType.MASTER_FREEHOLD));
      }

      // Final combined where condition
      predicateBuilder
          .and(property.isActive.isTrue())
          .and(property.internalToolFields.researchTypeID.ne(4));
      return predicateBuilder;
    }
    
    	
    public BooleanBuilder buildPropertyDetailsPredicate(List<Integer> propertyIds, QProperty property) {
        BooleanBuilder predicate = new BooleanBuilder();

        if (propertyIds != null && !propertyIds.isEmpty()) { 
            predicate.and(property.propertyID.in(propertyIds));
        }

        return predicate;
    }

    public BooleanBuilder buildMarketPredicate(QMarket market, QUse use, QProperty property, Integer useTypeId, Integer propertyID, Point point) {
        BooleanBuilder predicate = new BooleanBuilder();

        if (useTypeId != null) {
            predicate.and((use.useType.useTypeId.eq(useTypeId)));
            predicate.and(market.useTypeId.eq(useTypeId));
        }

        if(propertyID != null) {
            predicate.and(property.propertyID.eq(propertyID));
        }

        return predicate;
    }

    public BooleanBuilder buildSubMarketPredicate(QSubMarket subMarket, QMarket market, Integer useTypeId, Point point) {
        BooleanBuilder predicate = new BooleanBuilder();

        if(useTypeId != null) {
            predicate.and(market.useTypeId.eq(useTypeId));
        }

        predicate.and(Expressions.numberTemplate(Integer.class, "MBRContains({0}, {1})", subMarket.geoShape, point).eq(1));

        return predicate;
    }

    public BooleanBuilder buildPredicateByStateAndNonResearchType4(Integer stateId, Integer researchTypeId, QProperty property, QAddress address, QState state, QZipCode zipCode) {
        BooleanBuilder predicate = new BooleanBuilder();

        predicate.and(property.internalToolFields.researchTypeID.ne(researchTypeId));
        predicate.and(state.stateId.eq(stateId));
        predicate.and(zipCode.marketStateId.eq(stateId));
        predicate.and(address.marketStateId.eq(stateId));

        return predicate;
    }

    public BooleanBuilder buildPropertyElasticSearchPredicate(Integer stateId, String searchText, Integer streetMin,
        Integer streetMax, String streetName, Boolean isOdd, QProperty property, QAddress address, QZipCode zipcode,
        QState state, Boolean isElasticSearch) {
      BooleanBuilder predicate = new BooleanBuilder();

      //When not fallback - use elastic search for comma seperated propertyIDs
      if (isElasticSearch) {
        List<Integer> propertyIds = Arrays.stream(searchText.split(","))
            .map(String::trim)
            .filter(NumberUtils::isInteger)
            .map(Integer::parseInt)
            .collect(Collectors.toList());
        predicate.and(property.propertyID.in(propertyIds));
        BooleanBuilder statePredicate = buildPredicateByStateAndNonResearchType4(stateId, 4, property, address, state, zipcode);
        predicate.and(statePredicate);

        return predicate;
      }

      //Search by propertyID
      Optional.ofNullable(searchText).filter(NumberUtils::isInteger).map(Integer::parseInt)
          .ifPresent(id -> predicate.or(property.propertyID.eq(id)));

      //Search by propertyName (property)
      predicate.or(property.propertyName.contains(searchText));
      //Search by addressText (address)
      predicate.or(address.addressText.contains(searchText));

      if (streetMin == null && streetMax == null) {
        predicate.or(address.addressText.contains(searchText));
      } else if (streetMin != null && streetMax != null) { // streetMin and streetMax is Present
        BooleanBuilder rangePredicate = new BooleanBuilder();

        // Case 1: Both StreetNumberMinN and StreetNumberMaxN present
        BooleanExpression minMAxInRangePredicate = address.streetNumberMinN.isNotNull()
            .and(address.streetNumberMaxN.isNotNull())
            // Checking streetMin is in range of (streetNumberMinN, streetNumberMaxN)
            .and(Expressions.numberTemplate(Integer.class, "{0}", streetMin).between(address.streetNumberMinN,
                address.streetNumberMaxN))
            // Checking streetMax is in range of (streetNumberMinN, streetNumberMaxN)
            .and(Expressions.numberTemplate(Integer.class, "{0}", streetMax).between(address.streetNumberMinN,
                address.streetNumberMaxN))
            .and(address.addressText.containsIgnoreCase(streetName));

        // Odd/Even filter
        if (isOdd != null) {
            minMAxInRangePredicate = minMAxInRangePredicate.and(
              isOdd ? address.streetNumberMinN.mod(2).gt(0) : address.streetNumberMinN.mod(2).eq(0));
        }

        // Case 2: Only StreetNumberMinN present
        BooleanExpression minInRangePredicate = address.streetNumberMinN.isNotNull()
            .and(address.streetNumberMaxN.isNull())
            // Checking if streetMin is greater than or equal to streetNumberMinN
            .and(address.streetNumberMinN.goe(streetMin))
            // Checking if streetMin is greater than or equal to streetNumberMaxN
            .and(address.streetNumberMinN.loe(streetMax))
            .and(address.addressText.containsIgnoreCase(streetName));

        if (isOdd != null) {
            minInRangePredicate = minInRangePredicate.and(
              isOdd
                  ? address.streetNumberMinN.mod(2).gt(0)
                  : address.streetNumberMinN.mod(2).eq(0));
        }

        rangePredicate.or(minMAxInRangePredicate).or(minInRangePredicate);
        predicate.or(rangePredicate);
      } else if (streetMin != null) {
          //Checking if streetMin in between streeNumberMinN and streetNumberMaxN
        BooleanExpression condition = address.streetNumberMinN.loe(streetMin)
            .and(address.streetNumberMaxN.goe(streetMin))
            .and(address.addressText.containsIgnoreCase(streetName));

        if (isOdd != null) {
          condition = condition.and(
              isOdd
                  ? address.streetNumberMinN.mod(2).gt(0)
                  : address.streetNumberMinN.mod(2).eq(0));
        }

        predicate.or(condition);
      }

      BooleanBuilder statePredicate = buildPredicateByStateAndNonResearchType4(stateId, 4, property, address, state, zipcode);
      predicate.and(statePredicate);

      return predicate;
    }
  
     public BooleanBuilder ecreSearchBuild(PropertySearchECRERequestDTO searchCriteria, QProperty property, QAddress address, QAmenities amenities, QLocation location, QListing listing, QListingforSale listingforSale, QListingforLease listingforLease, QListingEntity listingEntity, QEntityModel listingAgent) throws ParseException {
        BooleanBuilder predicate = new BooleanBuilder();

        predicate.and(property.internalToolFields.researchTypeID.ne(4).and(property.propertyLocation.location.locationID.isNotNull()));

        if (searchCriteria.getPropertyIds() != null && !searchCriteria.getPropertyIds().isEmpty()) {
            predicate.and(property.propertyID.in(searchCriteria.getPropertyIds()));
        }

        if (searchCriteria.getBuildingClasses() != null && !searchCriteria.getBuildingClasses().isEmpty()) {
            predicate.and(property.propertyDetails.classTypeID.in(searchCriteria.getBuildingClasses()));
        }

        if (searchCriteria.getPropertyTypes() != null && !searchCriteria.getPropertyTypes().isEmpty() && !ListingTypeConstants.LEASE.equals(searchCriteria.getListingType())) {
            predicate.and(property.propertyDetails.useTypeID.in(searchCriteria.getPropertyTypes()));
        }

        if (searchCriteria.getSpecificUseIds() != null && !searchCriteria.getSpecificUseIds().isEmpty()) {
            predicate.and(property.propertyDetails.specificUseID.in(searchCriteria.getSpecificUseIds()));
        }

        if (searchCriteria.getMarketList() != null && !searchCriteria.getMarketList().isEmpty()) {
            predicate.and(property.propertyLocation.marketId.in(searchCriteria.getMarketList()));
        }

        if (searchCriteria.getSubMarketList() != null && !searchCriteria.getSubMarketList().isEmpty()) {
            predicate.and(property.propertyLocation.subMarketID.in(searchCriteria.getSubMarketList()));
        }

        if (searchCriteria.getCityIds() != null && !searchCriteria.getCityIds().isEmpty()) {
            predicate.and(address.city.cityId.in(searchCriteria.getCityIds()));
        }

        if (searchCriteria.getCountyIds() != null && !searchCriteria.getCountyIds().isEmpty()) {
            predicate.and(address.countyId.in(searchCriteria.getCountyIds()));
        }

        if (searchCriteria.getCountryId() != null) {
            predicate.and(address.countryId.eq(searchCriteria.getCountryId()));
        }

        if (searchCriteria.getNABERsEnergy() != null && !searchCriteria.getNABERsEnergy().isEmpty()) {
            predicate.and(property.propertyDetails.energyStarRatingID.in(searchCriteria.getNABERsEnergy()));
        }

        if (searchCriteria.getNABERsWater() != null && !searchCriteria.getNABERsWater().isEmpty()) {
            predicate.and(property.propertyDetails.waterStarRatingID.in(searchCriteria.getNABERsWater()));
        }

        if (searchCriteria.getGreenStar() != null && !searchCriteria.getGreenStar().isEmpty()) {
            predicate.and(property.propertyDetails.greenStarRatingID.in(searchCriteria.getGreenStar()));
        }

        if (searchCriteria.getZipCodes() != null && !searchCriteria.getZipCodes().isEmpty()) {
            predicate.and(address.zipCode.in(searchCriteria.getZipCodes()));
        }

        if (searchCriteria.getTenancyIds() != null && !searchCriteria.getTenancyIds().isEmpty()) {
            predicate.and(property.propertyDetails.tenancyTypeID.in(searchCriteria.getTenancyIds()));
        }

        if (searchCriteria.getConstructionStatuses() != null && !searchCriteria.getConstructionStatuses().isEmpty()) {
            predicate.and(property.propertyDetails.constructionStatusID.in(searchCriteria.getConstructionStatuses()));
        }

        if (searchCriteria.getStrataFilter() != null && !searchCriteria.getStrataFilter().isEmpty()) {
                predicate.and(property.propertyDetails.condoTypeID.in(searchCriteria.getStrataFilter()));
        }

        if (searchCriteria.getPolygonText() != null && searchCriteria.getPolygonText() != "") {
            Geometry polygonShape = new WKTReader().read(searchCriteria.getPolygonText());
            predicate.and(Expressions.booleanTemplate(
                    "ST_Contains({0}, {1})",
                    polygonShape,
                    location.locationPoint
            ));
        }

        if (searchCriteria.getSWLat() != null) {
            String wkt = String.format(
                    "POLYGON((%s %s, %s %s, %s %s, %s %s, %s %s))",
                    searchCriteria.getSWLng(), searchCriteria.getNELat(),
                    searchCriteria.getNELng(), searchCriteria.getNELat(),
                    searchCriteria.getNELng(), searchCriteria.getSWLat(),
                    searchCriteria.getSWLng(), searchCriteria.getSWLat(),
                    searchCriteria.getSWLng(), searchCriteria.getNELat()
            );
            Geometry polygonShape = new WKTReader().read(wkt);
            predicate.and(Expressions.booleanTemplate(
                    "ST_Contains({0}, {1})",
                    polygonShape,
                    location.locationPoint
            ));
        }

        if (searchCriteria.getBuildingSizeMin() != null && !searchCriteria.getBuildingSizeMin().equals(BigDecimal.ZERO) && (searchCriteria.getBuildingSizeMax() == null  || searchCriteria.getBuildingSizeMax().compareTo(BigDecimal.ZERO) == 0)) {
            predicate.and(property.propertySize.buildingSizeSF.goe(searchCriteria.getBuildingSizeSFMin()));
        } else if ((searchCriteria.getBuildingSizeMin() == null  || searchCriteria.getBuildingSizeMin().compareTo(BigDecimal.ZERO) == 0) && searchCriteria.getBuildingSizeMax() != null && !searchCriteria.getBuildingSizeMax().equals(BigDecimal.ZERO)) {
            predicate.and(property.propertySize.buildingSizeSF.loe(searchCriteria.getBuildingSizeSFMax()));
        } else if (searchCriteria.getBuildingSizeMin() != null && !searchCriteria.getBuildingSizeMin().equals(BigDecimal.ZERO)) {
            predicate.and(property.propertySize.buildingSizeSF.between(searchCriteria.getBuildingSizeSFMin(), searchCriteria.getBuildingSizeSFMax()));
        }

        if (searchCriteria.getLotSizeMin() != null && !searchCriteria.getLotSizeMin().equals(BigDecimal.ZERO) && (searchCriteria.getLotSizeMax() == null  || searchCriteria.getLotSizeMax().compareTo(BigDecimal.ZERO) == 0)) {
            predicate.and(property.propertySize.lotSizeSF.goe(searchCriteria.getLotSizeSFMin()));
        } else if ((searchCriteria.getLotSizeMin() == null  || searchCriteria.getLotSizeMin().compareTo(BigDecimal.ZERO) == 0) && searchCriteria.getLotSizeMax() != null && !searchCriteria.getLotSizeMax().equals(BigDecimal.ZERO)) {
            predicate.and(property.propertySize.lotSizeSF.loe(searchCriteria.getLotSizeSFMax()));
        } else if (searchCriteria.getLotSizeMin() != null && !searchCriteria.getLotSizeMin().equals(BigDecimal.ZERO)) {
            predicate.and(property.propertySize.lotSizeSF.between(searchCriteria.getLotSizeSFMin(), searchCriteria.getLotSizeSFMax()));
        }

        if (searchCriteria.getOwnership() != null) {
            predicate.and(property.internalToolFields.trueOwners.lower().like("%" + searchCriteria.getOwnership().toLowerCase() + "%").or(property.internalToolFields.recordedOwners.lower().like("%" + searchCriteria.getOwnership().toLowerCase() + "%")));
        }

        if (searchCriteria.getOwnerOccupiedStatus() != null && searchCriteria.getOwnerOccupiedStatus()) {
            predicate.and(property.propertyDetails.isOwnerOccupied.eq(searchCriteria.getOwnerOccupiedStatus()));
        }

        if (searchCriteria.getGRESBScoreMin() != null && searchCriteria.getGRESBScoreMax() != null) {
            predicate.and((property.propertyDetails.useTypeID.in(2,3,5).and(property.propertyDetails.gresbScoreMin.goe(searchCriteria.getGRESBScoreMin()).and(property.propertyDetails.gresbScoreMax.loe(searchCriteria.getGRESBScoreMax()))))
                    .or(property.propertyDetails.useTypeID.notIn(2,3,5).and(property.propertyDetails.gresbScoreMin.isNull()).and(property.propertyDetails.gresbScoreMin.isNull())));
        }

        if (searchCriteria.getPropertyName() != null) {
            predicate.and(property.propertyName.lower().like("%" + searchCriteria.getPropertyName().toLowerCase() + "%"));
        }

        if (searchCriteria.getStateID() != null) {
            predicate.and(address.stateId.eq(searchCriteria.getStateID()));
        }

        if (searchCriteria.getCompanyIds() != null && !searchCriteria.getCompanyIds().isEmpty()) {
            predicate.and(listingAgent.companyId.in(searchCriteria.getCompanyIds()));
        }

        if (searchCriteria.getAgentIds() != null && !searchCriteria.getAgentIds().isEmpty()) {
            predicate.and(listingEntity.entity.entityId.in(searchCriteria.getAgentIds()));
        }

        if (searchCriteria.getSaleTypeIds() != null && !searchCriteria.getSaleTypeIds().isEmpty()) {
            predicate.and(listingforSale.saleTypeId.in(searchCriteria.getSaleTypeIds()));
        }

        if (searchCriteria.getAgreementTypeIds() != null && !searchCriteria.getAgreementTypeIds().isEmpty()) {
            predicate.and(listing.agreementTypeId.in(searchCriteria.getAgreementTypeIds()));
        }

        if (searchCriteria.getSalePriceMin() != null && !searchCriteria.getSalePriceMin().equals(BigDecimal.ZERO) && (searchCriteria.getSalePriceMax() == null  || searchCriteria.getSalePriceMax().compareTo(BigDecimal.ZERO) == 0)) {
            predicate.and(listingforSale.askingSalePrice.goe(searchCriteria.getSalePriceMin()));
        } else if ((searchCriteria.getSalePriceMin() == null  || searchCriteria.getSalePriceMin().compareTo(BigDecimal.ZERO) == 0) && searchCriteria.getSalePriceMax() != null && !searchCriteria.getSalePriceMax().equals(BigDecimal.ZERO)) {
            predicate.and(listingforSale.askingSalePrice.loe(searchCriteria.getSalePriceMax()));
        } else if (searchCriteria.getSalePriceMin() != null && !searchCriteria.getSalePriceMin().equals(BigDecimal.ZERO)) {
            predicate.and(listingforSale.askingSalePrice.between(searchCriteria.getSalePriceMin(), searchCriteria.getSalePriceMax()));
        }

        if (searchCriteria.getCreatedDateMinFormat() != null && searchCriteria.getCreatedDateMaxFormat() != null) {
            predicate.and(listing.createdDate.between(searchCriteria.getCreatedDateMinFormat(), searchCriteria.getCreatedDateMaxFormat()));
        } else if (searchCriteria.getCreatedDateMinFormat() != null) {
            predicate.and(listing.createdDate.goe(searchCriteria.getCreatedDateMinFormat()));
        } else if (searchCriteria.getCreatedDateMaxFormat() != null) {
            predicate.and(listing.createdDate.loe(searchCriteria.getCreatedDateMaxFormat()));
        }

        if (searchCriteria.getSaleSizeMin() != null && !searchCriteria.getSaleSizeMin().equals(BigDecimal.ZERO) && (searchCriteria.getSaleSizeMax() == null  || searchCriteria.getSaleSizeMax().compareTo(BigDecimal.ZERO) == 0)) {
            predicate.and(listingforSale.saleSize.goe(searchCriteria.getSaleSizeMin()));
        } else if ((searchCriteria.getSaleSizeMin() == null  || searchCriteria.getSaleSizeMin().compareTo(BigDecimal.ZERO) == 0) && searchCriteria.getSaleSizeMax() != null && !searchCriteria.getSaleSizeMax().equals(BigDecimal.ZERO)) {
            predicate.and(listingforSale.saleSize.loe(searchCriteria.getSaleSizeMax()));
        } else if (searchCriteria.getSaleSizeMin() != null && !searchCriteria.getSaleSizeMin().equals(BigDecimal.ZERO)) {
            predicate.and(listingforSale.saleSize.between(searchCriteria.getSaleSizeMin(), searchCriteria.getSaleSizeMax()));
        }

         if (searchCriteria.getTotalAvailableMin() != null && searchCriteria.getTotalAvailableMax() == null && searchCriteria.getSuiteLevel() != null && searchCriteria.getSuiteLevel().equals(false)) {
             predicate.and(property.propertySize.lotSizeSF.goe(searchCriteria.getTotalAvailableMin()));
         } else if (searchCriteria.getTotalAvailableMin() == null && searchCriteria.getTotalAvailableMax() != null && searchCriteria.getSuiteLevel() != null && searchCriteria.getSuiteLevel().equals(false)) {
             predicate.and(property.propertySize.lotSizeSF.loe(searchCriteria.getTotalAvailableMax()));
         } else if (searchCriteria.getTotalAvailableMin() != null && searchCriteria.getTotalAvailableMin() !=null && searchCriteria.getSuiteLevel() != null && searchCriteria.getSuiteLevel().equals(false)) {
             predicate.and(property.propertySize.lotSizeSF.between(searchCriteria.getTotalAvailableMin(), searchCriteria.getTotalAvailableMax()));
         }

         if (searchCriteria.getSalePriceMin() != null && !searchCriteria.getSalePriceMin().equals(BigDecimal.ZERO) && (searchCriteria.getSalePriceMax() == null  || searchCriteria.getSalePriceMax().equals(BigDecimal.ZERO))) {
             predicate.and(listingforSale.askingSalePrice.goe(searchCriteria.getSalePriceMin()));
         } else if ((searchCriteria.getSalePriceMin() == null  || searchCriteria.getSalePriceMin().equals(BigDecimal.ZERO)) && searchCriteria.getSalePriceMax() != null && !searchCriteria.getSalePriceMax().equals(BigDecimal.ZERO)) {
             predicate.and(listingforSale.askingSalePrice.loe(searchCriteria.getSalePriceMax()));
         } else if (searchCriteria.getSalePriceMin() != null && !searchCriteria.getSalePriceMin().equals(BigDecimal.ZERO)) {
             predicate.and(listingforSale.askingSalePrice.between(searchCriteria.getSalePriceMin(), searchCriteria.getSalePriceMax()));
         }

         if (searchCriteria.getLeaseRateMin() != null && !searchCriteria.getLeaseRateMin().equals(BigDecimal.ZERO) && (searchCriteria.getLeaseRateMax() == null  || searchCriteria.getLeaseRateMax().equals(BigDecimal.ZERO))) {
             predicate.and(listingforLease.askingLeaseRatePerYearMin.goe(searchCriteria.getLeaseRateMin()));
         } else if ((searchCriteria.getLeaseRateMin() == null  || searchCriteria.getLeaseRateMin().equals(BigDecimal.ZERO)) && searchCriteria.getLeaseRateMax() != null && !searchCriteria.getLeaseRateMax().equals(BigDecimal.ZERO)) {
             predicate.and(listingforLease.askingLeaseRatePerYearMax.loe(searchCriteria.getLeaseRateMax()));
         } else if (searchCriteria.getLeaseRateMin() != null && !searchCriteria.getLeaseRateMin().equals(BigDecimal.ZERO)) {
             predicate.and(listingforLease.askingLeaseRatePerYearMin.goe(searchCriteria.getLeaseRateMin()));
             predicate.and(listingforLease.askingLeaseRatePerYearMax.loe(searchCriteria.getLeaseRateMax()));
         }

         if (searchCriteria.getLeaseRateTypeIds() != null && !searchCriteria.getLeaseRateTypeIds().isEmpty()) {
             predicate.and(listingforLease.leaseTypeId.in(searchCriteria.getLeaseRateTypeIds()));
         }

         if (!searchCriteria.getListingType().equals(ListingTypeConstants.ALL)) {
             if (searchCriteria.getSubLeaseFilter() != null && Objects.equals(searchCriteria.getSubLeaseFilter(), 2)) {
                 predicate.and(listing.listingTypeId.ne(ListingType.SUBLEASE).or(listing.listingTypeId.isNull())); //Exclude SubLease
             }

             if (searchCriteria.getSubLeaseFilter() != null && Objects.equals(searchCriteria.getSubLeaseFilter(), 3)) {
                 predicate.and(listing.listingTypeId.eq(ListingType.SUBLEASE).or(listing.listingTypeId.isNull())); //SubLease Only
             }

             if (searchCriteria.getCoWorkingFilter() != null && Objects.equals(searchCriteria.getCoWorkingFilter(), 2)) {
                 predicate.and(listing.listingTypeId.ne(ListingType.CO_WORKING).or(listing.listingTypeId.isNull())); //Exclude Coworking
             }

             if (searchCriteria.getCoWorkingFilter() != null && Objects.equals(searchCriteria.getCoWorkingFilter(), 3)) {
                 predicate.and(listing.listingTypeId.eq(ListingType.CO_WORKING).or(listing.listingTypeId.isNull())); //Coworking Only
             }
         }

         return predicate;
    }

    public BooleanBuilder leaseSearchBuild(PropertySearchRequestDTO searchCriteria, QProperty property, QAddress address, QCity city, QState state, QListing listing,QEntityModel listingAgent,QListingEntity listingEntity,QListingforLease listingforLease,QCompanySupport companySupport) {
        BooleanBuilder predicate = new BooleanBuilder();
        GeometryFactory geometryFactory = new GeometryFactory();

        if (searchCriteria.getPropertyTypes() != null && !searchCriteria.getPropertyTypes().isEmpty()) {
            predicate.and(property.propertyDetails.useTypeID.in(searchCriteria.getPropertyTypes()));
        }

        if (searchCriteria.getPropertyId() != null) {
            predicate.and(property.propertyID.eq(searchCriteria.getPropertyId()));
        }

        if (searchCriteria.getListingID() != null) {
            predicate.and(listing.listingId.eq(searchCriteria.getListingID()));
        }

        if (searchCriteria.getStateID() != null) {
            predicate.and(state.stateId.eq(searchCriteria.getStateID()));
        }

        if (searchCriteria.getCityIds() != null && !searchCriteria.getCityIds().isEmpty()) {
            predicate.and(city.cityId.in(searchCriteria.getCityIds()));
        }

        if (searchCriteria.getZipCodes() != null && !searchCriteria.getZipCodes().isEmpty()) {
            predicate.and(address.zipCode.in(searchCriteria.getZipCodes()));
        }

        if (searchCriteria.getBuildingClasses() != null && !searchCriteria.getBuildingClasses().isEmpty()) {
            predicate.and(property.propertyDetails.classTypeID.in(searchCriteria.getBuildingClasses()));
        }

        if (searchCriteria.getTenancy() != null && !searchCriteria.getTenancy().isEmpty()) {
            predicate.and(property.propertyDetails.tenancyTypeID.in(searchCriteria.getTenancy()));
        }

        if (searchCriteria.getFloors() != null) {
            predicate.and(property.propertyDetails.floors.eq(searchCriteria.getFloors()));
        }

        if (searchCriteria.getPropertyName() != null) {
            predicate.and(property.propertyName.lower().like("%" + searchCriteria.getPropertyName().toLowerCase() + "%"));
        }

        if (searchCriteria.getListingStatusIds() != null && !searchCriteria.getListingStatusIds().isEmpty()) {
            predicate.and(listing.listingStatus.listingStatusId.in(searchCriteria.getListingStatusIds()));
        }

        if (searchCriteria.getSpecificUseIds() != null && !searchCriteria.getSpecificUseIds().isEmpty()) {
            predicate.and(property.propertyDetails.specificUseID.in(searchCriteria.getSpecificUseIds()));
        }

        if (searchCriteria.getCompanyId() != null) {
            predicate.and(listingAgent.companyId.eq(searchCriteria.getCompanyId()));
        }

        if (searchCriteria.getAgentIds() != null && !searchCriteria.getAgentIds().isEmpty()) {
            predicate.and(listingEntity.entity.entityId.in(searchCriteria.getAgentIds()));
        }

        if (searchCriteria.getBuildingSizeMin() != null && searchCriteria.getBuildingSizeMax() == null) {
            predicate.and(property.propertySize.buildingSizeSF.goe(searchCriteria.getBuildingSizeMin()));
        } else if (searchCriteria.getBuildingSizeMin() == null && searchCriteria.getBuildingSizeMax() != null) {
            predicate.and(property.propertySize.buildingSizeSF.loe(searchCriteria.getBuildingSizeMax()));
        } else if (searchCriteria.getBuildingSizeMin() != null) {
            predicate.and(property.propertySize.buildingSizeSF.between(searchCriteria.getBuildingSizeMin(), searchCriteria.getBuildingSizeMax()));
        }

        if (searchCriteria.getLeaseRateMin() != null && searchCriteria.getLeaseRateMax() == null) {
            predicate.and(listingforLease.askingLeaseRatePerYearMin.goe(searchCriteria.getLeaseRateMin()));
        } else if (searchCriteria.getLeaseRateMin() == null && searchCriteria.getLeaseRateMax() != null) {
            predicate.and(listingforLease.askingLeaseRatePerYearMax.loe(searchCriteria.getLeaseRateMax()));
        } else if (searchCriteria.getLeaseRateMin() != null) {
            predicate.and(listingforLease.askingLeaseRatePerYearMin.goe(searchCriteria.getLeaseRateMin()).and(listingforLease.askingLeaseRatePerYearMax.loe(searchCriteria.getLeaseRateMax())));
        }

        if (searchCriteria.getLeaseRateTypeIds() != null && !searchCriteria.getLeaseRateTypeIds().isEmpty()) {
            predicate.and(listingforLease.leaseTypeId.in(searchCriteria.getLeaseRateTypeIds()));
        }

        if (searchCriteria.getLotSizeSFMin() != null && searchCriteria.getLotSizeSFMax() == null) {
            predicate.and(property.propertySize.lotSizeSF.goe(searchCriteria.getLotSizeSFMin()));
        } else if (searchCriteria.getLotSizeSFMin() == null && searchCriteria.getLotSizeSFMax() != null) {
            predicate.and(property.propertySize.lotSizeSF.loe(searchCriteria.getLotSizeSFMax()));
        } else if (searchCriteria.getLotSizeSFMin() != null) {
            predicate.and(property.propertySize.lotSizeSF.between(searchCriteria.getLotSizeSFMin(), searchCriteria.getLotSizeSFMax()));
        }

        if (searchCriteria.getCreatedDateMin() != null && searchCriteria.getCreatedDateMax() != null) {
            predicate.and(property.createdDate.between(searchCriteria.getCreatedDateMin(), searchCriteria.getCreatedDateMax()));
        } else if (searchCriteria.getCreatedDateMin() != null) {
            predicate.and(property.createdDate.goe(searchCriteria.getCreatedDateMin()));
        } else if (searchCriteria.getCreatedDateMax() != null) {
            predicate.and(property.createdDate.loe(searchCriteria.getCreatedDateMax()));
        }

        if (searchCriteria.getModifiedDateMin() != null && searchCriteria.getModifiedDateMax() != null) {
            predicate.and(property.modifiedDate.between(searchCriteria.getModifiedDateMin(), searchCriteria.getModifiedDateMax()));
        } else if (searchCriteria.getModifiedDateMin() != null) {
            predicate.and(property.modifiedDate.goe(searchCriteria.getModifiedDateMin()));
        } else if (searchCriteria.getModifiedDateMax() != null) {
            predicate.and(property.modifiedDate.loe(searchCriteria.getModifiedDateMax()));
        }

        if (searchCriteria.getConstructionStatus() != null && !searchCriteria.getConstructionStatus().isEmpty()) {
            predicate.and(property.propertyDetails.constructionStatusID.in(searchCriteria.getConstructionStatus()));
        }

        if (searchCriteria.getStrataTypeIds() != null && !searchCriteria.getStrataTypeIds().isEmpty()) {
            predicate.and(property.propertyDetails.condoTypeID.in(searchCriteria.getStrataTypeIds()));
        }

        if (searchCriteria.getTotalAvailableMin() != null && searchCriteria.getTotalAvailableMax() == null && searchCriteria.getSuiteLevel() != null && searchCriteria.getSuiteLevel() == Boolean.FALSE) {
            predicate.and(property.propertySize.lotSizeSF.goe(searchCriteria.getTotalAvailableMin()));
        } else if (searchCriteria.getTotalAvailableMin() == null && searchCriteria.getTotalAvailableMax() != null && searchCriteria.getSuiteLevel() != null && searchCriteria.getSuiteLevel() == Boolean.FALSE) {
            predicate.and(property.propertySize.lotSizeSF.loe(searchCriteria.getTotalAvailableMax()));
        } else if (searchCriteria.getTotalAvailableMin() != null && searchCriteria.getTotalAvailableMin() !=null && searchCriteria.getSuiteLevel() != null && searchCriteria.getSuiteLevel() == Boolean.FALSE) {
            predicate.and(property.propertySize.lotSizeSF.between(searchCriteria.getTotalAvailableMin(), searchCriteria.getTotalAvailableMax()));
        }

        if (searchCriteria.getSuiteLevel() != null && searchCriteria.getSuiteLevel() ==Boolean.TRUE) {
            //TODO
        }

        if (searchCriteria.getSuiteLevel() != null && searchCriteria.getSuiteLevel() == Boolean.TRUE && searchCriteria.getIsContiguous() != null && searchCriteria.getIsContiguous() == Boolean.TRUE) {
            //TODO
        }

        if (searchCriteria.getShowOnlyListingsAssignedToMe() != null && searchCriteria.getShowOnlyListingsAssignedToMe() ==Boolean.TRUE) {
            predicate.and(companySupport.supportEntityId.entityId.eq(33999));
        }

        return predicate;
    }


    public static BooleanExpression buildPropertySearchDetailsPredicates(
            PropertySearchDetailsRequestDTO propertySearchGridRequestDTO,
            JPAQueryFactory queryFactory
    ) {
        BooleanExpression predicate = Expressions.TRUE;
        ObjectMapper objectMapper = new ObjectMapper();

        if (propertySearchGridRequestDTO.getUserActivityLogID() != null) {
            QUserActivity userActivity = QUserActivity.userActivity;

            String details = String.valueOf(queryFactory
                    .select(userActivity.details)
                    .from(userActivity)
                    .where(userActivity.userActivityId.eq(propertySearchGridRequestDTO.getUserActivityLogID()))
                    .fetchOne());

            if (details != null) {
                try {
                    JsonNode detailsJson = objectMapper.readTree(details);
                    List<Integer> propertyIds = extractIdsFromJson(detailsJson, "PropertyIDs");
                    List<Integer> listingIds = extractIdsFromJson(detailsJson, "ListingIDs");
                    List<Integer> suiteIds = extractIdsFromJson(detailsJson, "SuiteIDs");

                    String listingType = propertySearchGridRequestDTO.getListingType();

                    switch (listingType) {
                        case ListingTypeConstants.ALL:
                            if (!propertyIds.isEmpty()) {
                                predicate = predicate.and(QProperty.property.propertyID.in(propertyIds));
                            }
                            break;

                        case ListingTypeConstants.LEASE:
                            if (!listingIds.isEmpty()) {
                                predicate = predicate.and(QListing.listing.listingId.in(listingIds))
                                        .and(QListing.listing.isActive.eq(true));
                            }
                            if (!suiteIds.isEmpty()) {
                                predicate = predicate.and(QSuite.suite.suiteID.in(suiteIds));
                            }
                            break;

                        case ListingTypeConstants.SALE:
                            if (!listingIds.isEmpty()) {
                                predicate = predicate.and(QListing.listing.listingId.in(listingIds))
                                        .and(QListing.listing.isActive.eq(true));
                            }
                            else {
                                return Expressions.FALSE;
                            }
                            // Note: suite filter is NOT needed for sale-only listings.
                            break;

                        case ListingTypeConstants.LEASEANDSALE:
                            if (!listingIds.isEmpty()) {
                                predicate = predicate.and(QListing.listing.listingId.in(listingIds))
                                        .and(QListing.listing.isActive.eq(true));
                            }
                            if (!suiteIds.isEmpty()) {
                                predicate = predicate.and(QSuite.suite.suiteID.in(suiteIds));
                            }
                            break;

                        default:
                            break;
                    }

                } catch (Exception e) {
                    // log if necessary
                }
            }
        }

        return predicate;
    }

    // Extracts IDs from JSON field (PropertyIDs, ListingIDs, or SuiteIDs)
    public static List<Integer> extractIdsFromJson(JsonNode jsonNode, String fieldName) {
        JsonNode idsNode = jsonNode.get(fieldName);
        if (idsNode == null || idsNode.isNull()) {
            return Collections.emptyList();
        }

        String idsString = idsNode.asText();
        if (idsString.isEmpty()) {
            return Collections.emptyList();
        }

        return Arrays.stream(idsString.split(","))
                .map(String::trim)
                .map(NumberUtils::safeParseInt)
                .collect(Collectors.toList());
    }
    public BooleanBuilder leaseAndSaleSearchERCBuild(PropertySearchRequestDTO searchCriteria,
            QProperty property, QAddress address, QCity city, QState state, QListing listing, QEntityModel listingAgent,
            QListingEntity listingEntity, QListingforLease listingforLease, QListingforSale listingforSale,
            QCompanySupport companySupport, QGroupRelationship groupRelationship, QCompany branch) {
        BooleanBuilder predicate = new BooleanBuilder();
        if (searchCriteria.getPropertyIds() != null && !searchCriteria.getPropertyIds().isEmpty()) {
            predicate.and(property.propertyID.in(searchCriteria.getPropertyIds()));
        }

        if (searchCriteria.getBuildingSizeMin() != null && searchCriteria.getBuildingSizeMax() != null) {
            predicate.and(
                    property.propertySize.buildingSizeSF.between(
                            searchCriteria.getBuildingSizeMin().subtract(BigDecimal.ONE),
                            searchCriteria.getBuildingSizeMax().add(BigDecimal.ONE)));
        } else if (searchCriteria.getBuildingSizeMin() != null) {
            predicate.and(property.propertySize.buildingSizeSF.goe(searchCriteria.getBuildingSizeMin()));
        } else if (searchCriteria.getBuildingSizeMax() != null) {
            predicate.and(property.propertySize.buildingSizeSF.loe(searchCriteria.getBuildingSizeMax()));
        }

        if (searchCriteria.getTenancy() != null && !searchCriteria.getTenancy().isEmpty()) {
            predicate.and(property.propertyDetails.tenancyTypeID.in(searchCriteria.getTenancy()));
        }

        if (searchCriteria.getConstructionStatus() != null && !searchCriteria.getConstructionStatus().isEmpty()) {
            predicate.and(property.propertyDetails.constructionStatusID.in(searchCriteria.getConstructionStatus()));
        }

        if (searchCriteria.getSpecificUseIds() != null && !searchCriteria.getSpecificUseIds().isEmpty()) {
            predicate.and(property.propertyDetails.specificUseID.in(searchCriteria.getSpecificUseIds()));
        }

        if (searchCriteria.getPropertyName() != null && !searchCriteria.getPropertyName().isBlank()) {
            predicate.and(property.propertyName.containsIgnoreCase(searchCriteria.getPropertyName()));
        }

        if (searchCriteria.getStreetName() != null && !searchCriteria.getStreetName().isBlank()) {
            predicate.and(address.addressStreetName.containsIgnoreCase(searchCriteria.getStreetName()));
        }

        if (searchCriteria.getStreetMin() != null && searchCriteria.getStreetMax() != null) {
            predicate.and(address.streetNumberMinN.goe(Integer.valueOf(searchCriteria.getStreetMin())));
            predicate.and(address.streetNumberMaxN.coalesce(0).loe(Integer.valueOf(searchCriteria.getStreetMax())));
        } else if (searchCriteria.getStreetMin() != null) {
            Integer streetMin = Integer.valueOf(searchCriteria.getStreetMin());
            predicate.and(address.streetNumberMinN.loe(streetMin));
            predicate.and(address.streetNumberMaxN.goe(streetMin));
        } else if (searchCriteria.getStreetMax() != null) {
            Integer streetMax = Integer.valueOf(searchCriteria.getStreetMax());
            predicate.and(address.streetNumberMinN.loe(streetMax));
            predicate.and(address.streetNumberMaxN.goe(streetMax));
        }

        if (searchCriteria.getTotalAvailableMin() != null && searchCriteria.getTotalAvailableMax() != null) {
            predicate.and(listing.totalAvailable.between(
                    searchCriteria.getTotalAvailableMin().subtract(BigDecimal.ONE),
                    searchCriteria.getTotalAvailableMax().add(BigDecimal.ONE)));
        } else if (searchCriteria.getTotalAvailableMin() != null) {
            predicate.and(listing.totalAvailable.goe(searchCriteria.getTotalAvailableMin()));
        } else if (searchCriteria.getTotalAvailableMax() != null) {
            predicate.and(listing.totalAvailable.loe(searchCriteria.getTotalAvailableMax()));
        }

        if (searchCriteria.getLeaseRateMin() != null && searchCriteria.getLeaseRateMax() != null) {
            predicate.and(listingforLease.askingLeaseRatePerYearMin
                    .goe(searchCriteria.getLeaseRateMin().subtract(BigDecimal.ONE)));
            predicate.and(listingforLease.askingLeaseRatePerYearMax
                    .loe(searchCriteria.getLeaseRateMax().add(BigDecimal.ONE)));
        } else if (searchCriteria.getLeaseRateMin() != null) {
            predicate.and(listingforLease.askingLeaseRatePerYearMin.goe(searchCriteria.getLeaseRateMin()));
        } else if (searchCriteria.getLeaseRateMax() != null) {
            predicate.and(listingforLease.askingLeaseRatePerYearMax.loe(searchCriteria.getLeaseRateMax()));
        }

        if (searchCriteria.getCityIds() != null && !searchCriteria.getCityIds().isEmpty()) {
            predicate.and(city.cityId.in(searchCriteria.getCityIds()));
        }

        if (searchCriteria.getSalePriceMin() != null && searchCriteria.getSalePriceMax() != null) {
            predicate.and(listingforSale.askingSalePrice.between(
                    searchCriteria.getSalePriceMin().subtract(BigDecimal.ONE),
                    searchCriteria.getSalePriceMax().add(BigDecimal.ONE)));
        } else if (searchCriteria.getSalePriceMin() != null) {
            predicate.and(listingforSale.askingSalePrice.goe(searchCriteria.getSalePriceMin()));
        } else if (searchCriteria.getSalePriceMax() != null) {
            predicate.and(listingforSale.askingSalePrice.loe(searchCriteria.getSalePriceMax()));
        }

        if (searchCriteria.getSalePricePerSFMin() != null && searchCriteria.getSalePricePerSFMax() != null) {
            predicate.and(listingforSale.salePricePerSF.between(
                    searchCriteria.getSalePricePerSFMin(),
                    searchCriteria.getSalePricePerSFMax()));
        } else if (searchCriteria.getSalePricePerSFMin() != null) {
            predicate.and(listingforSale.salePricePerSF.goe(searchCriteria.getSalePricePerSFMin()));
        } else if (searchCriteria.getSalePricePerSFMax() != null) {
            predicate.and(listingforSale.salePricePerSF.loe(searchCriteria.getSalePricePerSFMax()));
        }

        if (searchCriteria.getZipCodes() != null && !searchCriteria.getZipCodes().isEmpty()) {
            predicate.and(address.zipCode.in(searchCriteria.getZipCodes()));
        }

        if (searchCriteria.getBuildingClasses() != null && !searchCriteria.getBuildingClasses().isEmpty()) {
            predicate.and(property.propertyDetails.classTypeID.in(searchCriteria.getBuildingClasses()));
        }

        if (searchCriteria.getLotSizeSFMin() != null && searchCriteria.getLotSizeSFMax() != null) {
            predicate.and(property.propertySize.lotSizeSF.between(
                    searchCriteria.getLotSizeSFMin().subtract(BigDecimal.ONE),
                    searchCriteria.getLotSizeSFMax().add(BigDecimal.ONE)));
        } else if (searchCriteria.getLotSizeSFMin() != null) {
            predicate.and(property.propertySize.lotSizeSF.goe(searchCriteria.getLotSizeSFMin()));
        } else if (searchCriteria.getLotSizeSFMax() != null) {
            predicate.and(property.propertySize.lotSizeSF.loe(searchCriteria.getLotSizeSFMax()));
        }

        if (searchCriteria.getPropertyTypes() != null && !searchCriteria.getPropertyTypes().isEmpty()) {
            predicate.and(property.propertyDetails.useTypeID.in(searchCriteria.getPropertyTypes()));
        }

        if (searchCriteria.getStrataFilter() != null && searchCriteria.getStrataFilter() == PropertySearchRequestConstants.EXCLUDE_STRATA) {
            BooleanExpression nonStrataPropertyWithoutListing = listingforSale.listing.listingId.isNull()
                    .and(property.propertyDetails.condoTypeID.notIn(CondoType.STRATA, CondoType.MASTER_STRATA_RECORD));
            BooleanExpression nonCondoSaleListing = listingforSale.listing.listingId.isNotNull()
                    .and(listingforSale.isCondoSale.isNull()
                            .or(listingforSale.isCondoSale.eq(false)));
            predicate.and(nonStrataPropertyWithoutListing.or(nonCondoSaleListing));
        }

        if (searchCriteria.getStrataTypeIds() != null && !searchCriteria.getStrataTypeIds().isEmpty()) {
            predicate.and(property.propertyDetails.condoTypeID.in(searchCriteria.getStrataTypeIds()));
        }

        if (searchCriteria.getStrataFilter() != null && searchCriteria.getStrataFilter() == PropertySearchRequestConstants.ONLY_STRATA) {
            BooleanExpression nonStrataPropertyWithoutListing = listingforSale.listing.listingId.isNull()
                    .and(property.propertyDetails.condoTypeID.notIn(CondoType.STRATA, CondoType.MASTER_STRATA_RECORD));
            BooleanExpression nonCondoSaleListing = listingforSale.listing.listingId.isNotNull()
                    .and(listingforSale.isCondoSale.isNull()
                            .or(listingforSale.isCondoSale.eq(false)));
            predicate.and(nonStrataPropertyWithoutListing.or(nonCondoSaleListing));
        }

        if (searchCriteria.getListingStatusIds() != null && !searchCriteria.getListingStatusIds().isEmpty()) {
            predicate.and(listing.listingStatus.listingStatusId.in(searchCriteria.getListingStatusIds()));
        }

        if (searchCriteria.getModifiedDateMin() != null && searchCriteria.getModifiedDateMax() != null) {
            predicate.and(listing.modifiedDate.between(searchCriteria.getModifiedDateMin(),
                    searchCriteria.getModifiedDateMax()));
        } else if (searchCriteria.getModifiedDateMin() != null) {
            predicate.and(listing.modifiedDate.goe(searchCriteria.getModifiedDateMin()));
        } else if (searchCriteria.getModifiedDateMax() != null) {
            predicate.and(listing.modifiedDate.loe(searchCriteria.getModifiedDateMax()));
        }

        if (searchCriteria.getCreatedDateMin() != null && searchCriteria.getCreatedDateMax() != null) {
            predicate.and(listing.createdDate.between(searchCriteria.getCreatedDateMin(),
                    searchCriteria.getCreatedDateMax()));
        } else if (searchCriteria.getCreatedDateMin() != null) {
            predicate.and(listing.createdDate.goe(searchCriteria.getCreatedDateMin()));
        } else if (searchCriteria.getCreatedDateMax() != null) {
            predicate.and(listing.createdDate.loe(searchCriteria.getCreatedDateMax()));
        }

        if (searchCriteria.getListingID() != null) {
            predicate.and(listing.listingId.eq(searchCriteria.getListingID()));
        }
        if (searchCriteria.getPropertyId() != null) {
            predicate.and(property.propertyID.eq(searchCriteria.getPropertyId()));
        }
        predicate.and(listingEntity.isActive.eq(true));
        predicate.and(branch.hideAgentsInPublic.isNull().or(branch.hideAgentsInPublic.eq(false)));
        return predicate;
    }

    public BooleanBuilder saleListingLeaseAndSaleSearch(PropertySearchRequestDTO searchCriteria, QProperty property, QAddress address, QCity city, QState state, QListing listing, QEntityModel listingAgent, QListingEntity listingEntity, QListingforLease listingforLease, QListingforSale listingforSale,
                                                        QCompanySupport companySupport, QGroupRelationship groupRelationship, QCompany branch, QUse use) {
        BooleanBuilder predicate = new BooleanBuilder();
        predicate.and(this.leaseAndSaleSearchERCBuild(searchCriteria, property, address, city, state, listing, listingAgent, listingEntity, listingforLease, listingforSale, companySupport, groupRelationship, branch));
        predicate.and(listing.listingStatus.listingStatusId.notIn(5 , 7)).and(listing.isActive.coalesce(true).eq(true));
        // predicate.and(use.parentTableId.eq(ParentTable.Listing)).and(use.sequence.eq(1));
        return predicate;
    }

    public BooleanBuilder saleSearchBuild(PropertySearchRequestDTO searchCriteria, QProperty property, QUseType useType, QUse use, QListing listing, QState state, QAddress address, QSuite suite, QEntityModel listingAgent, QListingEntity listingEntity, QCompanySupport companySupport, QListingforSale listingForSale) {
        BooleanBuilder predicate = new BooleanBuilder();

        Integer pStreetMin = NumberUtils.safeParseInt(searchCriteria.getStreetMin());
        Integer pStreetMax = NumberUtils.safeParseInt(searchCriteria.getStreetMax());

        BigDecimal pBuildingSizeMin = searchCriteria.getBuildingSizeMin();
        BigDecimal pBuildingSizeMax = searchCriteria.getBuildingSizeMax();

        BigDecimal pTotalAvailableMin = searchCriteria.getTotalAvailableMin();
        BigDecimal pTotalAvailableMax = searchCriteria.getTotalAvailableMax();

        BigDecimal pLotSizeSFMin = searchCriteria.getLotSizeSFMin();
        BigDecimal pLotSizeSFMax = searchCriteria.getLotSizeSFMax();

        NumberPath<BigDecimal> sizeValue = property.propertySize.buildingSize;

        predicate.and(address.stateId.eq(57));

        if (searchCriteria.getPropertyTypes() != null && !searchCriteria.getPropertyTypes().isEmpty()) {
            predicate.and(useType.useTypeId.in(searchCriteria.getPropertyTypes()));
        }

        if (searchCriteria.getSpecificUseIds() != null && !searchCriteria.getSpecificUseIds().isEmpty()) {
            predicate.and(use.SpecificUsesID.in(searchCriteria.getSpecificUseIds()));
        }

        if (searchCriteria.getMetroId() != null) {
            predicate.and(property.propertyLocation.metroId.eq(searchCriteria.getMetroId()));
        }

        if (searchCriteria.getPropertyIds() != null && !searchCriteria.getPropertyIds().isEmpty()) {
            predicate.and(property.propertyID.in(searchCriteria.getPropertyIds()));
        }

       if (searchCriteria.getListingID() != null) {
           predicate.and(listing.listingId.eq(searchCriteria.getListingID()));
       }

       if (searchCriteria.getStateID() != null) {
           predicate.and(state.stateId.eq(searchCriteria.getStateID()));
       } else{
           predicate.and(state.stateId.eq(StateConstants.NEW_SOUTH_WALES));
       }

       if (searchCriteria.getCityIds() != null && !searchCriteria.getCityIds().isEmpty()) {
           predicate.and(address.city.cityId.in(searchCriteria.getCityIds()));
       }

       if (searchCriteria.getZipCodes() != null && !searchCriteria.getZipCodes().isEmpty()) {
           predicate.and(address.zipCode.in(searchCriteria.getZipCodes()));
       }

       if (searchCriteria.getBuildingClasses() != null && !searchCriteria.getBuildingClasses().isEmpty()) {
           predicate.and(property.propertyDetails.classTypeID.in(searchCriteria.getBuildingClasses()));
       }

       if (searchCriteria.getTenancy() != null && !searchCriteria.getTenancy().isEmpty()) {
           predicate.and(property.propertyDetails.tenancyTypeID.in(searchCriteria.getTenancy()));
       }

       if (searchCriteria.getFloors() != null) {
           predicate.and(property.propertyDetails.floors.eq(searchCriteria.getFloors()));
       }

       if (searchCriteria.getPropertyName() != null) {
           predicate.and(property.propertyName.lower().like("%" + searchCriteria.getPropertyName().toLowerCase() + "%"));
       }

       if (searchCriteria.getYearBuilt() != null) {
           predicate.and(property.propertyDetails.yearBuilt.eq(searchCriteria.getYearBuilt()));
       }

       if (pStreetMin != null && pStreetMax != null) {
           predicate.and(address.streetNumberMinN.loe(pStreetMin).and(address.streetNumberMaxN.coalesce(0).goe(pStreetMax)));
       } else if (pStreetMin != null) {
           predicate.and(address.streetNumberMinN.loe(pStreetMin).and(address.streetNumberMaxN.goe(pStreetMin)));
       } else if (pStreetMax != null) {
           predicate.and(address.streetNumberMinN.loe(pStreetMax).and(address.streetNumberMaxN.goe(pStreetMax)));
       }

       if (searchCriteria.getStreetName() != null && !searchCriteria.getStreetName().isEmpty()) {
           predicate.and(address.addressStreetName.containsIgnoreCase(searchCriteria.getStreetName()));
       }

       if (pBuildingSizeMin != null && pBuildingSizeMin.compareTo(BigDecimal.ZERO) > 0 &&
               pBuildingSizeMax != null && pBuildingSizeMax.compareTo(BigDecimal.ZERO) > 0) {
           predicate.and(sizeValue.between(pBuildingSizeMin.subtract(BigDecimal.ONE),pBuildingSizeMax.add(BigDecimal.ONE)));
       }
       else if (pBuildingSizeMin != null && pBuildingSizeMin.compareTo(BigDecimal.ZERO) > 0 &&
               (pBuildingSizeMax == null || pBuildingSizeMax.compareTo(BigDecimal.ZERO) == 0)) {
           predicate.and(sizeValue.goe(pBuildingSizeMin));
       }
       else if ((pBuildingSizeMin == null || pBuildingSizeMin.compareTo(BigDecimal.ZERO) == 0) &&
               pBuildingSizeMax != null && pBuildingSizeMax.compareTo(BigDecimal.ZERO) > 0) {
           predicate.and(sizeValue.loe(pBuildingSizeMax));
       }

       if (searchCriteria.getCompanyIds() != null && !searchCriteria.getCompanyIds().isEmpty()) {
           predicate.and(listingAgent.companyId.in(searchCriteria.getCompanyIds()));
       }

       if (searchCriteria.getAgentIds() != null && !searchCriteria.getAgentIds().isEmpty()) {
           predicate.and(listingEntity.entity.entityId.in(searchCriteria.getAgentIds()));
       }

       if (searchCriteria.getConstructionStatus() != null && !searchCriteria.getConstructionStatus().isEmpty()) {
           predicate.and(property.propertyDetails.constructionStatusID.in(searchCriteria.getConstructionStatus()));
       }

       if (searchCriteria.getListingStatusIds() != null && !searchCriteria.getListingStatusIds().isEmpty()) {
           predicate.and(listing.listingStatus.listingStatusId.in(searchCriteria.getListingStatusIds()));
       }

       if (searchCriteria.getPropertyId() != null) {
            predicate.and(property.propertyID.eq(searchCriteria.getPropertyId()));
       }

        if (searchCriteria.getCreatedDateMin() != null && searchCriteria.getCreatedDateMax() != null) {
            predicate.and(property.createdDate.between(searchCriteria.getCreatedDateMin(), searchCriteria.getCreatedDateMax()));
        } else if (searchCriteria.getCreatedDateMin() != null) {
            predicate.and(property.createdDate.goe(searchCriteria.getCreatedDateMin()));
        } else if (searchCriteria.getCreatedDateMax() != null) {
            predicate.and(property.createdDate.loe(searchCriteria.getCreatedDateMax()));
        }

        if (searchCriteria.getModifiedDateMin() != null && searchCriteria.getModifiedDateMax() != null) {
            predicate.and(property.modifiedDate.between(searchCriteria.getModifiedDateMin(), searchCriteria.getModifiedDateMax()));
        } else if (searchCriteria.getModifiedDateMin() != null) {
            predicate.and(property.modifiedDate.goe(searchCriteria.getModifiedDateMin()));
        } else if (searchCriteria.getModifiedDateMax() != null) {
            predicate.and(property.modifiedDate.loe(searchCriteria.getModifiedDateMax()));
        }

        if (searchCriteria.getShowOnlyListingsAssignedToMe() != null && searchCriteria.getShowOnlyListingsAssignedToMe()) {
            predicate.and(companySupport.supportEntityId.entityId.eq(listingAgent.entityId));
        }

        if (searchCriteria.getSalePriceMin() != null && searchCriteria.getSalePriceMax() != null) {
            predicate.and(listingForSale.askingSalePrice.between(
                    searchCriteria.getSalePriceMin().subtract(BigDecimal.ONE),
                    searchCriteria.getSalePriceMax().add(BigDecimal.ONE)));
        } else if (searchCriteria.getSalePriceMin() != null) {
            predicate.and(listingForSale.askingSalePrice.goe(searchCriteria.getSalePriceMin()));
        } else if (searchCriteria.getSalePriceMax() != null) {
            predicate.and(listingForSale.askingSalePrice.loe(searchCriteria.getSalePriceMax()));
        }

        if (searchCriteria.getSalePricePerSFMin() != null && searchCriteria.getSalePricePerSFMax() != null) {
            predicate.and(listingForSale.salePricePerSF.between(
                    searchCriteria.getSalePricePerSFMin(),
                    searchCriteria.getSalePricePerSFMax()));
        } else if (searchCriteria.getSalePricePerSFMin() != null) {
            predicate.and(listingForSale.salePricePerSF.goe(searchCriteria.getSalePricePerSFMin()));
        } else if (searchCriteria.getSalePricePerSFMax() != null) {
            predicate.and(listingForSale.salePricePerSF.loe(searchCriteria.getSalePricePerSFMax()));
        }

        if ((pLotSizeSFMin == null || pLotSizeSFMin.equals(BigDecimal.ZERO)) &&
                (pLotSizeSFMax == null || pLotSizeSFMax.equals(BigDecimal.ZERO))) {
        } else if (pLotSizeSFMin != null && pLotSizeSFMax != null && pLotSizeSFMin.compareTo(BigDecimal.ZERO) > 0 && pLotSizeSFMax.compareTo(BigDecimal.ZERO) > 0) {
            predicate = predicate.and(property.propertySize.lotSizeSF.between(pLotSizeSFMin.subtract(BigDecimal.ONE),pLotSizeSFMax.add(BigDecimal.ONE)
            ));
        } else if (pLotSizeSFMin != null && pLotSizeSFMin.compareTo(BigDecimal.ZERO) > 0 && (pLotSizeSFMax == null || pLotSizeSFMax.equals(BigDecimal.ZERO))) {
            predicate = predicate.and(property.propertySize.lotSize.goe(pLotSizeSFMin));
        } else if ((pLotSizeSFMin == null || pLotSizeSFMin.equals(BigDecimal.ZERO)) && pLotSizeSFMax != null && pLotSizeSFMax.compareTo(BigDecimal.ZERO) > 0) {
            predicate = predicate.and(property.propertySize.lotSizeSF.loe(pLotSizeSFMax));
        }

        if (searchCriteria.getSubLeaseCategory() != null && !searchCriteria.getSubLeaseCategory().equalsIgnoreCase("Include SubLease")) {
            if (searchCriteria.getSubLeaseCategory().equalsIgnoreCase("Exclude SubLease")) {
                predicate = predicate.and(listing.listingTypeId.ne(ListingType.SUBLEASE));
            } else if (searchCriteria.getSubLeaseCategory().equalsIgnoreCase("SubLease Only")) {
                predicate = predicate.and(listing.listingTypeId.eq(ListingType.SUBLEASE));
            }
        }

        if (searchCriteria.getStrataFilter() != null) {
            if (searchCriteria.getStrataFilter() == PropertySearchRequestConstants.EXCLUDE_STRATA) {
                predicate.and(property.propertyDetails.condoTypeID.ne(CondoType.STRATA));
            } else if (searchCriteria.getStrataFilter() == PropertySearchRequestConstants.ONLY_STRATA) {
                predicate.and(property.propertyDetails.condoTypeID.eq(CondoType.STRATA));
            }
        }

        if (searchCriteria.getTotalAvailableMin() != null && searchCriteria.getTotalAvailableMax() != null) {
            predicate.and(listing.totalAvailable.between(
                    searchCriteria.getTotalAvailableMin().subtract(BigDecimal.ONE),
                    searchCriteria.getTotalAvailableMax().add(BigDecimal.ONE)));
        } else if (searchCriteria.getTotalAvailableMin() != null) {
            predicate.and(listing.totalAvailable.goe(searchCriteria.getTotalAvailableMin()));
        } else if (searchCriteria.getTotalAvailableMax() != null) {
            predicate.and(listing.totalAvailable.loe(searchCriteria.getTotalAvailableMax()));
        }
        predicate.and(address.parentId.eq(property.propertyID).and(address.parentTableId.eq(ParentTable.Property)));
        predicate.and(suite.suiteStatusID.isNull().or(suite.suiteStatusID.eq(SuiteStatus.AVAILABLE)));
        return  predicate;
    }
}

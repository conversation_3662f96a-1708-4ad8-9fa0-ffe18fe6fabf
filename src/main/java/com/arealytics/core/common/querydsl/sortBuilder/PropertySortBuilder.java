package com.arealytics.core.common.querydsl.sortBuilder;

import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.dto.request.PropertySearchDetailsRequestDTO;
import com.arealytics.core.enumeration.QuerySortDirection;
import com.arealytics.core.enumeration.StrataDetailsSortBy;

import static com.arealytics.core.domain.empiricalProd.QMarket.market;
import static com.arealytics.core.domain.empiricalProd.QSubMarket.subMarket;

import com.arealytics.core.common.querydsl.query.PropertyCountExpressions;
import com.arealytics.core.stringTemplates.PropertyStringTemplates;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.NumberExpression;
import com.querydsl.core.types.dsl.StringExpression;

public class PropertySortBuilder {
  
  public static OrderSpecifier<?> getOrderForStrataDetails(StrataDetailsSortBy sortby, QuerySortDirection direction,
      QProperty property, QAddress address, QSale sale, StringExpression strataType) {
    Order order = direction.equals(QuerySortDirection.DESCENDING) ? Order.DESC : Order.ASC;
    
    OrderSpecifier<?> sortOrder = switch (sortby) {
      case STRATA_TYPE -> new OrderSpecifier<>(order, strataType);
      case ADDRESS -> new OrderSpecifier<>(order, address.addressText);
      case STRATA_UNIT -> new OrderSpecifier<>(order, property.propertyDetails.condoUnit);
      case BUILDING_SF -> new OrderSpecifier<>(order, property.propertySize.buildingSizeSF);
      case LOT_SIZE_SF -> new OrderSpecifier<>(order, property.propertySize.lotSizeSF);
      case BUILDING_SIZE_SM -> new OrderSpecifier<>(order, property.propertySize.buildingSizeSF); // SM = derived later
      case LOT_SIZE_SM -> new OrderSpecifier<>(order, property.propertySize.lotSizeSF);
      case LAST_SALE_DATE -> new OrderSpecifier<>(order, sale.deedOrSaleDate);
      case LAST_SALE_PRICE -> new OrderSpecifier<>(order, sale.soldPrice);
      case PARCEL_NUMBERS -> new OrderSpecifier<>(order, property.propertyDetails.parcelInfo);
      case PROPERTY_ID -> new OrderSpecifier<>(order, property.propertyID);
      default -> throw new IllegalArgumentException("Unhandled sort option: " + sortby);
    };
    return sortOrder;
  }

 public static OrderSpecifier<?> getOrderForPropertySearch(
         NumberExpression<Long> lCount, NumberExpression<Long> dlCount, NumberExpression<Long> slCount,
         NumberExpression<Long> llCount,
         PropertySearchDetailsRequestDTO requestDTO,
        QProperty property,
        QAddress address,
        QCity city,
        QCounty county,
        QUseType useType,
        QSpecificUses specificUses,
        QListingforLease listingforLease,
        QListingforSale listingforSale,
        QListing listing,
        QListingGroup listingGroup,
        QPerson listingAgentPerson,
        QMarket market,
        QSubMarket subMarket) {

    String sortBy = requestDTO.getSortBy() != null ? requestDTO.getSortBy() : "PropertyName";
    String sortDirection = requestDTO.getSortDirection() != null ? requestDTO.getSortDirection() : "Ascending";
    Order order = sortDirection.equalsIgnoreCase("Ascending") ? Order.ASC : Order.DESC;

    // Centralized count expressions

    return switch (sortBy) {
        case "PropertyID" ->
                new OrderSpecifier<>(order, property.propertyID);

        case "PropertyName" ->
                new OrderSpecifier<>(order, PropertyStringTemplates.getPropertyName(property, address));

        case "City" ->
                new OrderSpecifier<>(order, city.cityName);

        case "County" ->
                new OrderSpecifier<>(order, county.countyName);

        case "PropertyType" ->
                new OrderSpecifier<>(order, useType.useTypeName);

        case "Address" ->
                new OrderSpecifier<>(order, address.addressText);

        case "SpecificUses" ->
                new OrderSpecifier<>(order, specificUses.specificUsesName);

        case "Price" ->
                new OrderSpecifier<>(order, PropertyStringTemplates.getPrice(llCount, slCount, listingforLease, listingforSale, property));

        case "ListingCompanyName" ->
                new OrderSpecifier<>(order, PropertyStringTemplates.getListingCompanyName(lCount, dlCount, slCount, property));

        case "AgentName" ->
                new OrderSpecifier<>(order, PropertyStringTemplates.getAgentName(lCount, dlCount, slCount, listingAgentPerson));

        case "MarketName" ->
                new OrderSpecifier<>(order, market.marketName);

        case "SubMarketName" ->
                new OrderSpecifier<>(order, subMarket.subMarketName);

        case "BuildingSizeSM" ->
                new OrderSpecifier<>(order, property.propertySize.buildingSize);

        case "TotalAvailableSM" ->
                new OrderSpecifier<>(order, PropertyStringTemplates.getTotalAvailableSM(lCount, dlCount, slCount, listing, property));

        default ->
                new OrderSpecifier<>(Order.ASC, PropertyStringTemplates.getPropertyName(property, address));
    };
}

public static OrderSpecifier<?> getOrderBySaleSearch(
                PropertySearchDetailsRequestDTO requestDTO,
                QProperty property,
                QAddress address,
                QCity city,
                QCounty county,
                QUseType useType,
                QSpecificUses specificUses,
                QListingforLease listingforLease,
                QListingforSale listingforSale,
                QListing listing,
                QListingGroup listingGroup,
                QPerson listingAgentPerson,
                QMarket market,
                QSubMarket subMarket) {

        String sortBy = requestDTO.getSortBy() != null ? requestDTO.getSortBy() : "PropertyName";
        String sortDirection = requestDTO.getSortDirection() != null ? requestDTO.getSortDirection() : "Ascending";
        Order order = sortDirection.equalsIgnoreCase("Ascending") ? Order.ASC : Order.DESC;

        // Centralized count expressions

        return switch (sortBy) {
                case "PropertyID" ->
                        new OrderSpecifier<>(order, property.propertyID);

                case "PropertyName" ->
                        new OrderSpecifier<>(order, PropertyStringTemplates.getPropertyName(property, address));

                case "City" ->
                        new OrderSpecifier<>(order, city.cityName);

                case "County" ->
                        new OrderSpecifier<>(order, county.countyName);

                case "PropertyType" ->
                        new OrderSpecifier<>(order, useType.useTypeName);

                case "Address" ->
                        new OrderSpecifier<>(order, address.addressText);

                case "SpecificUses" ->
                        new OrderSpecifier<>(order, specificUses.specificUsesName);

                case "Price" ->
                        new OrderSpecifier<>(order, listingforSale.askingSalePrice);

                case "BuildingSizeSM" ->
                        new OrderSpecifier<>(order, property.propertySize.buildingSize);

                case "TotalAvailableSM" ->
                        new OrderSpecifier<>(order, listing.totalAvailable);

                case "SalePricePerSF" ->
                        new OrderSpecifier<>(order, listingforSale.saleSize);

                default ->
                        new OrderSpecifier<>(Order.ASC, PropertyStringTemplates.getPropertyName(property, address));
        };
}

}

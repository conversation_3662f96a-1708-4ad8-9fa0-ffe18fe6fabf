package com.arealytics.core.specification;

import com.arealytics.core.domain.empiricalProd.Market;
import org.springframework.data.jpa.domain.Specification;

public class MarketSpecification {

    public static Specification<Market> isActive() {
        return (root, query, cb) -> cb.isTrue(root.get("isActive"));
    }

    public static Specification<Market> hasStateId(Integer stateId) {
        return (root, query, cb) -> stateId != null ? cb.equal(root.get("metro").get("stateId"), stateId) : null;
    }

    public static Specification<Market> hasMetroId(Integer metroId) {
        return (root, query, cb) -> metroId != null ? cb.equal(root.get("metro").get("metroId"), metroId) : null;
    }

    public static Specification<Market> hasUseTypeId(Integer useTypeId) {
        return (root, query, cb) -> useTypeId != null ? cb.equal(root.get("useType").get("useTypeId"), useTypeId)
                : null;
    }
}

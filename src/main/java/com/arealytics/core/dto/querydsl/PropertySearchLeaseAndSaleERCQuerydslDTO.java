package com.arealytics.core.dto.querydsl;

import com.arealytics.core.dto.response.AddressDTO;
import com.arealytics.core.dto.response.LocationDTO;
import com.arealytics.core.dto.response.PropertySearchLeaseAndSaleResponseDTO;
import com.arealytics.core.enumeration.CondoType;
import com.arealytics.core.enumeration.RecordType;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertySearchLeaseAndSaleERCQuerydslDTO {
        private Integer propertyID;
        private String propertyName;
        private Integer useTypeID;
        private String useTypeName;
        private Integer specificUseID;
        private String specificUseName;
        private BigDecimal buildingSizeSF;
        private BigDecimal buildingSizeSM;
        private CondoType condoTypeID;
        private RecordType recordTypeName;
        private String agentName;
        private BigDecimal askingSalePrice;
        private String listingCompanyName;
        private Integer listingID;
        private Integer modDateCounter;
        private String price;
        private String modifiedDate;
        private BigDecimal salePricePerSF;
        private BigDecimal salePricePerSM;
        private BigDecimal totalAvailable;
        private BigDecimal totalAvailableSM;
        private AddressDTO address;
        private LocationDTO location;
        private PropertySearchLeaseAndSaleResponseDTO LeaseAndSalePropertiesDetails;
}


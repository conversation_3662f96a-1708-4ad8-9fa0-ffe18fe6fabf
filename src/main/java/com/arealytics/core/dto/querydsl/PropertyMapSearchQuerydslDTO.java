package com.arealytics.core.dto.querydsl;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.arealytics.core.domain.empiricalProd.EntityModel;
import com.arealytics.core.dto.response.AddressDTO;
import com.arealytics.core.dto.response.BaseDTO;
import com.arealytics.core.dto.response.LocationDTO;
import com.arealytics.core.enumeration.*;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertyMapSearchQuerydslDTO {

    private Integer propertyID;

    private String propertyName;

    private Integer yearBuilt;

    private CondoType condoTypeID;

    private String CondoUnit;

    private Integer useTypeID;

    private String useTypeName;

    private Integer specificUseID;

    private String specificUseName;

    @JsonProperty("BuildingSF")
    private BigDecimal buildingSizeSF;

    @JsonProperty("LotSizeSF")
    private BigDecimal lotSizeSF;

    @JsonProperty("ContributedGBASizeSF")
    private BigDecimal ContributedGBASizeSF;

    @JsonProperty("PropertyResearchTypeID")
    private Integer researchTypeID;

    private String researchTypeName;

    private String mainPhotoURL;

    private Boolean hasNoBuildingFootprints;

    @JsonProperty("Address")
    private AddressDTO address;

    @JsonProperty("Location")
    private LocationDTO location;

    private String CityName;

    private String StateAbbr;

    private  String CountryCode;

    private String ParcelInfo;

    private String TrueOwners;

    private String RecordedOwners;

    private ClassType ClassType;

    private ConstructionStatus ConstructionStatus;
}

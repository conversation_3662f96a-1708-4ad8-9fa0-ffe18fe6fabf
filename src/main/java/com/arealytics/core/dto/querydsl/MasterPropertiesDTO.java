package com.arealytics.core.dto.querydsl;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class MasterPropertiesDTO {
  private Integer propertyID;
  private String propertyName;
  private String address;
  private Integer cityID;
  private String cityName;
  private Integer stateID;
  private String stateName;
  private String stateAbbr;
  private Integer countryID;
  private String countryName;
  private String zipCode;
  private String propertyUse;
  private String parcelInfo;
  private BigDecimal buildingSF;
  private BigDecimal buildingSizeSM;
  private String propertyNameDisplay;
  private Integer researchTypeID;
  private String researchTypeName;
  private String lastStrataUnit;
}

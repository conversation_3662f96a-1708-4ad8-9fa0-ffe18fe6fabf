package com.arealytics.core.dto.querydsl;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.arealytics.core.domain.empiricalProd.EntityModel;
import com.arealytics.core.dto.response.AddressDTO;
import com.arealytics.core.dto.response.BaseDTO;
import com.arealytics.core.dto.response.LocationDTO;
import com.arealytics.core.enumeration.*;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertyECRESearchQuerydslDTO {

    private Integer propertyID;

    private CondoType condoTypeID;

    private Integer useTypeID;

    private String useTypeName;

    private BigDecimal Latitude;

    private BigDecimal Longitude;

    private Integer masterPropertyId;

    private Integer listingID;
}

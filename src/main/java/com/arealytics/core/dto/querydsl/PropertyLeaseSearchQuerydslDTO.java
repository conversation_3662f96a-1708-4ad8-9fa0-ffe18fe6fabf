package com.arealytics.core.dto.querydsl;

import com.arealytics.core.dto.response.AddressDTO;
import com.arealytics.core.dto.response.LocationDTO;
import com.arealytics.core.dto.response.PropertySearchLeaseResponseDTO;
import com.arealytics.core.enumeration.CondoType;
import com.arealytics.core.enumeration.RecordType;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

import java.math.BigDecimal;
import java.time.Instant;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
@ToString
public class PropertyLeaseSearchQuerydslDTO {
    private Integer propertyID;
    private String propertyName;
    private CondoType condoTypeID;
    private Integer useTypeID;
    private String useTypeName;
    private Integer specificUseID;
    private String specificUseName;
    private BigDecimal buildingSize;
    private BigDecimal buildingSizeSF;
    private Integer listingID;
    private String leaseTypeName;
    private Instant modifiedDate;
    private RecordType recordTypeName;
    private String stateName;
    private BigDecimal totalAvailable;
    private AddressDTO address;
    private LocationDTO location;
    private PropertySearchLeaseResponseDTO LeasePropertiesDetails;
    private String cityName;
}

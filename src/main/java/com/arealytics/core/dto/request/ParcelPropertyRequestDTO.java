package com.arealytics.core.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ParcelPropertyRequestDTO {

    @NotNull(message = "ParcelNo is required") @JsonProperty("ParcelNo")
    private String parcelNo;

    @JsonProperty("ParcelSF")
    private Double parcelSF;

    @JsonProperty("Lot")
    private String lot;

    @JsonProperty("Block")
    private String block;

    @JsonProperty("SubDivision")
    private String subDivision;
}

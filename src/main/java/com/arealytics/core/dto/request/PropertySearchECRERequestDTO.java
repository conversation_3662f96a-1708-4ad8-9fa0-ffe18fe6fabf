package com.arealytics.core.dto.request;

import com.arealytics.core.enumeration.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertySearchECRERequestDTO {
        private List<String> citySearchText;
        private List<String> countySearchText;
        private List<String> zipcodeSearchText;
        private List<Integer> MarketList;
        private List<Integer> SubMarketList;
        private Integer subLeaseFilter;
        private Integer coWorkingFilter;
        @NotNull(message = "Page is required")
        private Integer page = 0;
        private Integer pageSize = 500;
        private List<CondoType> strataFilter;
        private Boolean fitout;
        private String searchCriteriaJSON;
        private String selectedPolygon;
        private Integer GRESBScoreMin;
        private Integer GRESBScoreMax;
        private Integer countryId;
        private String sortBy;
        private String sortDirection;
        private Boolean isMapSearch;
        private String polygonText;
        private List<String> polygonTextArray;
        private List<String> allLatlngArray;
        private String latlngArray;
        private BigDecimal centreLatitude;
        private BigDecimal centreLongitude;
        private BigDecimal circleRadius;
        private List<CircleRadiusDTO> circleRadiusArray;
        private String circleRadiusJSON;
        private BigDecimal salePriceMin;
        private BigDecimal salePriceMax;
        private List<ConstructionStatus> constructionStatuses;
        private List<AmenitiesType> amenitiesTypes;
        private String searchValue;
        private List<Integer> specificUseIds;
        private List<Integer> propertyTypes;
        private List<ClassType> buildingClasses;
        private List<Tenancy> tenancyIds;
        @NotNull(message = "ListingType is required")
        private String listingType;
        private List<Integer> propertyIds;
        private String propertyName;
        private BigDecimal buildingSizeMax;
        private BigDecimal buildingSizeMin;
        private BigDecimal totalAvailableMax;
        private BigDecimal totalAvailableMin;
        private BigDecimal lotSizeMin;
        private BigDecimal lotSizeMax;
        @NotNull(message = "StateID is required")
        private Integer stateID;
        private List<GreenStarRating> greenStar;
        private List<EnergyStarRating> NABERsEnergy;
        private List<WaterStarRating> NABERsWater;
        private List<String> zipCodes;
        private List<Integer> countyIds;
        private List<Integer> cityIds;
        private BigDecimal SWLat;
        private BigDecimal NELat;
        private BigDecimal NELng;
        private BigDecimal SWLng;
        private String ownership;
        private Boolean ownerOccupiedStatus;
        private JsonNode userActivityJSON;
        private BigDecimal buildingSizeSFMax;
        private BigDecimal buildingSizeSFMin;
        private BigDecimal lotSizeSFMin;
        private BigDecimal lotSizeSFMax;
        private String createdDateMax;
        private Instant createdDateMaxFormat;
        private String createdDateMin;
        private Instant createdDateMinFormat;
        private Boolean suiteLevel;
        private List<Integer> companyIds;
        private List<Integer> agentIds;
        private List<SaleType> saleTypeIds;
        private List<AgreementType> agreementTypeIds;
        private BigDecimal saleSizeMin;
        private BigDecimal saleSizeMax;
        private BigDecimal saleSizeSFMin;
        private BigDecimal saleSizeSFMax;
        private BigDecimal leaseRateMin;
        private BigDecimal leaseRateMax;
        private Boolean isContiguous;
        private List<Integer> leaseRateTypeIds;
        private Boolean isFullFloor;
}

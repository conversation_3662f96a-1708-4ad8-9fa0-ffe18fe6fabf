package com.arealytics.core.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
public class CityRequestDTO {

    @NotNull(message = "CityName is required")
    @Pattern(regexp = "^(?!\\s*$).+", message = "CityName must not be blank if provided")
    @JsonProperty("CityName")
    private String cityName;

    @NotNull(message = "StateId is required")
    @JsonProperty("StateId")
    private Integer stateId;
}

package com.arealytics.core.dto.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertySearchDetailsRequestDTO {
    private String listingType;
    private Integer userActivityLogID;
    private Integer startingIndex;
    private Integer offsetValue;
    private String sortBy;
    private String sortDirection;
}

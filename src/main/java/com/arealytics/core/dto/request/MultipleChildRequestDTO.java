package com.arealytics.core.dto.request;

import com.arealytics.core.dto.response.PropertyDetailsDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class MultipleChildRequestDTO {

    private PropertyDetailsDTO property;

    private RangeDTO range;

    private List<ChildDTO> children;

    @Data
    @JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
    public static class RangeDTO {
        private Integer min;
        private Integer max;
    }

    @Data
    @JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
    public static class ChildDTO {
        private Integer condoUnit;
    }
}

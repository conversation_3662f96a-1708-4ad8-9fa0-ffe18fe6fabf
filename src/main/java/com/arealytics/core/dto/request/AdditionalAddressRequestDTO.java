package com.arealytics.core.dto.request;

import com.arealytics.core.enumeration.Prefix;

import lombok.*;

import com.arealytics.core.enumeration.Quadrant;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Positive;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = false)
public class AdditionalAddressRequestDTO {
    private String  addressStreetName;
    private Boolean  addressType;
    private String  buildingNumber;
    private Integer  cityId;
    private Integer  countryId;
    private Integer  countyId;
    private String  eastWestSt;
    private Boolean  isActive;
    private String  northSouthSt;
    private Prefix streetPrefix1;
    private Prefix streetPrefix2;
    private Integer  parentId;
    private Quadrant  quadrantId;
    private Integer  stateId;
    private Integer  streetNumberMin;
    private Integer  streetNumberMax;
    private Integer  streetSuffix1;
    private Integer  streetSuffix2;
    private Integer  zipCode;
}



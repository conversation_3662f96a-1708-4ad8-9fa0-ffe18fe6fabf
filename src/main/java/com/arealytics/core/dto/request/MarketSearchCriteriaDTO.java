package com.arealytics.core.dto.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class MarketSearchCriteriaDTO {
    private Integer metroId;
    private Integer stateId;
    private Integer useTypeId;
}

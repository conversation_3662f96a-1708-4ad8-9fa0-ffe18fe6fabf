package com.arealytics.core.dto.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@RequiredArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class BuildingFootPrintDeleteDTO {
    @NotNull(message = "propertyId is required") private Integer propertyId;

    @NotBlank(message = "buildingFootPrintID/s is required") private String buildingFootPrintIDs;
}

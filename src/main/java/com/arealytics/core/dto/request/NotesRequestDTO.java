package com.arealytics.core.dto.request;

import com.arealytics.core.enumeration.NoteType;
import com.arealytics.core.enumeration.ParentTable;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = false)
public class NotesRequestDTO{
    private NoteType noteTypeId;
    private String noteTitle;
    private String noteDescription;
    private ParentTable parentTableId;
    private Integer parentId;
}

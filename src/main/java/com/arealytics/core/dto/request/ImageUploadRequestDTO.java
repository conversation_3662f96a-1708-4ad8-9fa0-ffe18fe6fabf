package com.arealytics.core.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ImageUploadRequestDTO {

    @NotNull(message = "base64 is required")
    @Pattern(regexp = "^(?!\\s*$).+", message = "base64 must not be blank if provided")
    private String base64;

    @NotNull(message = "fileName is required")
    @Pattern(regexp = "^(?!\\s*$).+", message = "filename must not be blank if provided")
    private String fileName;

    private Boolean isMarketBrief;
    private Boolean isClientLogo;
    private Boolean isSignature;
}

package com.arealytics.core.dto.request;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;

import com.arealytics.core.enumeration.ClassType;
import com.arealytics.core.enumeration.CondoType;
import com.arealytics.core.enumeration.ConstructionStatus;
import com.arealytics.core.enumeration.Tenancy;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
// @Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PropertyMapSearchRequestDTO {
    private Integer page = 0;
    private Integer pageSize = 500;
    private String zipCode;
    private Integer propertyId;
    @JsonProperty("NELat")
    private BigDecimal NELat;
    @JsonProperty("NELng")
    private BigDecimal NELng;
    @JsonProperty("SWLat")
    private BigDecimal SWLat;
    @JsonProperty("SWLng")
    private BigDecimal SWLng;
    private String PolygonText;
}

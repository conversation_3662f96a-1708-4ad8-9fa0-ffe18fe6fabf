package com.arealytics.core.dto.request;

import com.arealytics.core.enumeration.MediaRelationType;
import com.arealytics.core.enumeration.MediaSource;
import com.arealytics.core.enumeration.MediaSubType;
import com.arealytics.core.enumeration.MediaType;
import com.arealytics.core.interfaces.onCreate;
import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class MediaRequestDTO {

  @Size(max = 255, message = "Description cannot exceed 255 characters")
  @Pattern(regexp = "^(?!\\s*$).+", message = "Description must not be blank if provided")
  @JsonProperty("Description")
  private String description;

  @NotNull(message = "Ext is required", groups = onCreate.class)
  @Pattern(regexp = "^(?!\\s*$).+", message = "Ext must not be blank if provided")
  @Size(max = 45, message = "Ext cannot exceed 45 characters")
  @JsonProperty("Ext")
  private String ext;

  @JsonProperty("Height")
  private Integer height;

  @JsonProperty("Width")
  private Integer width;

  @JsonProperty("IsDefault")
  private boolean isDefault;

  @NotNull(message = "IsOwnMedia is required", groups = onCreate.class)
  @JsonProperty("IsOwnMedia")
  private boolean isOwnMedia;

  @NotNull(message = "MediaName is required", groups = onCreate.class)
  @Size(max = 255, message = "MediaName cannot exceed 255 characters")
  @Pattern(regexp = "^(?!\\s*$).+", message = "MediaName must not be blank if provided")
  @JsonProperty("MediaName")
  private String mediaName;

  @NotNull(message = "MediaRelationTypeID is required")
  @Enumerated(EnumType.STRING)
  @JsonProperty("MediaRelationTypeID")
  private MediaRelationType mediaRelationTypeId;

  @Enumerated(EnumType.STRING)
  @JsonProperty("MediaSourceID")
  private MediaSource mediaSourceId;

  @Enumerated(EnumType.STRING)
  @JsonProperty("MediaSubTypeID")
  private MediaSubType mediaSubTypeId;

  @NotNull(message = "MediaTypeID is required", groups = onCreate.class)
  @Enumerated(EnumType.STRING)
  @JsonProperty("MediaTypeID")
  private MediaType mediaTypeId;

  @NotBlank(message = "Path is required", groups = onCreate.class)
  @Pattern(regexp = "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\.[a-zA-Z0-9]+$", message = "Path must be a UUID followed by a file extension (e.g., 6e281815-70ca-4383-a547-80431cb3688e.jpg)")
  @JsonProperty("Path")
  private String path;

  @NotNull(message = "PropertyID is required")
  @JsonProperty("PropertyID")
  private Integer propertyId;

  @NotNull(message = "RelationID is required")
  @JsonProperty("RelationID")
  private Integer relationId;

  @NotNull(message = "Size is required", groups = onCreate.class)
  @JsonProperty("Size")
  private Integer size;

  @Pattern(regexp = "^(?!\\s*$).+", message = "SourceComments must not be blank if provided")
  @JsonProperty("SourceComments")
  private String sourceComments;

}

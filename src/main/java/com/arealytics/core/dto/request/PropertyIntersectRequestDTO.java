package com.arealytics.core.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertyIntersectRequestDTO {

    @JsonProperty("MetroID")
    private Integer metroID;

     @JsonProperty("PropertyID")
    private Integer propertyID;

    @JsonProperty("UseTypeID")
    private Integer useTypeID;

    private BigDecimal latitude;

    private BigDecimal longitude;
}

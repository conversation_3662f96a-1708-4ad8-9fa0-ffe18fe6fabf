package com.arealytics.core.dto.request;

import com.arealytics.core.enumeration.UserPreferenceScreen;
import com.arealytics.core.enumeration.UserPreferenceType;
import com.arealytics.core.interfaces.onCreate;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class UserPreferenceRequestDTO {
  @NotNull(message = "Type is required", groups = onCreate.class)
  private UserPreferenceType type;

  @NotNull(message = "Data is required", groups = onCreate.class)
  private String data;

  @NotNull(message = "Screen is required", groups = onCreate.class)
  private UserPreferenceScreen screen;
}

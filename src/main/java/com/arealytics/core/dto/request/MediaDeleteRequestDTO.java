
package com.arealytics.core.dto.request;

import com.arealytics.core.enumeration.MediaRelationType;
import com.arealytics.core.interfaces.onCreate;
import com.arealytics.core.interfaces.onUpdate;
import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MediaDeleteRequestDTO {

    @NotNull(message = "MediaRelationTypeID is required")
    @Enumerated(EnumType.STRING)
    @JsonProperty("MediaRelationTypeID")
    private MediaRelationType mediaRelationTypeId;

    @NotNull(message = "RelationID is required")
    @JsonProperty("RelationID")
    private Integer relationId;

    @NotNull(message = "MediaRelationshipID is required")
    @JsonProperty("MediaRelationshipID")
    private Integer mediaRelationshipId;

}

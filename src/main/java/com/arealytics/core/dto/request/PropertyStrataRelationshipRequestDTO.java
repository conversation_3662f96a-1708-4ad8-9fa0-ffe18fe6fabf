package com.arealytics.core.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class PropertyStrataRelationshipRequestDTO {

    @JsonProperty("ChildPropertyIds")
    @NotBlank(message = "ChildPropertyIds is required")
    @Pattern(regexp = "^\\d+(,\\d+)*$", message = "ChildPropertyIds must be a comma-separated list of integers (e.g., '123,456')")
    private String childPropertyIds;

    @JsonProperty("MasterPropertyId")
    @NotNull(message = "MasterProperty is required")
    @Positive(message = "MasterPropertyId must be a positive number")
    private Integer masterPropertyId;
}



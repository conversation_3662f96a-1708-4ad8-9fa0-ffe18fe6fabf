package com.arealytics.core.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@RequiredArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class BuildingFootPrintRequestDTO {
    private Long buildingFootPrintId;

    @NotEmpty(message = "Building foot-print is required")
    private String buildingFootPrint;

    @Min(value = 0, message = "Area should not be negative")
    private Double area;

    @Min(value = 0, message = "MinFloorNumber should not be negative")
    private Integer minFloorNumber;

    @Min(value = 0, message = "MaxFloorNumber should not be negative")
    private Integer maxFloorNumber;

    private Integer useTypeId;
    private String description;
    private Integer additionalUseTypeId;
    private Integer additionalSpecificUseTypeId;
    private Integer mainSpecificUseTypeId;
}

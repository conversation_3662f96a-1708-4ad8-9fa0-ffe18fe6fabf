package com.arealytics.core.dto.request;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = false)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class BuildingFootprintCollectionRequestDTO {

    @NotEmpty(message = "At least one building footprint is required") @JsonProperty("BuildingFootPrints")
    private @Valid List<BuildingFootPrintRequestDTO> buildingFootPrintRequestDTOS;

    @NotNull(message = "propertyId is required")
    private Integer propertyId;
    private Boolean isNewProperty;
}

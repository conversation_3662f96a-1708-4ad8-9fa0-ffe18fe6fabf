package com.arealytics.core.dto.response;

import com.arealytics.core.dto.querydsl.PropertyECRESearchQuerydslDTO;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class ECRESearchResponseDTO {
    private List<PropertyECRESearchQuerydslDTO> properties;
    private Long suitesCount;
}

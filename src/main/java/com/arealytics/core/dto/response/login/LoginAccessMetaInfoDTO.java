package com.arealytics.core.dto.response.login;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class LoginAccessMetaInfoDTO {

    private Integer entityID;

    private Integer accountLockID;

    private Boolean isActive;

    private LocalDateTime endDate;

    private Boolean hasAccess;

    private Boolean branchHasAccess;

    private Boolean branchIsMember;

    private Integer failedAttemptsCount;

    private Boolean isLimitedAccess;

    private Boolean hasMarketBriefAccess;

    private Boolean enforceTenantExportLimit;

    private Boolean enableSaleExport;

    private Boolean enableLeaseExport;

    private Boolean cauHasAccess;

    private Boolean cauBranchHasAccess;

    private Boolean forcefullyResetPassword;

    private Boolean enableV2Search;

    private Boolean enforceProExportLimit;

    private Integer companyID;

    private Integer ultimateCompanyID;
}

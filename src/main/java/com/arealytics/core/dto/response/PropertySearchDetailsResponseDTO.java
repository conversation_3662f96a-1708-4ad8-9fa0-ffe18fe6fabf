package com.arealytics.core.dto.response;

import com.arealytics.core.enumeration.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertySearchDetailsResponseDTO {
    private Integer propertyID;
    private Integer listingID;
    private Boolean isActive;
    private String propertyName;
    private String city;
    private String county;
    private String zipCode;
    private String propertyType;
    private String specificUses;
    private String address;
    private BigDecimal buildingSize;
    private String buildingSizeSM;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String mainPhotoUrl;
    private String mediaUrl;
    private Integer yearBuilt;
    private Integer floors;
    private String propertyComments;
    private String state;
    private String country;
    private String buildingComments;
    private String marketName;
    private String subMarketName;
    private ConstructionStatus constructionStatusID;
    private String totalAvailable;
    private BigDecimal totalAvailableForSort;
    private String totalAvailableSM;
    private String listingTypeName;
    private String askingRate;
    private String listingCompanyName;
    private String agentName;
    private BigDecimal askingSalePrice;
    private String recordTypeName;
    private String price;
    private BigDecimal salePricePerSF;
    private String salePricePerSM;
    private BigDecimal totalVacant;
    private String totalVacantSM;
    private String askingLeaseRatePerYrText;
    private String leaseTypeName;
    private CondoType condoTypeID;
    private String owners;
    private String countryCode;
    private String stateCode;
    private String condoUnit;
    private Integer masterPropertyID;
    private Boolean hasNoBuildingFootprints;
    private BigDecimal contributedGBA_SF;
    private String contributedGBA_SM;
    private ClassType classTypeName; // Lease-specific
    private BigDecimal minDiv; // Lease-specific
    private String minDivSM; // Lease-specific
    private Integer streetNumberMinN; // Lease-specific
    private Integer streetNumberMaxN; // Lease-specific

    @JsonIgnore
    private List<SuiteSearchDetailsResponseDTO> suites; // Suite details for lease
}

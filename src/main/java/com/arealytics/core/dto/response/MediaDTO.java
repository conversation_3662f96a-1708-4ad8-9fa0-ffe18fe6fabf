package com.arealytics.core.dto.response;

import java.math.BigDecimal;

import com.arealytics.core.enumeration.MediaRelationType;
import com.arealytics.core.enumeration.MediaSource;
import com.arealytics.core.enumeration.MediaSubType;
import com.arealytics.core.enumeration.MediaType;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class MediaDTO extends BaseDTO {
  private Integer mediaID;

  private String mediaName;

  private Double height;

  private Double width;

  private Double size;

  private String path;

  private String ext;

  private String description;

  private String modifiedByName;

  private Integer mediaRelationshipID;

  @Enumerated(EnumType.STRING)
  private MediaRelationType mediaRelationTypeID;

  private String mediaRelationTypeName;

  @Enumerated(EnumType.STRING)
  private MediaType mediaTypeID;

  private String mediaTypeName;

  @Enumerated(EnumType.STRING)
  private MediaSubType mediaSubTypeID;

  private String mediaSubTypeName;

  private Integer relationID;

  private Integer propertyID;

  private Boolean isDefault;

  private Boolean isOwnMedia;

  @Enumerated(EnumType.STRING)
  private MediaSource mediaSourceID;

  private String sourceComments;

  private Boolean hasEdit;

  private BigDecimal buildingSizeSF;

  private String condoUnit;
}

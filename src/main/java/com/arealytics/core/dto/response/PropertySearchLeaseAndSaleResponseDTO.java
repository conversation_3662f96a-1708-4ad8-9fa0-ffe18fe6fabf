package com.arealytics.core.dto.response;

import com.arealytics.core.enumeration.ClassType;
import com.arealytics.core.enumeration.ConstructionStatus;
import com.arealytics.core.enumeration.SupportRoleType;
import com.arealytics.core.enumeration.Tenancy;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

import java.math.BigDecimal;
import java.time.Instant;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertySearchLeaseAndSaleResponseDTO {
    private ClassType classTypeID;
    private Integer streetNumberMinN;
    private Integer streetNumberMaxN;
    private BigDecimal askingLeaseRatePerYearMin;
    private BigDecimal askingLeaseRatePerYearMax;
    private Tenancy tenancyTypeID;
    private Integer leaseTypeID;
    private BigDecimal lotSizeSF;
    private BigDecimal lotSizeSM;
    private Integer companyID;
    private Integer entityId;
    private SupportRoleType supportRoleTypeID;
    private Boolean isCondoSale;
    private ConstructionStatus constructionStatusID;
    private Integer listingStatusID;
    private Integer listingMangedByProviderStatus;
    private Boolean isInternalListing;
    private Instant createdDate;
}


package com.arealytics.core.dto.response;

import com.arealytics.core.enumeration.SaleType;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertySearchSaleListingResponseDTO {
    private Integer propertyID;
    private Integer listingID;
    private String propertyName;
    private String address;
    private String city;
    private String listingStatusName;
    private Integer listingStatusID;
    private Integer generalUseID;
    private String generalUse;
    private String strataUnit;
    private BigDecimal saleSize;
    private BigDecimal saleSizeSM;
    private BigDecimal askingSalePrice;
    private Boolean vacant;
    private Integer saleTypeID;
    private String saleTypeName;
    private String companyName;
    private String agentName;
    private String modifiedDate;
    private Boolean exclFromAvailability;
    private Integer listingMangedByProviderStatus;
    private Boolean isInternalListing;
    private Integer researcherEntityID;
    private Integer modDateCounter;
    
}
package com.arealytics.core.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class FullStrataDetailResponseDTO {
  private Integer propertyID;
  private String propertyName;
  private String Address;
  private String strataUnit;
  private String propertyUse;
  private BigDecimal lotSizeSF;
  private BigDecimal lotSizeSM;
  private BigDecimal buildingSizeSF;
  private BigDecimal buildingSizeSM;
  private Boolean isMaster;
  private String strataType;
  private String owner;
  private String ownerType;
  private String parcelNumbers;
  private Integer saleID;
  private LocalDateTime lastSaleDate;
  private BigDecimal lastSalePrice;
  private Integer floorNumber;
  private BigDecimal totalAvailableSM;
  private BigDecimal saleSizeSM;
  private Boolean hasNoBuildingFootprints;
  private BigDecimal contributedGBASF;
  private BigDecimal contributedGBASM;
}

package com.arealytics.core.dto.response.login;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class LoginDTO {
    @JsonProperty("Response")
    LoginResponseStatusDTO loginResponseStatusDTO;

    @JsonProperty("UserInfo")
    LoginUserInfoDTO loginUserInfoDTO;

    @JsonProperty("Token")
    String token;
}

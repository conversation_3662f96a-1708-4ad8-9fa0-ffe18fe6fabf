package com.arealytics.core.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertyIntersectResponseDTO {
    
    @JsonProperty("MarketID")
    private Integer marketID;

    private String marketName;

    @JsonProperty("SubMarketID")
    private Integer subMarketID;

    private String subMarketName;
    
    @JsonProperty("MetroID")
    private Integer metroID;
}

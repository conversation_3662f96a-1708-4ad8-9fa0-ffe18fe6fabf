package com.arealytics.core.dto.response;

import com.arealytics.core.enumeration.ParentTable;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class SavedSearchFieldsResponseDTO {
  private Integer changeLogFieldID;
  private ParentTable parentTableID;
  private String fieldName;
  private String displayText;
}

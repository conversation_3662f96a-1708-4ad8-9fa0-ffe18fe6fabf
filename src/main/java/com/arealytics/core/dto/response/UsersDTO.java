package com.arealytics.core.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class UsersDTO {

    @JsonProperty("EntityID")
    private final Integer entityId;

    @JsonProperty("PersonID")
    private final Integer personId;

    private final String firstName;

    private final String lastName;

    private final String company;

    private final String role;

    @JsonProperty("RoleID")
    private final Integer roleId;
}

package com.arealytics.core.dto.response;

import com.arealytics.core.enumeration.*;

import java.math.BigDecimal;
import java.time.Instant;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertySearchLeaseResponseDTO {
    private ListingType listingType;
    private Boolean isActive;
    private ClassType classTypeID;
    private String propertyComments;
    //    private String askingRate;
//    private String listingCompanyName;
//    private String agentName;
    //    private String price;
//    private Double totalVacant;
//    private String totalVacantSM;
//    private String askingLeaseRatePerYrText;
    //    private Double minDiv;
//    private String minDivSM;
//    private Integer modDateCounter;
    private Integer listingStatusId;
    private Integer yearBuilt;
    private Integer floors;
    private String buildingComments;
    private String mainPhotoUrl;
    private Instant createdDate;
    private Integer createdBy;
    private Integer modifiedBy;
    private String additionalAgents;
}


package com.arealytics.core.dto.response;
import com.arealytics.core.enumeration.BuildSpecStatus;
import com.arealytics.core.enumeration.SizeSource;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.AllArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class PropertyDetailsSizeResponseDTO extends PropertyDetailsDTO {

    @JsonProperty("LotSizeSM")
    private BigDecimal lotSizeSM;

    @JsonProperty("LotSizeSMFormatted")
    private String lotSizeSMFormatted;

    @JsonProperty("LotSizeACSM")
    private BigDecimal lotSizeACSM;

    @JsonProperty("BuildingSizeSM")
    private BigDecimal buildingSizeSM;

    @JsonProperty("BuildingSizeSMFormatted")
    private String buildingSizeSMFormatted;

    @JsonProperty("ContributedGBA_SM")
    private BigDecimal contributedGBASM;

    @JsonProperty("GLA_SM")
    private BigDecimal glaSM;

    @JsonProperty("GLAR_SM")
    private BigDecimal glarSM;

    @JsonProperty("Mezzanine_Size_SM")
    private BigDecimal mezzanineSizeSM;

    @JsonProperty("Awnings_Size_SM")
    private BigDecimal awningsSizeSM;

    @JsonProperty("RetailSM")
    private  BigDecimal RetailSM;

    @JsonProperty("OfficeSM")
    private BigDecimal officeSM;

    @JsonProperty("NLA_SM")
    private BigDecimal nlaSM;

    @JsonProperty("ClearHeightMinM")
    private BigDecimal clearHeightMinM;

    @JsonProperty("ClearHeightMaxM")
    private BigDecimal clearHeightMaxM;

    @JsonProperty("ColumnSpacingLenM")
    private BigDecimal columnSpacingLenM;

    @JsonProperty("ColumnSpacingWidthM")
    private BigDecimal columnSpacingWidthM;

    @JsonProperty("TypicalFloorSizeSM")
    private BigDecimal typicalFloorSizeSM;

    @JsonProperty("ParcelNumber")
    private String parcelNumber;

    @JsonProperty("WidthM")
    private  BigDecimal widthM;

    @JsonProperty("DepthM")
    private BigDecimal depthM;

    @JsonProperty("SmallestFloorSM")
    private BigDecimal smallestFloorSM;

    @JsonProperty("LargestFloorSM")
    private BigDecimal largestFloorSM;

    @JsonProperty("TotalAnchorSF")
    private BigDecimal totalAnchorSF;

    @JsonProperty("RetailFrontageM")
    private BigDecimal retailFrontageM;

    @JsonProperty("ClassTypeName")
    private String classTypeName;

    @JsonProperty("TenancyName")
    private String tenancyName;

    @JsonProperty("CondoTypeName")
    private String condoTypeName;

    @JsonProperty("BldgSizeSourceName")
    private String bldgSizeSourceName;

    @JsonProperty("EnergyStarRatingName")
    private String energyStarRatingName;

    @JsonProperty("WaterStarRatingName")
    private String waterStarRatingName;

    @JsonProperty("GreenStarRatingName")
    private String greenStarRatingName;

    @JsonProperty("ConstructionTypeName")
    private String constructionTypeName;

    @JsonProperty("ConstructionStatusName")
    private String constructionStatusName;

    @JsonProperty("HVACTypeName")
    private String hvacTypeName;

    @JsonProperty("SprinklerTypeName")
    private String sprinklerTypeName;

    @JsonProperty("BuildSpecStatusName")
    private String buildSpecStatusName;

    @JsonProperty("RoofTypeName")
    private String roofTypeName;

    @JsonProperty("GovernmentInterestName")
    private String governmentInterestName;

    @JsonProperty("PowerTypeName")
    private String powerTypeName;

    @JsonProperty("HardstandAreaSourceName")
    private String hardstandAreaSourceName;

    @JsonProperty("NRASizeSourceName")
    private String NRASizeSourceName;

    @JsonProperty("ContributedGBASizeSourceName")
    private String contributedGBASizeSourceName;

    @JsonProperty("GLASizeSourceName")
    private String GLASizeSourceName;

    @JsonProperty("GLARSizeSourceName")
    private String GLARSizeSourceName;

    @JsonProperty("LotSizeSourceName")
    private String lotSizeSourceName;
}

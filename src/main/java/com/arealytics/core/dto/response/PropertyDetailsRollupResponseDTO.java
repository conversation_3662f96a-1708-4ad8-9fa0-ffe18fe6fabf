package com.arealytics.core.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertyDetailsRollupResponseDTO {
    private PropertyDetailsSizeResponseDTO propertyDetailsSizeResponseDTO;
    private PropertyMasterRollupObject rollupObject;
}

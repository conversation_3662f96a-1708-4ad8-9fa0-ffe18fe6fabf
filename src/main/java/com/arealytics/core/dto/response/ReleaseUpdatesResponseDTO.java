package com.arealytics.core.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ReleaseUpdatesResponseDTO {
    private Integer applicationID;
    private Integer releaseID;
    private String releaseNoteLink;
    private List<Update> updates;
    private String version;

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Update {
        private String description;
        private String resource;
        private String title;
        private String type;
    }
}

package com.arealytics.core.dto.response;

import java.time.Instant;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public abstract class BaseDTO {
  private Integer createdBy;
  private Instant createdDate;
  private Integer modifiedBy;
  private Instant modifiedDate;
}

package com.arealytics.core.dto.response;
import java.math.BigDecimal;
import java.time.Instant;

import com.arealytics.core.enumeration.*;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
@ToString
public class PropertySearchResponseBaseDTO
{

    //String getAddress();
    private String agentName;
    private String askingLeaseRatePerYrText;
    private BigDecimal askingRate;
    private BigDecimal askingSalePrice;
    private BigDecimal buildingSizeSF;
    private BigDecimal buildingSizeSM;
    private String cityName;
    private CondoType condoTypeID;
    private String leaseTypeName;
    private String listingCompanyName;
    private Integer listingID;
    private Integer modDateCounter;
    private Instant modifiedDate;
    private BigDecimal price;
    private Integer propertyID;
    private String propertyName;
    private Integer useTypeID;
    private String useTypeName;
    private RecordType recordTypeName;
    private BigDecimal salePricePerSF;
    private BigDecimal salePricePerSM;
    private Integer specificUseID;
    private String specificUseName;
    private String stateName;
    private BigDecimal totalAvailable;
    private BigDecimal totalAvailableSM;
    private AddressDTO address;
    private LocationDTO location;

    private PropertySearchAllResponseDTO allPropertiesDetails;
    private PropertySearchLeaseResponseDTO leasePropertiesDetails;
    private PropertySearchSaleResponseDTO salePropertiesDetails;
    private PropertySearchLeaseAndSaleResponseDTO leaseAndSalePropertiesDetails;
}

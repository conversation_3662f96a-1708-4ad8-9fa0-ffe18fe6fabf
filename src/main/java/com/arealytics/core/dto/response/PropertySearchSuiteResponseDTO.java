package com.arealytics.core.dto.response;


import com.arealytics.core.domain.empiricalProd.SpaceType;
import com.arealytics.core.enumeration.SpaceUseType;
import com.arealytics.core.enumeration.SuiteStatus;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertySearchSuiteResponseDTO {
    private Integer propertyID;
    private Integer listingID;
    private Integer suiteID;
    private String propertyName;
    private String address;
    private String city;
    private Integer generalUseID;
    private String generalUse;
    private String spaceUseTypeName;
    private Integer spaceUseTypeID;
    private Integer suiteStatusID;
    private String suiteStatusName;
    private Integer spaceTypeID;
    private String spaceTypeName;
    private String suiteNumber;
    private BigDecimal availableSF;
    private BigDecimal availableSM;
    private String askingRateText;
    private BigDecimal minDivSF;
    private BigDecimal minDiv;
    private Boolean exclAvailable;
    private Boolean isContiguous;
    @JsonProperty("TIAllowance")
    private BigDecimal tiAllowance;
    private String companyName;
    private String agentName;
    private String modifiedDate;
    private Boolean isVacant;
    private BigDecimal askingRateHigh;
    private BigDecimal askingRateLow;
    private Integer listingMangedByProviderStatus;
    private Boolean isInternalListing;
    private Integer modDateCounter;
    private Integer floorNumber;
}

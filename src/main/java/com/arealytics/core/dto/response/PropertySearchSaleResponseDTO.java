package com.arealytics.core.dto.response;

import com.arealytics.core.enumeration.ListingType;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.Instant;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Sale listing type property search response")
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertySearchSaleResponseDTO{
    private ListingType ListingTypeID;
    private Boolean IsActive;
    private String MainPhotoUrl;
    private Integer YearBuilt;
    private Integer floors;
    private String propertyComments;
    private String BuildingComments;
    private BigDecimal TotalVacant;
    private BigDecimal TotalVacantSM;
    private Integer ListingStatusId;
    private Boolean IsCondoSale;
    private Instant createdDate;
    private String additionalBrokers;
}

package com.arealytics.core.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = false)
public class CountryDTO {
    @JsonProperty("CountryId")
    private Integer countryId;

    @JsonProperty("CountryName")
    private String countryName;

    @JsonProperty("Alpha-2Code")
    private String alpha2Code;

    @JsonProperty("Alpha-3Code")
    private String alpha3Code;

    @JsonProperty("UNCountryCode")
    private String unCountryCode;

    @JsonProperty("CallingCountryCode")
    private String callingCountryCode;

    @JsonProperty("IsActive")
    private Boolean isActive;

    @JsonProperty("UnitId")
    private Integer unitId;

    @JsonProperty("DateFormat")
    private String dateFormat;

    @JsonProperty("DateFormatSave")
    private String dateFormatSave;

    @JsonProperty("DateFormatRead")
    private String dateFormatRead;

    @JsonProperty("UnitDisplayTextSize")
    private String unitDisplayTextSize;

    @JsonProperty("UnitDisplayTextLength")
    private String unitDisplayTextLength;

    @JsonProperty("MobileNumberPrefix")
    private String mobileNumberPrefix;

    @JsonProperty("MobileNumberMask")
    private String mobileNumberMask;

    @JsonProperty("MobileNumberRegEx")
    private String mobileNumberRegEx;

    @JsonProperty("MobileNumberPattern")
    private String mobileNumberPattern;

    @JsonProperty("PhoneNumberMask")
    private String phoneNumberMask;

    @JsonProperty("PhoneNumberRegEx")
    private String phoneNumberRegEx;

    @JsonProperty("PhoneNumberPattern")
    private String phoneNumberPattern;
}

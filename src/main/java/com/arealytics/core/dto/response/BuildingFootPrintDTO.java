package com.arealytics.core.dto.response;

import java.time.Instant;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = false)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class BuildingFootPrintDTO {
    private Long buildingFootPrintId;
    private String buildingFootPrint;

    @JsonProperty("CRE_PropertyId")
    private Integer crePropertyId;
    private Double sizeInSF;
    private Double sizeInSM;

    @Min(value = 0, message = "MinFloorNumber should not be negative")
    private Integer propertyMinFloor;

    @Min(value = 0, message = "MaxFloorNumber should not be negative")
    private Integer propertyMaxFloor;
    private Integer floors;
    private Boolean isDefault;
    private Integer useTypeId;
    private Integer additionalUseTypeId;
    private Integer mainSpecificUseTypeId;
    private Integer additionalSpecificUseTypeId;
    private String useTypeName;
    private String additionalUseTypeName;
    private String mainSpecificUseTypeName;
    private String additionalSpecificUseTypeName;
    private String description;
    private String modifiedByName;
    private Instant modifiedDate;
}


package com.arealytics.core.dto.response;

import com.arealytics.core.enumeration.CondoType;
import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PropertyStrataDetailsDTO {
  @JsonProperty("PropertyID")
  private Integer propertyId;

  @JsonProperty("PropertyName")
  private String propertyName;

  @JsonProperty("Address")
  private String address;

  @JsonProperty("LotSizeSF")
  private Double lotSizeSF;

  @JsonProperty("LotSizeSM")
  private Double lotSizeSM;

  @JsonProperty("BuildingSF")
  private Double buildingSF;

  @JsonProperty("BuildingSizeSM")
  private Double buildingSizeSM;

  @JsonProperty("IsMaster")
  private boolean isMaster;

  @JsonProperty("StrataType")
  private String strataType;

  @JsonProperty("ParcelNumbers")
  private String parcelNumbers;

  @JsonProperty("CondoUnit")
  private String condoUnit;
}

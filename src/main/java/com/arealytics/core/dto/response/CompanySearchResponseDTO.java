package com.arealytics.core.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class CompanySearchResponseDTO {
  private Integer companyID;
  private String companyName;
  private String branchName;
  private String immediateParentCompanyName;
  private String ultimateParentCompanyName;
}

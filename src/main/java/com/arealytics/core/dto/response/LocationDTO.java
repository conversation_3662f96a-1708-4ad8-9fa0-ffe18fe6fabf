package com.arealytics.core.dto.response;

import com.arealytics.core.enumeration.RoofTopSource;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;
import org.locationtech.jts.geom.Point;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class LocationDTO {
    private BigDecimal latitude;

    private BigDecimal longitude;

    @JsonProperty("ZCoordinate")
    private BigDecimal zCoordinate;

    private RoofTopSource rooftopSourceID;

    @JsonProperty("GISShapeID")
    private Integer gisShapeID;
}
package com.arealytics.core.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PolygonShapesResponseDTO extends BaseDTO {
    private Integer companySearchShapeID;
    private String name;
    private String polygon;
    private String fileName;
    private String createdByName;
    private String modifiedByName;
}

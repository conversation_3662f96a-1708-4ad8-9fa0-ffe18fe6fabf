package com.arealytics.core.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = false)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class BuildingFootPrintsDTO {
    private Integer propertyId;
    private Integer propertyMinFloor;
    private Integer propertyMaxFloor;
    private Integer floors;
    private Double sizeInSF;
    private Double sizeInSM;
    private Integer useTypeId;
    private String useTypeName;
    private Integer additionalUseTypeId;
    private String additionalUseTypeName;
    private Integer mainSpecificUseTypeId;
    private String mainSpecificUseTypeName;
    private Integer additionalSpecificUseTypeId;
    private String additionalSpecificUseTypeName;
    private String condoUnit;
    private Integer propertyUseTypeID;
}

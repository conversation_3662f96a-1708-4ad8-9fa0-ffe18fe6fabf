package com.arealytics.core.dto.response;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class ChildFreeHoldsAdditionalUsesDTO {

    private String section;

    private Integer useTypeID;

    private String useTypeName;

    private Integer specificUsesID;

    private String specificUsesName;

    private Integer floors;

    private Double floorSizeSF;

    private Double floorSizeSM;

    private Object minFloor;

    private Object maxFloor;
}

package com.arealytics.core.dto.response;

import com.arealytics.core.enumeration.CondoType;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertyElasticSearchResponseDTO {
  private String address;
  private String addressText;
  private Integer cityID;
  private String cityName;
  private CondoType condoTypeID;
  private Integer propertyID;
  private String propertyName;
  private String propertyNameDisplay;
  private String stateAbbr;
  private Integer stateID;
  private String stateName;
}

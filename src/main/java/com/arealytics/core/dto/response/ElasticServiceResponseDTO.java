package com.arealytics.core.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;

@Data
public class ElasticServiceResponseDTO {
    private String _index;
    private String _id;
    private String _score;
    private Source _source;

    @Data
    @JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
    public class Source {
        private Integer propertyID;
        private String address;
        private Integer streetNumberMin;
        private Integer streetNumberMax;
        private String addressStreetName;
        private String zipCode;
        private String cityName;
        private String fullAddressText;
        private String propertyName;
        private Integer stateID;
        private String unitNumber;
    }
}

package com.arealytics.core.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ParcelPropertyDTO {
    @JsonProperty("Block")
    private String block;

    @JsonProperty("Lot")
    private String lot;

    @JsonProperty("ParcelID")
    private Integer parcelID;

    @JsonProperty("ParcelNo")
    private String parcelNo;

    @JsonProperty("ParcelSize")
    private Double parcelSize;

    @JsonProperty("ParcelSizeSM")
    private Double parcelSizeSM;

    @JsonProperty("PropertyID")
    private Integer propertyID;

    @JsonProperty("Sequence")
    private Integer sequence;

    @JsonProperty("SubDivision")
    private String subDivision;
}

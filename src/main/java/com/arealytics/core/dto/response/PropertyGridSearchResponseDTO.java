package com.arealytics.core.dto.response;

import java.util.List;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;


@Data
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertyGridSearchResponseDTO {

     private List<PropertySearchDetailsResponseDTO> properties;
     private List<SuiteSearchDetailsResponseDTO> suites;
}

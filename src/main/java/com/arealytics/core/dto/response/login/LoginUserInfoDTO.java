package com.arealytics.core.dto.response.login;

import com.arealytics.core.enumeration.Role;
import com.arealytics.core.enumeration.Unit;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class LoginUserInfoDTO {
    private String username;

    private Integer loginID;

    private String personName;

    private String firstName;

    private String lastName;

    private String email;

    private Integer companyID;

    private String companyName;

    private Role roleID;

    private Integer tenantID;

    private Integer personID;

    private Integer entityID;

    private Integer countryID;

    private Integer metroID;

    private BigDecimal metroCentroidLat;

    private BigDecimal metroCentroidLong;

    private String roleName;

    private Unit unitId;

    private String mainPhotoUrl;

    private Integer failedAttemptsCount;

    private String dateFormat;

    private Boolean isMember;

    private String metroSkylineImageUrl;

    private Boolean canImpersonateUser;

    private Boolean canUpdateRole;

    private Boolean pilotMode;

    private String officePhone;

    private String driveToolVersion;

    private String companyFloorNumber;

    private String companyAddress;

    private String companyMainPhotoUrl;

    private String companyCityName;

    private String companyZipCode;

    private String phoneNumberRegEx;

    private String phoneNumberPattern;

    private String phoneNumberMask;

    private String unitDisplayTextSize;

    private String unitDisplayTextLength;

    private Boolean canEditInPublic;

    private String mobileNumberPrefix;

    private String mobileNumberMask;

    private Boolean showSavedSearch;

    private Boolean showRegisteredLease;

    private Integer companyTierID;

    private Integer ultimateCompanyID;

    private String ultimateCompanyName;

    private Integer stateID;

    private Boolean isLimitedAccess;

    private Boolean hasMarketBriefAccess;

    private Boolean enforceTenantExportLimit;

    private Boolean enableSaleExport;

    private Boolean enableLeaseExport;

    private Boolean forcefullyResetPassword;

    private Boolean enableV2Search;

    private Boolean enforceProExportLimit;
}

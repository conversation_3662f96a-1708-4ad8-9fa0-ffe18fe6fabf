package com.arealytics.core.dto.response;

import java.time.Instant;
import java.util.List;

import com.arealytics.core.enumeration.NoteType;
import com.arealytics.core.enumeration.ParentTable;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class NotesResponseDTO {
    private Integer noteId;
    private NoteType noteTypeId;
    private String noteTitle;
    private String noteDescription;
    private ParentTable parentTableId;
    private Integer parentId;
    private String createdByName;
    private String modifiedByName;
    private Instant createdDate;
    private List<LinkedMediaDTO> linkedMedias;

    @Data
    @JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
    public static class LinkedMediaDTO{
        private Integer mediaId;
        private String path;
    }
}





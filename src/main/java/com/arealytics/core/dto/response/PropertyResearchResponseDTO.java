package com.arealytics.core.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertyResearchResponseDTO extends BaseDTO {
    private Integer propertyResearchStatusID;
    private Integer propertyID;
    private Boolean isActive;
    private Integer propertyResearchTypeID;
    private String propertyResearchTypeName;
    private String modifiedPersonName;
}

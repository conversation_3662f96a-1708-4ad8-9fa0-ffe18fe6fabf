package com.arealytics.core.dto.response;

import com.arealytics.core.enumeration.SpaceUseType;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.arealytics.core.enumeration.PossessionType;

@Data
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class SuiteSearchDetailsResponseDTO {
    private Integer propertyID;
    private Integer listingID;
    private Integer suiteID;
    private String propertyName;
    private String address;
    private BigDecimal buildingSize;
    private String buildingSizeSM;
    private String listingCompanyName;
    private String agentName;
    private String floorNumber;
    private String suiteNumber;
    private BigDecimal availableSF;
    private String availableSM;
    private BigDecimal maxSize;
    private BigDecimal maxSizeSM;
    private String askingRateText;
    private Boolean isVacant;
    private PossessionType possessionTypeID;
    private LocalDateTime dateAvailable;
    private SpaceUseType spaceUseTypeName;
    private LocalDateTime dateOnMarket;
    private Boolean exclAvailable;
    private Boolean isContiguous;
    private BigDecimal tiAllowance;
    private Boolean fitout;
    private BigDecimal minSF;
    private String minSM;
}

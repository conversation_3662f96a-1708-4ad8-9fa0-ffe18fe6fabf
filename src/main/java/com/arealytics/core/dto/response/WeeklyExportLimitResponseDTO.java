package com.arealytics.core.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class WeeklyExportLimitResponseDTO {
  private List<ExportLimitData> exportLimitData;
  private Boolean agreedExportTerms;

  @Getter
  @Setter
  @AllArgsConstructor
  @NoArgsConstructor
  @JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
  public static class ExportLimitData {
    private Integer parentTableID;
    private String parentTableName;
    private Integer exportLimit;
    private Integer exportCount;
  }
}

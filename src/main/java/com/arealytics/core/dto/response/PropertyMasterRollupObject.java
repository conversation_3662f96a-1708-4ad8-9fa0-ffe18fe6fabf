package com.arealytics.core.dto.response;

import com.arealytics.core.enumeration.SizeSource;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertyMasterRollupObject {

    @JsonProperty("BuildingSF")
    private BigDecimal buildingSizeSF;

    @JsonProperty("BuildingSizeSM")
    private BigDecimal buildingSizeSM;

    @JsonProperty("GradeLevelDriveIn")
    private Integer gradeLevelIn;

    private Integer dockHigh;

    private Integer truckwell;

    @JsonProperty("Anchors")
    private Integer noOfAnchor;

    @JsonProperty("NoOfAnchor")
    private Integer noOfAnchors;

    @JsonProperty("TotalAnchorSF")
    private BigDecimal totalAnchorSF;

    private Integer parkingSpaces;

    private Integer passengerElevators;

    private Integer parkingElevators;

    private BigDecimal retailFrontage;

    @JsonProperty("RetailFrontageM")
    private BigDecimal retailFrontageM;

    private Integer freighElevators;

    private BigDecimal parkingRatio;

    @JsonProperty("ReservedParkingSpaces")
    private Integer reservedParkingSpaces;

    @JsonProperty("UnreservedParkingSpaces")
    private Integer unreservedParkingSpaces;

    private Integer awningsCount;

    private Integer trafficCount;

    private Boolean railServed;

    private Boolean hasPortAccess;

    private Boolean isOwnerOccupied;

    private Boolean hasResCoveredParking;

    private Boolean isVented;

    private Boolean isADAAccessible;

    private Boolean hasYardFenced;

    private Boolean hasYardUnfenced;

    private Boolean yardPaved;

    private Boolean hasYard;

    @JsonProperty("HasUnreservedParkingSpaces")
    private Boolean hasUnreservedParkingSpaces;

    private Boolean hasSolar;

    @JsonProperty("HVAC")
    private Boolean hvac;

    private Boolean includeInAnalytics;

    private Boolean mezzanine;

    private Boolean awnings;

    private Boolean lifts;

    private String specificUseName;

    private String tenancyTypeID;

    private String classTypeID;

    private String constructionTypeID;

    private String constructionStatusID;

    private String volts;

    private String amps;

    private String sprinklerTypeID;

    private String hvacTypeID;

    private String governmentInterestID;

    @JsonProperty("AmenitiesType")
    private String amenitiesType;

    private String buildingWebsite;

    private String zoningCode;

    @JsonProperty("FeatureIDs")
    private String featureIDs;

    private String powerType;

    private String sizeSourceID;

    private String lotSizeSourceID;

    private String contributedGBASizeSourceID;

    @JsonProperty("GLASizeSourceID")
    private String glasSizeSourceID;

    @JsonProperty("GLARSizeSourceID")
    private String glarSizeSourceID;

    @JsonProperty("OfficeHVAC")
    private String officeHvac;

    private String yearBuilt;

    private String yearRenovated;

    private String waterStarRatingID;

    private String energyStarRatingID;

    private String greenStarRatingID;

    private String smallestFloor;

    private String largestFloor;

    @JsonProperty("SmallestFloorSM")
    private String smallestFloorSM;

    @JsonProperty("LargestFloorSM")
    private String largestFloorSM;

    @JsonProperty("NoOfOfficeFloor")
    private String noOfOfficeFloors;

    @JsonProperty("OfficeSF")
    private String officeSize;

    @JsonProperty("OfficeSM")
    private String officeSM;

    private String typicalFloorSize;

    @JsonProperty("TypicalFloorSizeSM")
    private String typicalFloorSizeSM;

    @JsonProperty("TIAllowance")
    private String tiAllowance;

    private String clearHeightMin;

    private String clearHeightMax;

    @JsonProperty("ClearHeightMinM")
    private String clearHeightMinM;

    @JsonProperty("ClearHeightMaxM")
    private String clearHeightMaxM;

    @JsonProperty("ReservedParkingSpacesRatePerMonth")
    private String reservedParkingSpacesRatePerMonth;

    @JsonProperty("UnreservedParkingSpacesRatePerMonth")
    private String unreservedParkingSpacesRatePerMonth;

    @JsonProperty("ContributedGBA_SF")
    private String contributedGBASizeSF;

    @JsonProperty("ContributedGBA_SM")
    private String contributedGBASM;

    @JsonProperty("GRESBScoreMin")
    private String gresbScoreMin;

    @JsonProperty("GRESBScoreMax")
    private String gresbScoreMax;

    private String vacancy;

    private String bookValue;

    private String bookValueDate;

    @JsonProperty("Mezzanine_Size_SF")
    private String mezzanineSizeSF;

    @JsonProperty("Mezzanine_Size_SM")
    private String mezzanineSizeSM;

    @JsonProperty("GLA_SF")
    private String glaSizeSF;

    @JsonProperty("GLA_SM")
    private String glaSM;

    @JsonProperty("GLAR_SF")
    private String glarSizeSF;

    @JsonProperty("GLAR_SM")
    private String glarSM;

    @JsonProperty("Awnings_Size_SF")
    private String awningsSizeSF;

    @JsonProperty("Awnings_Size_SM")
    private String awningsSizeSM;

    private String hardstandArea;

    private String constructionStartDate;

    @JsonProperty("EstCompletionDate")
    private String estCompletion;

    private String actualCompletion;

    private String titleReferenceDate;

    private String propertyUse;

    private Integer useTypeID;

    @JsonProperty("ClassTypeName")
    private String classTypeName;

    @JsonProperty("TenancyName")
    private String tenancyName;

    @JsonProperty("CondoTypeName")
    private String condoTypeName;

    @JsonProperty("BldgSizeSourceName")
    private String bldgSizeSourceName;

    @JsonProperty("EnergyStarRatingName")
    private String energyStarRatingName;

    @JsonProperty("WaterStarRatingName")
    private String waterStarRatingName;

    @JsonProperty("GreenStarRatingName")
    private String greenStarRatingName;

    @JsonProperty("ConstructionTypeName")
    private String constructionTypeName;

    @JsonProperty("ConstructionStatusName")
    private String constructionStatusName;

    @JsonProperty("HVACTypeName")
    private String hvacTypeName;

    @JsonProperty("SprinklerTypeName")
    private String sprinklerTypeName;

    private String mainPhotoUrl;

    private List<ChildFreeHoldsAdditionalUsesDTO> childFreeHoldsAdditionalUses;

    private String buildSpecStatusID;

    @JsonProperty("BuildSpecStatusName")
    private String buildSpecStatusName;

    private String floors;

    private String roofTypeID;

    @JsonProperty("RoofTypeName")
    private String roofTypeName;

    private String currentTitle;

    private Boolean hasReservedParkingSpaces;

    private Boolean hasSprinkler;

    @JsonProperty("IsCraneServed")
    private Boolean craneServed;

    private Integer liftsCount;

    @JsonProperty("GovernmentInterestName")
    private String governmentInterestName;

    @JsonProperty("PowerTypeName")
    private String powerTypeName;

    private String phase;

    @JsonProperty("TypicalFloorSizeSourceID")
    private String typicalFloorSizeSourceId;

    private String hardstandAreaSourceID;

    @JsonProperty("HardstandAreaSourceName")
    private String hardstandAreaSourceName;

    private Integer noOfUnits;

    @JsonProperty("NRASizeSourceID")
    private String NRASizeSourceID;

    @JsonProperty("NRASizeSourceName")
    private String NRASizeSourceName;

    @JsonProperty("ContributedGBASizeSourceName")
    private String contributedGBASizeSourceName;

    @JsonProperty("GLASizeSourceName")
    private String GLASizeSourceName;

    @JsonProperty("GLARSizeSourceName")
    private String GLARSizeSourceName;
    
    @JsonProperty("LotSizeSourceName")
    private String lotSizeSourceName;
}

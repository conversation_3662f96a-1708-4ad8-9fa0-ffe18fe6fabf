package com.arealytics.core.dto.response;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;

import com.arealytics.core.dto.querydsl.PropertySearchQuerydslDTO;
import com.arealytics.core.enumeration.*;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertyDTO extends PropertySearchQuerydslDTO {

    @JsonProperty("LotSizeSM")
    private BigDecimal lotSizeSM;

    @JsonProperty("LotSizeSMFormatted")
    private String lotSizeSMFormatted;

    @JsonProperty("LotSizeACSM")
    private BigDecimal lotSizeACSM;

    @JsonProperty("BuildingSizeSM")
    private BigDecimal buildingSizeSM;

    @JsonProperty("BuildingSizeSMFormatted")
    private String buildingSizeSMFormatted;

    @JsonProperty("ContributedGBA_SM")
    private BigDecimal contributedGBASizeSM;

    @JsonProperty("ContributedGBA_SMFormatted")
    private String contributedGBASizeSMFormatted;
}

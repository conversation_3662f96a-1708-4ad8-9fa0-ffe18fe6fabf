-- Drop Non-essential columns from the BuildingFootprints table.
ALTER TABLE Empirical_GIS.BuildingFootPrints
DROP COLUMN BuildingfootPrintStageID,
DROP COLUMN BuildingFootPrints_Stage_ID,
DROP COLUMN latitude,
DROP COLUMN longitude,
DROP COLUMN Gross_Building_Area,
DROP COLUMN Number_Floors_Estimate,
DROP COLUMN Average_Floor_Height,
DROP COLUMN Floor_Area,
DROP COLUMN Strata_Type,
DROP COLUMN LGA,
DROP COLUMN Arealytics,
DROP COLUMN General_Use,
DROP COLUMN Level2_Zoning,
DROP COLUMN Level1_Zoning,
DROP COLUMN height,
CHAN<PERSON> COLUMN CRE_PropertyID CRE_PropertyID INT NULL DEFAULT NULL FIRST,
CH<PERSON>GE COLUMN isActive isActive TINYINT(1) NULL DEFAULT '1' AFTER Description,
CHANGE COLUMN CreatedBy CreatedBy INT NULL DEFAULT NULL AFTER isActive,
CHANGE COLUMN ModifiedBy ModifiedBy VARCHAR(255) NULL DEFAULT NULL AFTER CreatedBy,
<PERSON>AN<PERSON> COLUMN CreatedDate CreatedDate DATETIME NULL DEFAULT CURRENT_TIMESTAMP AFTER ModifiedBy,
CHANGE COLUMN ModifiedDate ModifiedDate DATETIME NULL DEFAULT CURRENT_TIMESTAMP AFTER CreatedDate;

-- Merge Empirical_Prod.PropertySummary Columns to  Empirical_Prod.Property

ALTER TABLE Empirical_Prod.Property
 ADD COLUMN `ResearchTypeID` int,
 ADD COLUMN `ResearchTypeName` varchar(25),
 ADD COLUMN `TrueOwners` text,
 ADD COLUMN `RecordedOwners` text,
 ADD COLUMN `UseTypeID` int,
 ADD COLUMN `UseTypeName` varchar(25),
 ADD COLUMN `SpecificUseID` int,
 ADD COLUMN `SpecificUseName` varchar(50),
 ADD COLUMN `AddressID` int,
 ADD COLUMN `LocationID` int,
 ADD COLUMN `GroupID` int,
 ADD COLUMN `BuildingSizeSF` decimal(14,3),
 ADD COLUMN `LotSizeSF` decimal(14,3),
 ADD COLUMN `MainPhotoUrl` varchar(255),
 ADD COLUMN `ListingCompany` text,
 ADD COLUMN `LCount` int,
 ADD COLUMN `LLCount` int,
 ADD COLUMN `DLCount` int,
 ADD COLUMN `SLCount` int,
 ADD COLUMN `TotalAvailableSF` decimal(14,3),
 ADD COLUMN `MinAskingSalePrice` decimal(14,3),
 ADD COLUMN `MaxAskingSalePrice` decimal(14,3),
 ADD COLUMN `AskingLeaseRatePerYearMin` decimal(14,3),
 ADD COLUMN `AskingLeaseRatePerYearMax` decimal(14,3),
 ADD COLUMN `ParcelInfo` text,
 ADD COLUMN `PropertyKey` varchar(36),
 ADD COLUMN `TotalSaleSizeSF` decimal(14,3), ADD COLUMN `ContributedGBASizeSF` decimal(14,3), ADD COLUMN `GLASizeSF` decimal(14,3), ADD COLUMN `GLARSizeSF` decimal(14,3), ADD COLUMN `MezzanineSizeSF` decimal(14,3), ADD COLUMN `AwningsSizeSF` decimal(14,3);



-- Replicate the existing Property Summary foreign keys constraints and indexes for the Migrated Columns in the Property table

CREATE INDEX `fk_psummary_addressid_idx` ON Empirical_Prod.Property (AddressID);
CREATE INDEX `fk_psummary_groupid_idx` ON Empirical_Prod.Property (GroupID);
CREATE INDEX `fk_psummary_locationid_idx` ON Empirical_Prod.Property (LocationID);
CREATE INDEX `fk_psummary_researchtypeid_idx` ON Empirical_Prod.Property (ResearchTypeID);
CREATE INDEX `fk_psummary_specificusetypeid_idx` ON Empirical_Prod.Property (SpecificUseID);
CREATE INDEX `fk_psummary_usetypeid_idx` ON Empirical_Prod.Property (UseTypeID);
CREATE INDEX `idex_psummary_pid_locid` ON Empirical_Prod.Property (LocationID,PropertyID);
CREATE INDEX `idx_propertysummary_buildingsizesf` ON Empirical_Prod.Property (BuildingSizeSF);
CREATE INDEX `idx_propertysummary_propkey` ON Empirical_Prod.Property (PropertyKey)

ALTER TABLE Empirical_Prod.Property ADD CONSTRAINT `fk_prop_fk_psummary_addressid` FOREIGN KEY (`AddressID`) REFERENCES `Address` (`AddressID`) ON DELETE NO ACTION ON UPDATE NO ACTION;

ALTER TABLE Empirical_Prod.Property ADD CONSTRAINT `fk_prop_fk_psummary_groupid` FOREIGN KEY (`GroupID`) REFERENCES `Group` (`GroupID`) ON DELETE NO ACTION ON UPDATE NO ACTION;

ALTER TABLE Empirical_Prod.Property ADD CONSTRAINT `fk_prop_fk_psummary_locationid` FOREIGN KEY (`LocationID`) REFERENCES `Location` (`LocationID`) ON DELETE NO ACTION ON UPDATE NO ACTION;

ALTER TABLE Empirical_Prod.Property ADD CONSTRAINT `fk_prop_fk_psummary_researchtypeid` FOREIGN KEY (`ResearchTypeID`) REFERENCES `PropertyResearchType` (`PropertyResearchTypeID`) ON DELETE NO ACTION ON UPDATE NO ACTION;

ALTER TABLE Empirical_Prod.Property ADD CONSTRAINT `fk_prop_fk_psummary_specificusetypeid` FOREIGN KEY (`SpecificUseID`) REFERENCES `SpecificUses` (`SpecificUsesID`) ON DELETE NO ACTION ON UPDATE NO ACTION;

ALTER TABLE Empirical_Prod.Property ADD CONSTRAINT `fk_prop_fk_psummary_usetypeid` FOREIGN KEY (`UseTypeID`) REFERENCES `UseType` (`UseTypeID`) ON DELETE NO ACTION ON UPDATE NO ACTION




-- Data Backfill Query: Empirical_Prod.PropertySummary Data to Property Table

UPDATE Empirical_Prod.Property p
JOIN Empirical_Prod.PropertySummary ps ON p.PropertyID = ps.PropertyID
SET
    p.`ResearchTypeID` = ps.`ResearchTypeID`,
    p.`ResearchTypeName` = ps.`ResearchTypeName`,
    p.`TrueOwners` = ps.`TrueOwners`,
    p.`RecordedOwners` = ps.`RecordedOwners`,
    p.`UseTypeID` = ps.`UseTypeID`,
    p.`UseTypeName` = ps.`UseTypeName`,
    p.`SpecificUseID` = ps.`SpecificUseID`,
    p.`SpecificUseName` = ps.`SpecificUseName`,
    p.`AddressID` = ps.`AddressID`,
    p.`LocationID` = ps.`LocationID`,
    p.`GroupID` = ps.`GroupID`,
    p.`BuildingSizeSF` = ps.`BuildingSizeSF`,
    p.`LotSizeSF` = ps.`LotSizeSF`,
    p.`MainPhotoUrl` = ps.`MainPhotoUrl`,
    p.`ListingCompany` = ps.`ListingCompany`,
    p.`LCount` = ps.`LCount`,
    p.`LLCount` = ps.`LLCount`,
    p.`DLCount` = ps.`DLCount`,
    p.`SLCount` = ps.`SLCount`,
    p.`TotalAvailableSF` = ps.`TotalAvailableSF`,
    p.`MinAskingSalePrice` = ps.`MinAskingSalePrice`,
    p.`MaxAskingSalePrice` = ps.`MaxAskingSalePrice`,
    p.`AskingLeaseRatePerYearMin` = ps.`AskingLeaseRatePerYearMin`,
    p.`AskingLeaseRatePerYearMax` = ps.`AskingLeaseRatePerYearMax`,
    p.`ParcelInfo` = ps.`ParcelInfo`,
    p.`PropertyKey` = ps.`PropertyKey`,
    p.`TotalSaleSizeSF` = ps.`TotalSaleSizeSF`,
    p.`ContributedGBASizeSF` = ps.`ContributedGBASizeSF`,
    p.`GLASizeSF` = ps.`GLASizeSF`,
    p.`GLARSizeSF` = ps.`GLARSizeSF`,
    p.`MezzanineSizeSF` = ps.`MezzanineSizeSF`,
    p.`AwningsSizeSF` = ps.`AwningsSizeSF`;



ALTER TABLE `Empirical_Prod`.`Lease`
DROP FOREIGN KEY `fk_StartingRateMethod`,
DROP FOREIGN KEY `fk_leasetranstypeid_leasetranstypeid_idx`,
DROP FOREIGN KEY `fk_LeaseTermsMethod`,
DROP FOREIGN KEY `fk_lease_SuiteStatus`,
DROP FOREIGN KEY `fk_lease_SprinklerType`,
DROP FOREIGN KEY `fk_lease_sharelevelid`,
DROP FOREIGN KEY `fk_lease_Possession`,
DROP FOREIGN KEY `fk_lease_oriinationtypeid`,
DROP FOREIGN KEY `fk_lease_listingtypeid`,
DROP FOREIGN KEY `fk_lease_LeaseType`,
DROP FOREIGN KEY `fk_lease_leasestatusid`,
DROP FOREIGN KEY `fk_lease_leaseresearchstatusid`,
DROP FOREIGN KEY `fk_FaceRentPerSqmMethod`,
DROP FOREIGN KEY `fk_ExpirationDateMethod`,
DROP FOREIGN KEY `fk_DateOccupiedMethod`,
DROP FOREIGN KEY `fk_AvailableSpaceSFMethod`;
ALTER TABLE `Empirical_Prod`.`Lease`
DROP INDEX `fk_StartingRateMethod` ,
DROP INDEX `fk_FaceRentPerSqmMethod` ,
DROP INDEX `fk_LeaseTermsMethod` ,
DROP INDEX `fk_ExpirationDateMethod` ,
DROP INDEX `fk_DateOccupiedMethod` ,
DROP INDEX `fk_AvailableSpaceSFMethod` ,
DROP INDEX `fk_lease_leasestatusid_idx` ,
DROP INDEX `fk_lease_sharelevelid_idx` ,
DROP INDEX `fk_lease_listingtypeid_idx` ,
DROP INDEX `fk_lease_leaseresearchstatusid_idx` ,
DROP INDEX `fk_lease_oriinationtypeid_idx` ,
DROP INDEX `fk_leasetranstypeid_leasetranstypeid_idx` ,
DROP INDEX `fk_lease_LeaseType_idx` ,
DROP INDEX `fk_lease_SprinklerType_idx` ,
DROP INDEX `fk_lease_Possession_idx` ,
DROP INDEX `fk_lease_SuiteStatus_idx` ;
;

ALTER TABLE `Empirical_Prod`.`Listing`
DROP FOREIGN KEY `fk_Listing_SpecialCondition`,
DROP FOREIGN KEY `fk_Listing_ShareLevel`,
DROP FOREIGN KEY `fk_Listing_RecordtypeID`,
DROP FOREIGN KEY `fk_Listing_ListingType`,
DROP FOREIGN KEY `fk_Listing_Agreement`;
ALTER TABLE `Empirical_Prod`.`Listing`
DROP INDEX `fk_Listing_RecordtypeID_idx` ,
DROP INDEX `fk_Listing_SpecialCondition_idx` ,
DROP INDEX `fk_Listing_Agreement_idx` ,
DROP INDEX `fk_Listing_ShareLevel_idx` ,
DROP INDEX `fk_Listing_ListingType_idx` ;
;

ALTER TABLE `Empirical_Prod`.`Sale`
DROP FOREIGN KEY `fk_sale_transactionorginationtype`,
DROP FOREIGN KEY `fk_sale_saletypeid`,
DROP FOREIGN KEY `fk_sale_salemethodid`,
DROP FOREIGN KEY `fk_sale_rightsIncludedTypeID`,
DROP FOREIGN KEY `fk_sale_noitypeid`,
DROP FOREIGN KEY `fk_sale_noisourceid`,
DROP FOREIGN KEY `fk_sale_leaseincomeclassId`,
DROP FOREIGN KEY `fk_sale_instrumentdoctypeid`,
DROP FOREIGN KEY `fk_sale_incomesourceid`,
DROP FOREIGN KEY `fk_sale_incomeclassId`,
DROP FOREIGN KEY `fk_sale_encumbrancetypeid`,
DROP FOREIGN KEY `fk_sale_conveyed`,
DROP FOREIGN KEY `fk_sale_condotypeid`,
DROP FOREIGN KEY `fk_sale_cashflowtypeid`,
DROP FOREIGN KEY `fk_sale_capratetypeid`,
DROP FOREIGN KEY `fk_sale_capratesourceid`,
DROP FOREIGN KEY `fk_sale_armslengthtypeid`;
ALTER TABLE `Empirical_Prod`.`Sale`
DROP INDEX `fk_sale_leaseincomeclassId_idx` ,
DROP INDEX `fk_forsale_incomeclassId_idx` ,
DROP INDEX `fk_sale_salemethodid_idx` ,
DROP INDEX `fk_sale_condotypeid_idx` ,
DROP INDEX `fk_sale_transactionorginationtype_idx` ,
DROP INDEX `fk_sale_noitypeid_idx` ,
DROP INDEX `fk_sale_noisourceid_idx` ,
DROP INDEX `fk_sale_cashflowtypeid_idx` ,
DROP INDEX `fk_sale_capratetypeid_idx` ,
DROP INDEX `fk_sale_capratesourceid_idx` ,
DROP INDEX `fk_sale_incomesourceid_idx` ,
DROP INDEX `fk_sale_encumbrancetypeid_idx` ,
DROP INDEX `fk_sale_instrumentdoctypeid_idx` ,
DROP INDEX `fk_sale_conveyed_idx` ,
DROP INDEX `fk_sale_saletypeid_idx` ,
DROP INDEX `fk_sale_rightsIncludedTypeID_idx` ,
DROP INDEX `fk_sale_armslengthtypeid_idx` ;
;

ALTER TABLE `Empirical_Prod`.`SubHideReasons`
DROP FOREIGN KEY `fk_SubHideReasons_HideReasonID`;
ALTER TABLE `Empirical_Prod`.`SubHideReasons`
DROP INDEX `fk_SubHideReasons_HideReasonID_idx` ;
;

ALTER TABLE `Empirical_Prod`.`Suite`
DROP FOREIGN KEY `fk_Suite_TruckwellDoors`,
DROP FOREIGN KEY `fk_Suite_SuiteUseID`,
DROP FOREIGN KEY `fk_Suite_SuiteStatus`,
DROP FOREIGN KEY `fk_Suite_SprinklerType`,
DROP FOREIGN KEY `fk_Suite_Possession`,
DROP FOREIGN KEY `fk_Suite_LeaseType`,
DROP FOREIGN KEY `fk_Suite_DockDoorType`;
ALTER TABLE `Empirical_Prod`.`Suite`
DROP INDEX `fk_Suite_SuiteUseID_idx` ,
DROP INDEX `fk_Suite_LeaseType_idx` ,
DROP INDEX `fk_Suite_SprinklerType_idx` ,
DROP INDEX `fk_Suite_TruckwellDoors_idx` ,
DROP INDEX `fk_Suite_DockDoorType_idx` ,
DROP INDEX `fk_Suite_Possession_idx` ,
DROP INDEX `fk_Suite_SuiteStatus_idx` ;
;

ALTER TABLE `Empirical_Prod`.`SuiteTenant`
DROP FOREIGN KEY `fk_suiteteant_tenantstatusid`;
ALTER TABLE `Empirical_Prod`.`SuiteTenant`
DROP INDEX `fk_suiteteant_tenantstatusid_idx` ;
;

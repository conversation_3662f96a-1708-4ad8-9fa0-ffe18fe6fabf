-- Populate Empirical_Prod.lookup with Unified Data from Various lookup Tables Sharing a Common Structure.

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, IsRetired, CategoryTypeID)
SELECT ConstructionTypeID, ConstructionTypeName, IsActive, Retired, 'ConstructionType'
FROM Empirical_Prod.ConstructionType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT DataTypeID, DataTypeName, IsActive, 'DataType'
FROM Empirical_Prod.DataType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT DockDoorsTypeID, DockDoorsTypeName, IsActive, 'DockDoorsType;'
FROM Empirical_Prod.DockDoorsType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT SizeSourceID, SizeSourceName, IsActive, 'SizeSource'
FROM Empirical_Prod.SizeSource;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT TenancyID, TenancyName, IsActive, 'Tenancy'
FROM Empirical_Prod.Tenancy;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT MessageStatusID, MessageStatusName, IsActive, 'MailMessageStatus'
FROM Empirical_Prod.MailMessageStatus;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT SprinklerTypeID, SprinklerTypeName, IsActive, 'SprinklerType'
FROM Empirical_Prod.SprinklerType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT HVACTypeID, HVACTypeName, IsActive, 'HVACType'
FROM Empirical_Prod.HVACType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT EnergyStarRatingID, EnergyStarRatingName, IsActive, 'EnergyStarRating'
FROM Empirical_Prod.EnergyStarRating;


INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT WaterStarRatingID, WaterStarRatingName, IsActive, 'WaterStarRating'
FROM Empirical_Prod.WaterStarRating;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT GreenStarRatingID, GreenStarRatingName, IsActive, 'GreenStarRating'
FROM Empirical_Prod.GreenStarRating;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT AmenitiesTypeID, AmenitiesTypeName, IsActive, 'AmenitiesType'
FROM Empirical_Prod.AmenitiesType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT BuildSpecStatusID, BuildSpecStatusName, IsActive, 'BuildSpecStatus'
FROM Empirical_Prod.BuildSpecStatus;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT RoofTypeID, RoofTypeName, IsActive, 'RoofType'
FROM Empirical_Prod.RoofType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT PowerTypeID, PowerTypeName, IsActive, 'PowerTypes'
FROM Empirical_Prod.PowerTypes;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT LandUseID, LandUseName, IsActive, 'LandUse'
FROM Empirical_Prod.LandUse;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT ZoningClassID, ZoningClassName, IsActive, 'ZoningClass'
FROM Empirical_Prod.ZoningClass;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, Abbr, IsActive, CategoryTypeID)
SELECT PrefixID, Prefix, PrefixName, IsActive, 'Prefix'
FROM Empirical_Prod.Prefix;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, Abbr, IsActive, CategoryTypeID)
SELECT SuffixID, Suffix, SuffixName, IsActive, 'Suffix'
FROM Empirical_Prod.Suffix;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT QuadrantID, QuadrantName, IsActive, 'Quadrant'
FROM Empirical_Prod.Quadrant;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT RecordTypeID, RecordTypeName, IsActive, 'RecordType'
FROM Empirical_Prod.RecordType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT ShareLevelID, ShareLevelName, IsActive, 'ShareLevel'
FROM Empirical_Prod.ShareLevel;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT AgreementTypeID, AgreementTypeName, IsActive, 'AgreementType'
FROM Empirical_Prod.AgreementType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, IsRetired, CategoryTypeID)
SELECT ListingTypeID, ListingTypeName, IsActive, IsRetired, 'ListingType'
FROM Empirical_Prod.ListingType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT SaleTypeID, SaleTypeName, IsActive, 'SaleType'
FROM Empirical_Prod.SaleType;
INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT PossessionTypeID, PossessionTypeName, IsActive, 'PossessionType'
FROM Empirical_Prod.PossessionType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT MediaTypeID, MediaTypeName, IsActive, 'MediaType'
FROM Empirical_Prod.MediaType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT MediaSubTypeID, MediaSubTypeName, IsActive, 'MediaSubType'
FROM Empirical_Prod.MediaSubType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT MediaSourceID, MediaSourceName, IsActive, 'MediaSource'
FROM Empirical_Prod.MediaSource;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT RoofTopSourceID, RoofTopSource, IsActive, 'RoofTopSource'
FROM Empirical_Prod.RoofTopSource;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT AddressTypeID, AddressTypeDisplay, IsActive, 'AddressType'
FROM Empirical_Prod.AddressType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT ArmsLengthTypeID, ArmsLengthTypeName, IsActive, 'ArmsLengthType'
FROM Empirical_Prod.ArmsLengthType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT BuyerContactTypeID, BuyerContactTypeName, IsActive, 'BuyerContactType'
FROM Empirical_Prod.BuyerContactType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT BuyerTypeID, BuyerTypeName, IsActive, 'BuyerType'
FROM Empirical_Prod.BuyerType;
INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT CapRateTypeID, CapRateTypeID, IsActive, 'CapRateType'
FROM Empirical_Prod.CapRateType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT CashFlowTypeID, CashFlowTypeName, IsActive, 'CashFlowType'
FROM Empirical_Prod.CashFlowType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT ContactTypeID, ContactTypeName, IsActive, 'ContactType'
FROM Empirical_Prod.ContactType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT EncumbrancesTypeID, EncumbrancesTypeName, IsActive, 'EncumbrancesType'
FROM Empirical_Prod.EncumbrancesType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT EscalationTypeID, EscalationTypeName, IsActive, 'EscalationType'
FROM Empirical_Prod.EscalationType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT LeedStatusID, LeedStatusName, IsActive, 'LeedStatus'
FROM Empirical_Prod.LeedStatus;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT EnergyProviderID, EnergyProviderName, IsActive, 'EnergyProvider'
FROM Empirical_Prod.EnergyProvider;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, IsRetired, CategoryTypeID)
SELECT ExpirationDateMethodID, ExpirationDateMethodName, IsActive, IsRetired, 'ExpirationDateMethods'
FROM Empirical_Prod.ExpirationDateMethods;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT EarthquakeZoneID, EarthquakeZoneName, IsActive, 'EarthquakeZone'
FROM Empirical_Prod.EarthquakeZone;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, IsRetired, CategoryTypeID)
SELECT AvailableSpaceSFMethodID, AvailableSpaceSFMethodName, IsActive, IsRetired, 'AvailableSpaceSFMethods'
FROM Empirical_Prod.AvailableSpaceSFMethods;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT CleanRoomClassID, CleanRoomClass, IsActive, 'CleanRoomClass'
FROM Empirical_Prod.CleanRoomClass;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT ConveyedID, ConveyedName, IsActive, 'Conveyed'
FROM Empirical_Prod.Conveyed;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, IsRetired, CategoryTypeID)
SELECT DateOccupiedMethodID, DateOccupiedMethodName, IsActive, IsRetired, 'DateOccupiedMethods'
FROM Empirical_Prod.DateOccupiedMethods;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, IsRetired, CategoryTypeID)
SELECT FaceRentPerSqmMethodID, FaceRentPerSqmMethodName, IsActive, IsRetired, 'FaceRentPerSqmMethods'
FROM Empirical_Prod.FaceRentPerSqmMethods;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT FeatureID, FeatureName, IsActive, 'Features'
FROM Empirical_Prod.Features;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT FeedFormatID, FeedFormatName, IsActive, 'FeedFormats'
FROM Empirical_Prod.FeedFormats;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT FinanceTypeID, FinanceTypeName, IsActive, 'FinanceType'
FROM Empirical_Prod.FinanceType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT HideReasonID, HideReasonName, IsActive, 'HideReasons'
FROM Empirical_Prod.HideReasons;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT IncomeClassID, IncomeClassName, IsActive, 'IncomeClass'
FROM Empirical_Prod.IncomeClass;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT IncomeSourceID, IncomeSourceName, IsActive, 'IncomeSource'
FROM Empirical_Prod.IncomeSource;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT InstrumentDocTypeID, InstrumentDocTypeName, IsActive, 'InstrumentDocType'
FROM Empirical_Prod.InstrumentDocType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT LeaseContactTypeID, LeaseContactTypeName, IsActive, 'LeaseContactType'
FROM Empirical_Prod.LeaseContactType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT LeaseExtensionTypeID, LeaseExtensionTypeName, IsActive, 'LeaseExtensionType'
FROM Empirical_Prod.LeaseExtensionType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT LeaseIncomeClassID, LeaseIncomeClassName, IsActive, 'LeaseIncomeClass'
FROM Empirical_Prod.LeaseIncomeClass;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT LeaseResearchStatusID, LeaseResearchStatusName, IsActive, 'LeaseResearchStatus'
FROM Empirical_Prod.LeaseResearchStatus;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT LeaseReviewTypeID, LeaseReviewTypeName, IsActive, 'LeaseReviewType'
FROM Empirical_Prod.LeaseReviewType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT LeaseStatusID, LeaseStatusName, IsActive, 'LeaseStatus'
FROM Empirical_Prod.LeaseStatus;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT MarketBriefStatusID, MarketBriefStatusName, IsActive, 'MarketBriefStatus'
FROM Empirical_Prod.MarketBriefStatus;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT MediaRelationTypeID, MediaRelationTypeName, IsActive, 'MediaRelationType'
FROM Empirical_Prod.MediaRelationType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT NOIMethodID, NOIMethodName, IsActive, 'NOIMethod'
FROM Empirical_Prod.NOIMethod;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT NOISourceID, NOISourceName, IsActive, 'NOISource'
FROM Empirical_Prod.NOISource;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT NOITypeID, NOITypeName, IsActive, 'NOIType'
FROM Empirical_Prod.NOIType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT OfficeHvacTypeID, OfficeHvacTypeName, IsActive, 'OfficeHvacType'
FROM Empirical_Prod.OfficeHvacType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT OtherRelatioshipTypeID, OtherRelationshipTypeName, IsActive, 'OtherRelatioshipType'
FROM Empirical_Prod.OtherRelatioshipType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT PhoneTypeID, PhoneTypeName, IsActive, 'PhoneType'
FROM Empirical_Prod.PhoneType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT PowerTypeID, PowerTypeName, IsActive, 'PowerTypes'
FROM Empirical_Prod.PowerTypes;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT PriceConfirmationStatusID, PriceConfirmationStatusName, IsActive, 'PriceConfirmationStatus'
FROM Empirical_Prod.PriceConfirmationStatus;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT TemplateStatusID, TemplateStatusName, IsActive, 'ReportTemplateStatus'
FROM Empirical_Prod.ReportTemplateStatus;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT ReviewTypeID, ReviewTypeName, IsActive, 'ReviewTypes'
FROM Empirical_Prod.ReviewTypes;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT RightsIncludedTypeID, RightsIncludedTypeName, IsActive, 'RightsIncludedType'
FROM Empirical_Prod.RightsIncludedType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT AccessTypeID, AccessTypeName, IsActive, 'RoadAccessType'
FROM Empirical_Prod.RoadAccessType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT SalePriceSourceID, SalePriceSourceName, IsActive, 'SalePriceSource'
FROM Empirical_Prod.SalePriceSource;


INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT SaleMethodID, SaleMethodName, IsActive, 'SaleMethod'
FROM Empirical_Prod.SaleMethod;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT SellerTypeID, SellerTypeName, IsActive, 'SellerType'
FROM Empirical_Prod.SellerType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT SizeTypeID, SizeTypeName, IsActive, 'SizeType'
FROM Empirical_Prod.SizeType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT SuiteTypeID, SuiteTypeName, IsActive, 'SuiteType'
FROM Empirical_Prod.SuiteType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT TruckwellDoorsTypeID, TruckwellDoorsTypeName, IsActive, 'TruckwellDoorsType'
FROM Empirical_Prod.TruckwellDoorsType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, IsRetired, CategoryTypeID)
SELECT TransactionOriginationTypeID, TransactionOriginationTypeName, IsActive, IsRetired, 'TransactionOriginationType'
FROM Empirical_Prod.TransactionOriginationType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT TenantVerificationSourceID, TenantVerificationSourceName, IsActive, 'TenantVerificationSource'
FROM Empirical_Prod.TenantVerificationSource;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT TenantStatusID, TenantStatusName, IsActive, 'TenantStatus'
FROM Empirical_Prod.TenantStatus;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT VacancyID, VacancyName, IsActive, 'Vacancy'
FROM Empirical_Prod.Vacancy;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT RoleID, RoleName, 1, 'Role'
FROM Empirical_Prod.Role;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT UnitID, UnitName, IsActive, 'Unit'
FROM Empirical_Prod.Unit;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, DisplayText, CategoryTypeID)
SELECT CondoTypeID, CondoTypeName, IsActive, DisplayText, 'CondoType'
FROM Empirical_Prod.CondoType;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT RatingTierID, RatingTierName, IsActive, 'RatingTier'
FROM Empirical_Prod.RatingTier;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT FloorStatusID, FloorStatusName, IsActive, 'FloorStatus'
FROM Empirical_Prod.FloorStatus;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT TenantResearchStatusID, TenantResearchStatusName, IsActive, 'TenantResearchStatus'
FROM Empirical_Prod.TenantResearchStatus;

INSERT INTO Empirical_Prod.lookup (LookUpTypeID, LookUpTypeName, IsActive, CategoryTypeID)
SELECT CompanyTierID, TierName, IsActive, 'CompanyTier'
FROM Empirical_Prod.CompanyTier;

-- Remove Foreign Key Constraints from Lookup Fields from Empirical_Prod.Property Table
ALTER TABLE `Empirical_Prod`.`Property`
DROP FOREIGN KEY `fk_Property_ZoningClassID`,
DROP FOREIGN KEY `fk_Property_Zoning_Surrounding`,
DROP FOREIGN KEY `fk_Property_Zoning_Potential`,
DROP FOREIGN KEY `fk_Property_TenancyType`,
DROP FOREIGN KEY `fk_Property_SprinklerType`,
DROP FOREIGN KEY `fk_Property_SizeSourceID`,
DROP FOREIGN KEY `fk_Property_SizeSource_NRA`,
DROP FOREIGN KEY `fk_Property_LotSizeSourceID`,
DROP FOREIGN KEY `fk_Property_HVACType`,
DROP FOREIGN KEY `fk_Property_HVAC_OfficeAC`,
DROP FOREIGN KEY `fk_Property_GreenStarRatingID`,
DROP FOREIGN KEY `fk_Property_GovermentInterest`,
DROP FOREIGN KEY `fk_Property_EnergyStarRatingID`,
DROP FOREIGN KEY `fk_Property_ConstuctionStatus`,
DROP FOREIGN KEY `fk_Property_ConstructionType`,
DROP FOREIGN KEY `fk_Property_CondoTypeID`,
DROP FOREIGN KEY `fk_Property_ClassType`,
DROP FOREIGN KEY `fk_Property_Building`;
ALTER TABLE `Empirical_Prod`.`Property`
DROP INDEX `fk_Property_GreenStarRatingID_idx` ,
DROP INDEX `fk_Property_EnergyStarRatingID_idx` ,
DROP INDEX `fk_Property_SizeSource_NRA_idx` ,
DROP INDEX `fk_Property_SizeSourceID_idx` ,
DROP INDEX `fk_Property_CondoTypeID_idx` ,
DROP INDEX `fk_Property_Building_idx` ,
DROP INDEX `fk_Property_Zoning_Surrounding_idx` ,
DROP INDEX `fk_Property_Zoning_Potential_idx` ,
DROP INDEX `fk_Property_ZoningClassID_idx` ,
DROP INDEX `fk_Property_HVAC_OfficeAC_idx` ,
DROP INDEX `fk_Property_GovermentInterest_idx` ,
DROP INDEX `fk_Property_ClassType_idx` ,
DROP INDEX `fk_Property_TenancyType_idx` ,
DROP INDEX `fk_Property_SprinklerType_idx` ,
DROP INDEX `fk_Property_HVACType_idx` ,
DROP INDEX `fk_Property_ConstructionType_idx` ,
DROP INDEX `fk_Property_ConstuctionStatus_idx` ;
;

-- Empirical_Prod.Address Table

ALTER TABLE `Empirical_Prod`.`Address`
DROP FOREIGN KEY `fk_address_type_id`,
DROP FOREIGN KEY `fk_address_suffix2`,
DROP FOREIGN KEY `fk_address_suffix`,
DROP FOREIGN KEY `fk_address_quadrant`,
DROP FOREIGN KEY `fk_address_prefix2`,
DROP FOREIGN KEY `fk_address_prefix`;
ALTER TABLE `Empirical_Prod`.`Address`
DROP INDEX `fk_address_quadrant_idx` ,
DROP INDEX `idx_Address_SuffixID` ,
DROP INDEX `fk_address_type_id_idx` ,
DROP INDEX `fk_address_suffix2_idx` ,
DROP INDEX `fk_address_prefix2_idx` ,
DROP INDEX `fk_address_prefix_idx` ;
;

-- Empirical_Prod.Location Table

ALTER TABLE `Empirical_Prod`.`Location`
DROP FOREIGN KEY `fk_location_rooftopsourceid`;
ALTER TABLE `Empirical_Prod`.`Location`
DROP INDEX `fk_location_rooftopsourceid_idx` ;
;

-- Empirical_Prod.PropertyResearchStatus Table

ALTER TABLE `Empirical_Prod`.`PropertyResearchStatus`
DROP FOREIGN KEY `fk_PropertyResearchStatus_ResearchTypeID`;
ALTER TABLE `Empirical_Prod`.`PropertyResearchStatus`
DROP INDEX `fk_PropertyResearchStatus_ResearchTypeID_idx` ;
;

 -- Empirical_Prod.MediaRelationship Table.

ALTER TABLE `Empirical_Prod`.`MediaRelationship`
DROP FOREIGN KEY `fk_MediaRelationship_RelationTypeID`,
DROP FOREIGN KEY `fk_MediaRelationship_MediaTypeID`,
DROP FOREIGN KEY `fk_MediaRelationship_MediaSubTypeID`;
ALTER TABLE `Empirical_Prod`.`MediaRelationship`
DROP INDEX `fk_MediaRelationship_MediaSubTypeID_idx` ,
DROP INDEX `fk_MediaRelationship_MediaTypeID_idx` ,
DROP INDEX `fk_MediaRelationship_RelationTypeID_idx` ;
;

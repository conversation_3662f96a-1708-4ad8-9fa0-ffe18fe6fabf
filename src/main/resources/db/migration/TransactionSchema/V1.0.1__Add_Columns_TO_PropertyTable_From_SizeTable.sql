
-- Merge SizeTypes where ParentTableID = 1(Property) to Empirical_Prod.Property

ALTER TABLE Empirical_Prod.Property
ADD COLUMN `BuildingSize` DECIMAL(12,3),
ADD COLUMN `LotSize` DECIMAL(12,3),
ADD COLUMN `TotalAnchor` DECIMAL(12,3),
ADD COLUMN `MinFloorSize` DECIMAL(12,3),
ADD COLUMN `MaxFloorSize` DECIMAL(12,3),
ADD COLUMN `OfficeSize` DECIMAL(12,3),
ADD COLUMN `RetailSize` DECIMAL(12,3),
ADD COLUMN `LotSizeAC` DECIMAL(12,3),
ADD COLUMN `NLA` DECIMAL(12,3),
ADD COLUMN `NLAAC` DECIMAL(12,3),
ADD COLUMN `Mezzanine` DECIMAL(12,3),
ADD COLUMN `ContributedGBA` DECIMAL(12,3),
ADD COLUMN `GLA` DECIMAL(12,3),
ADD COLUMN `GLAR` DECIMAL(12,3)
ADD COLUMN `Awnings` DECIMAL(12,3);




-- Size Table Data

SET SQL_SAFE_UPDATES = 0;

-- Data Backfill Query: Empirical_Prod.Size Data to Property Table

UPDATE Empirical_Prod.Property p
LEFT JOIN Empirical_Prod.Size s8  ON p.PropertyID = s8.ParentID  AND s8.SizeTypeID = 8
LEFT JOIN Empirical_Prod.Size s14 ON p.PropertyID = s14.ParentID AND s14.SizeTypeID = 14
LEFT JOIN Empirical_Prod.Size s13 ON p.PropertyID = s13.ParentID AND s13.SizeTypeID = 13
LEFT JOIN Empirical_Prod.Size s26 ON p.PropertyID = s26.ParentID AND s26.SizeTypeID = 26
LEFT JOIN Empirical_Prod.Size s27 ON p.PropertyID = s27.ParentID AND s27.SizeTypeID = 27
LEFT JOIN Empirical_Prod.Size s11 ON p.PropertyID = s11.ParentID AND s11.SizeTypeID = 11
LEFT JOIN Empirical_Prod.Size s12 ON p.PropertyID = s12.ParentID AND s12.SizeTypeID = 12
LEFT JOIN Empirical_Prod.Size s9  ON p.PropertyID = s9.ParentID  AND s9.SizeTypeID = 9
LEFT JOIN Empirical_Prod.Size s15 ON p.PropertyID = s15.ParentID AND s15.SizeTypeID = 15
LEFT JOIN Empirical_Prod.Size s10 ON p.PropertyID = s10.ParentID AND s10.SizeTypeID = 10
LEFT JOIN Empirical_Prod.Size s28 ON p.PropertyID = s28.ParentID AND s28.SizeTypeID = 28
LEFT JOIN Empirical_Prod.Size s29 ON p.PropertyID = s29.ParentID AND s29.SizeTypeID = 29
LEFT JOIN Empirical_Prod.Size s30 ON p.PropertyID = s30.ParentID AND s30.SizeTypeID = 30
SET
    p.BuildingSize    = COALESCE(s8.SizeValue, p.BuildingSize),
    p.LotSize         = COALESCE(s14.SizeValue, p.LotSize),
    p.TotalAnchor     = COALESCE(s13.SizeValue, p.TotalAnchor),
    p.MinFloorSize    = COALESCE(s26.SizeValue, p.MinFloorSize),
    p.MaxFloorSize    = COALESCE(s27.SizeValue, p.MaxFloorSize),
    p.OfficeSize      = COALESCE(s11.SizeValue, p.OfficeSize),
    p.RetailSize      = COALESCE(s12.SizeValue, p.RetailSize),
    p.LotSizeAC       = COALESCE(s9.SizeValue, p.LotSizeAC),
    p.NLA             = COALESCE(s15.SizeValue, p.NLA),
    p.NLAAC           = COALESCE(s10.SizeValue, p.NLAAC),
    p.ContributedGBA  = COALESCE(s28.SizeValue, p.ContributedGBA),
    p.GLA            = COALESCE(s29.SizeValue, p.GLA),
    p.GLAR           = COALESCE(s30.SizeValue, p.GLAR);

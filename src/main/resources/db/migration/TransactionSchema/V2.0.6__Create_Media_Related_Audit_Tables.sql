CREATE TABLE `Media_Audit` (
  `MediaID` int NOT NULL,
  `revision_id` int NOT NULL,
  `revision_type` tinyint DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `description_MOD` bit(1) DEFAULT NULL,
  `Ext` varchar(45) DEFAULT NULL,
  `ext_MOD` bit(1) DEFAULT NULL,
  `Height` int DEFAULT NULL,
  `height_MOD` bit(1) DEFAULT NULL,
  `IsOwnMedia` bit(1) DEFAULT NULL,
  `isOwnMedia_MOD` bit(1) DEFAULT NULL,
  `MediaName` varchar(255) DEFAULT NULL,
  `mediaName_MOD` bit(1) DEFAULT NULL,
  `MediaSourceID` int DEFAULT NULL,
  `mediaSourceId_MOD` bit(1) DEFAULT NULL,
  `Path` varchar(255) DEFAULT NULL,
  `path_MOD` bit(1) DEFAULT NULL,
  `Size` int DEFAULT NULL,
  `size_MOD` bit(1) DEFAULT NULL,
  `SourceComments` text,
  `sourceComments_MOD` bit(1) DEFAULT NULL,
  `Width` int DEFAULT NULL,
  `width_MOD` bit(1) DEFAULT NULL,
  PRIMARY KEY (`MediaID`,`revision_id`),
  CONSTRAINT `FK_MediaAudit_RevisionInfo` FOREIGN KEY (`revision_id`) REFERENCES `Revision_Info` (`id`)
) ;

CREATE TABLE `MediaRelationship_Audit` (
  `MediaRelationshipID` int NOT NULL,
  `revision_id` int NOT NULL,
  `revision_type` tinyint DEFAULT NULL,
  `IsActive` bit(1) DEFAULT NULL,
  `isActive_MOD` bit(1) DEFAULT NULL,
  `IsDefault` bit(1) DEFAULT NULL,
  `isDefault_MOD` bit(1) DEFAULT NULL,
  `MediaID` int DEFAULT NULL,
  `mediaId_MOD` bit(1) DEFAULT NULL,
  `MediaRelationTypeID` int DEFAULT NULL,
  `mediaRelationTypeId_MOD` bit(1) DEFAULT NULL,
  `MediaSubTypeID` int DEFAULT NULL,
  `mediaSubTypeId_MOD` bit(1) DEFAULT NULL,
  `MediaTypeID` int DEFAULT NULL,
  `mediaTypeId_MOD` bit(1) DEFAULT NULL,
  `RelationID` int DEFAULT NULL,
  `relationId_MOD` bit(1) DEFAULT NULL,
  `media_MOD` bit(1) DEFAULT NULL,
  `PropertyID` int DEFAULT NULL,
  `property_MOD` bit(1) DEFAULT NULL,
  PRIMARY KEY (`MediaRelationshipID`,`revision_id`),
  CONSTRAINT `FK_MediaRelationShipAudit_RevisionInfo` FOREIGN KEY (`revision_id`) REFERENCES `Revision_Info` (`id`)
) ;

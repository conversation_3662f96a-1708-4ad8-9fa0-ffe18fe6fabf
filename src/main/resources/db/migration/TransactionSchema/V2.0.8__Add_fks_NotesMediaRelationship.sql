ALTER TABLE `Empirical_Prod`.`NotesMediaRelationship` 
ADD INDEX `fk_MediaID_idx` (`MediaID` ASC) VISIBLE;
;
ALTER TABLE `Empirical_Prod`.`NotesMediaRelationship` 
ADD CONSTRAINT `fk_MediaID`
  FOREIGN KEY (`MediaID`)
  REFERENCES `Empirical_Prod`.`Media` (`MediaID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `Empirical_Prod`.`NotesMediaRelationship` 
ADD INDEX `fk_NoteID_idx` (`NoteID` ASC) VISIBLE;
;
ALTER TABLE `Empirical_Prod`.`NotesMediaRelationship` 
ADD CONSTRAINT `fk_NoteID`
  FOREIGN KEY (`NoteID`)
  REFERENCES `Empirical_Prod`.`Notes` (`NoteID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


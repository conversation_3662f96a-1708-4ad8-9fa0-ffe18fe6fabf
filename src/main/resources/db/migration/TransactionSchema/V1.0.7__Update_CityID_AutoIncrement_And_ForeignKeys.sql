-- Drop foreign keys
ALTER TABLE Empirical_Tenants.ConfirmedTenants
DROP FOREIGN KEY fk_tenant_cityid;

ALTER TABLE Empirical_DataStage.Suite_Stage
DROP FOREIGN KEY SuiteStage_CityID;

ALTER TABLE Empirical_Prod.Address
DROP FOREIGN KEY fk_Address_City;

-- Modify CityID column
ALTER TABLE Empirical_Prod.City
MODIFY COLUMN CityID INT NOT NULL AUTO_INCREMENT;

-- Declare and set variable
SET @AutoIncrementSize = NULL;

-- Set variable to max(CityID) + 1
SELECT MAX(CityID) + 1 INTO @AutoIncrementSize FROM Empirical_Prod.City;

-- Prepare dynamic SQL to use the variable
SET @sql = CONCAT('ALTER TABLE Empirical_Prod.City AUTO_INCREMENT = ', @AutoIncrementSize);
select @sql;

-- Execute dynamic SQL
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Recreate foreign keys
ALTER TABLE Empirical_Tenants.ConfirmedTenants
ADD CONSTRAINT fk_tenant_cityid
FOREIGN KEY (CityID) REFERENCES Empirical_Prod.City (CityID)
ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE Empirical_DataStage.Suite_Stage
ADD CONSTRAINT SuiteStage_CityID
FOREIGN KEY (CityID) REFERENCES Empirical_Prod.City (CityID)
ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE Empirical_Prod.Address
ADD CONSTRAINT fk_Address_City
FOREIGN KEY (CityID) REFERENCES Empirical_Prod.City (CityID)
ON DELETE RESTRICT ON UPDATE CASCADE;

CREATE TABLE IF NOT EXISTS Empirical_Prod.Notes_Audit (
    NoteID INT NOT NULL,
    revision_id INT NOT NULL,
    revision_type TINYINT DEFAULT NULL,

    NoteTypeID INT DEFAULT NULL,
    noteTypeID_MOD BIT DEFAULT NULL,

    NoteTitle VARCHAR(100) DEFAULT NULL,
    noteTitle_MOD BIT DEFAULT NULL,

    NoteDescription MEDIUMTEXT DEFAULT NULL,
    noteDescription_MOD BIT DEFAULT NULL,

    ParentTableID INT DEFAULT NULL,
    parentTableID_MOD BIT DEFAULT NULL,

    ParentID INT DEFAULT NULL,
    parentID_MOD BIT DEFAULT NULL,

    CreatedBy INT DEFAULT NULL,
    createdBy_MOD BIT DEFAULT NULL,

    CreatedDate DATETIME DEFAULT NULL,
    createdDate_MOD BIT DEFAULT NULL,

    ModifiedBy INT DEFAULT NULL,
    modifiedBy_<PERSON>OD BIT DEFAULT NULL,

    ModifiedDate DATETIME DEFAULT NULL,
    modifiedDate_MOD BIT DEFAULT NULL,

    IsActive TINYINT(1) NOT NULL DEFAULT 1,
    isActive_MOD BIT DEFAULT NULL,

    PRIMARY KEY (NoteID, revision_id),
    CONSTRAINT FK_NotesAudit_RevisionInfo FOREIGN KEY (revision_id) REFERENCES Revision_Info (id)
);

ALTER TABLE `Empirical_Prod`.`ReleaseUpdates`
<PERSON><PERSON><PERSON> COLUMN `UpdatedBy` `ModifiedBy` INT NULL DEFAULT NULL ,
<PERSON>AN<PERSON> COLUMN `CreatedAt` `CreatedDate` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ,
CH<PERSON><PERSON> COLUMN `UpdatedAt` `ModifiedDate` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP ,
ADD INDEX `fk_ReleaseUpdates_ApplicationId_idx` (`ApplicationID` ASC) VISIBLE,
ADD INDEX `fk_ReleaseUpdates_CreatedBy_idx` (`CreatedBy` ASC) VISIBLE,
ADD INDEX `fk_ReleaseUpdates_ModifiedBy_idx` (`ModifiedBy` ASC) VISIBLE;
;
ALTER TABLE `Empirical_Prod`.`ReleaseUpdates`
ADD CONSTRAINT `fk_ReleaseUpdates_ApplicationId`
  FOREIGN KEY (`ApplicationID`)
  REFERENCES `Empirical_Prod`.`Application` (`ApplicationID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `fk_ReleaseUpdates_CreatedBy`
  FOREIGN KEY (`CreatedBy`)
  REFERENCES `Empirical_Prod`.`Entity` (`EntityID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `fk_ReleaseUpdates_ModifiedBy`
  FOREIGN KEY (`ModifiedBy`)
  REFERENCES `Empirical_Prod`.`Entity` (`EntityID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

-- create lookup table, this is not required for UAT and PROD

CREATE TABLE `Empirical_Prod`.`lookup` (
  `ID` INT NOT NULL AUTO_INCREMENT,
  `CategoryTypeID` ENUM('ConstructionType', 'DataType', 'DockDoorsType', 'SizeSource', 'Tenancy', 'GovernmentInterest', 'EncumbrancesType', 'MailMessageStatus', 'SprinklerType', 'HVACType', 'EnergyStarRating', 'WaterStarRating', 'GreenStarRating', 'AmenitiesType', 'BuildSpecStatus', 'RoofType', 'PowerTypes', 'LandUse', 'ZoningClass', 'Prefix', 'Suffix', 'Quadrant', 'RecordType', 'ShareLevel', 'AgreementType', 'ListingType', 'SaleType', 'PossessionType', 'MediaType', 'MediaSubType', 'MediaSource', 'RoofTopSource', 'AddressType', 'ArmsLengthType', 'BuyerContactType', 'BuyerType', 'CapRateType', 'CashFlowType', 'ContactType', 'EscalatioType', 'ExpirationDateMethods', 'EarthquakeZone', 'AvailableSpaceSFMethods', 'CleanRoomClass', 'Conveyed', 'DateOccupiedMethods', 'EnergyProvider', 'FaceRentPerSqmMethods', 'Features', 'FeedFormats', 'FinanceType', 'HideReasons', 'IncomeClass', 'IncomeSource', 'InstrumentDocType', 'LeaseContactType', 'LeaseExtensionType', 'LeaseIncomeClass', 'LeaseResearchStatus', 'LeaseReviewType', 'LeaseStatus', 'MarketBriefStatus', 'MediaRelationType', 'NOIMethod', 'NOISource', 'NOIType', 'OfficeHvacType', 'OtherRelatioshipType', 'PhoneType', 'PriceConfirmationStatus', 'ReportTemplateStatus', 'ResearchStatus', 'ReviewTypes', 'RightsIncludedType', 'RoadAccessType', 'SalePriceSource', 'SaleMethod', 'SellerType', 'SizeType', 'SuiteType', 'TruckwellDoorsType', 'TransactionOriginationType', 'TenantVerificationSource', 'TenantStatus', 'Vacancy', 'Role', 'EscalationType', 'LeedStatus', 'CondoType', 'Unit', 'RatingTier', 'FloorStatus', 'TenantResearchStatus', 'CompanyTier') NOT NULL,
  `LookUpTypeID` INT NOT NULL,
  `LookUpTypeName` VARCHAR(45) NULL DEFAULT NULL,
  `IsActive` TINYINT(1) NULL DEFAULT 1,
  `IsRetired` TINYINT(0) NULL DEFAULT NULL,
  `Abbr` VARCHAR(45) NULL DEFAULT NULL,
  `DisplayText` VARCHAR(45) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`));

spring.application.name=apicore

# Enable JPA Logging
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=none

# Database Configuration
spring.datasource.empirical-prod.jdbc-url=${SPRING_DATASOURCE_URL}
spring.datasource.empirical-prod.username=${SPRING_DATASOURCE_USERNAME}
spring.datasource.empirical-prod.password=${SPRING_DATASOURCE_PASSWORD}
spring.datasource.empirical-prod.driver-class-name=com.mysql.cj.jdbc.Driver

spring.datasource.empirical-gis.jdbc-url=*************************************************************************************************************************************
spring.datasource.empirical-gis.username=dbuser-arealytics-app
spring.datasource.empirical-gis.password=DBUserArealyticsAapp#622
spring.datasource.empirical-gis.driver-class-name=com.mysql.cj.jdbc.Driver

#Jwt
JWT_SECRET_KEY = *819C59DB21E24A4C21B974DD26D0B86176B2B938
EXPIRY_TIME=6h
JWT_ENABLE_IP_VALIDATION=false

spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

# S3 bucket Configuration
aws.s3.bucket.name=empiricalcres3
aws.s3.bucket.region=us-east-1
aws.s3.access-key-id=********************
aws.s3.secret-access-key=jaB1KhbI5MWVGYAiZVvTjvSPE2m+EcD9s8jksGUJ
aws.s3.acl=public-read
aws.s3.path=EmpiricalCRE/PHOENIX
aws.s3.upload-max-size=50mb
aws.s3.base=https://media.empiricalcre.com
aws.s3.image.market-brief=/MarketBrief/Media/

flyway.transaction.location=classpath:db/migration/TransactionSchema
flyway.gis.location=classpath:db/migration/GISSchema
flyway.baseline=2

# Redis Configuration
redis.host=localhost
redis.port=6379
redis.password=Ph0en!x@Red!$2305
spring.data.redis.repositories.enabled=false

#CORS Configration
cors.allowed-origins=http://localhost:4200,http://localhost:4400,http://localhost:4500,http://localhost:8080,https://*.arealytics.com.au

#swagger details
swagger.server.url=https://api-phoenix-dev.arealytics.com.au
swagger.server.description=Development Server

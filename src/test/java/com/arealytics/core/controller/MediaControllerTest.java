package com.arealytics.core.controller;

import com.arealytics.core.dto.request.MediaDeleteRequestDTO;
import com.arealytics.core.dto.request.MediaRequestDTO;
import com.arealytics.core.dto.response.ApiResponse;
import com.arealytics.core.dto.response.MediaDTO;
import com.arealytics.core.enumeration.MediaRelationType;
import com.arealytics.core.enumeration.MediaSource;
import com.arealytics.core.enumeration.MediaSubType;
import com.arealytics.core.enumeration.MediaType;
import com.arealytics.core.service.MediaService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.any;

import java.util.Arrays;
import java.util.List;

import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import jakarta.validation.constraints.Null;

@ExtendWith(MockitoExtension.class)
class MediaControllerTest {

  @Mock
  private MediaService mediaService;

  @InjectMocks
  private MediaController mediaController;

  private MediaRequestDTO mediaRequestDTO;

  private MediaDTO mediaDTO;

  @BeforeEach
  void setUp() {
    mediaRequestDTO = new MediaRequestDTO();
    // mediaRequestDTO.setId(1);
    mediaRequestDTO.setMediaName("property_front_view.jpg");
    mediaRequestDTO.setPath("/media/images/property123/front.jpg");
    mediaRequestDTO.setDefault(true);
    mediaRequestDTO.setOwnMedia(false);
    mediaRequestDTO.setDescription("Front view of the property");
    mediaRequestDTO.setExt("jpg");
    mediaRequestDTO.setHeight(1080);
    mediaRequestDTO.setWidth(1920);
    mediaRequestDTO.setSize(2048);
    mediaRequestDTO.setMediaName("property_front_view");
    mediaRequestDTO.setMediaRelationTypeId(MediaRelationType.PROPERTY);
    mediaRequestDTO.setMediaSourceId(MediaSource.BROKERS_PDF);
    mediaRequestDTO.setMediaSubTypeId(MediaSubType.MAIN_PHOTO);
    mediaRequestDTO.setMediaTypeId(MediaType.LISTING_SIGN);
    mediaRequestDTO.setSourceComments("Taken during site visit");
    mediaRequestDTO.setRelationId(113512);
    mediaRequestDTO.setPropertyId(113512);
  }

  @Test
  void getMediaByRelationId_ShouldReturnMediaList() {
    // Arrange
    List<MediaDTO> mediaList = Arrays.asList(mediaDTO);
    when(mediaService.getMediaByRelationId(MediaRelationType.PROPERTY, 113512)).thenReturn(mediaList);

    // Act
    ResponseEntity<ApiResponse<List<MediaDTO>>> response = mediaController
        .getMediaByRelationId(MediaRelationType.PROPERTY, 113512);

    // Assert
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertNotNull(response.getBody());
    assertEquals("Success", response.getBody().getMessage());
    // assertEquals(1, response.getBody().getResponseData().getFirst().getSize());
    verify(mediaService, times(1)).getMediaByRelationId(MediaRelationType.PROPERTY, 113512);
  }

  @Test
  void createMedia_ShouldReturnCreatedMedia() {
    // Arrange
    MediaRequestDTO requestDTO = new MediaRequestDTO();
    mediaRequestDTO = new MediaRequestDTO();
    // mediaRequestDTO.setId(1);
    mediaRequestDTO.setMediaName("property_front_view.jpg");
    mediaRequestDTO.setPath("/media/images/property123/front.jpg");
    mediaRequestDTO.setDefault(true);
    mediaRequestDTO.setOwnMedia(false);
    mediaRequestDTO.setDescription("Front view of the property");
    mediaRequestDTO.setExt("jpg");
    mediaRequestDTO.setHeight(1080);
    mediaRequestDTO.setWidth(1920);
    mediaRequestDTO.setSize(2048);
    mediaRequestDTO.setMediaName("property_front_view");
    mediaRequestDTO.setMediaRelationTypeId(MediaRelationType.PROPERTY);
    mediaRequestDTO.setMediaSourceId(MediaSource.BROKERS_PDF);
    mediaRequestDTO.setMediaSubTypeId(MediaSubType.MAIN_PHOTO);
    mediaRequestDTO.setMediaTypeId(MediaType.LISTING_SIGN);
    mediaRequestDTO.setSourceComments("Taken during site visit");
    mediaRequestDTO.setRelationId(113512);
    mediaRequestDTO.setPropertyId(113512);

    when(mediaService.createMedia(any(MediaRequestDTO.class))).thenReturn(mediaDTO);

    // Act
    ResponseEntity<ApiResponse<MediaDTO>> response = mediaController.createMedia(requestDTO);

    // Assert
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertNotNull(response.getBody());
    assertEquals("Success", response.getBody().getMessage());
    verify(mediaService, times(1)).createMedia(any(MediaRequestDTO.class));
  }

  @Test
  void updateMedia_ShouldReturnUpdatedMedia() {
    // Arrange
    MediaRequestDTO requestDTO = new MediaRequestDTO();
    requestDTO.setMediaName("updated.jpg");
    requestDTO.setMediaRelationTypeId(MediaRelationType.PROPERTY);
    requestDTO.setRelationId(113512);

    when(mediaService.updateMedia(requestDTO, 1)).thenReturn(mediaDTO);

    // Act
    ResponseEntity<ApiResponse<MediaDTO>> response = mediaController.updateMedia(1, requestDTO);

    // Assert
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertNotNull(response.getBody());
    assertEquals("Success", response.getBody().getMessage());
    verify(mediaService, times(1)).updateMedia(requestDTO, 1);
  }

  @Test
  void testDeleteMedia() {
    Integer mediaRelationshipId = 1288566;

    MediaDeleteRequestDTO mediaDeleteRequestDTO = new MediaDeleteRequestDTO();
    mediaDeleteRequestDTO.setMediaRelationshipId(mediaRelationshipId);
    mediaDeleteRequestDTO.setRelationId(113512);
    mediaDeleteRequestDTO.setMediaRelationTypeId(MediaRelationType.PROPERTY);

    // No need to mock return since deleteMedia is void
    doNothing().when(mediaService).deleteMedia(mediaDeleteRequestDTO);

    ResponseEntity<ApiResponse<Null>> response = mediaController.deleteMedia(mediaDeleteRequestDTO);

    // Assertions
    assertNotNull(response);
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertEquals("Success", response.getBody().getMessage());
    assertEquals(HttpStatus.OK.value(), response.getBody().getStatus());
    assertNull(response.getBody().getResponseData());

    // Verify the service was called once
    verify(mediaService, times(1)).deleteMedia(mediaDeleteRequestDTO);
  }
}

package com.arealytics.core.constants;

import java.util.List;

public final class SqlClauseConstants {
    private SqlClauseConstants() {
        // Prevent instantiation
    }

    public static final List<String> PROPERTY_DETAILS_EXPECTED_CLAUSES = List.of(
            "from Property",
            "join address.location",
            "join address.city",
            "join address.state",
            "left join PropertyStrataRelationship",
            "property.genuses as use",
            "left join Market",
            "left join SubMarket",
            "left join Metro",
            "left join SpecificUses specificUses",
            "left join County",
            "left join Country",
            "left join MediaRelationship",
            "left join Media",
            "left join EntityModel",
            "left join Person",
            "left join EntityModel modifiedEntity",
            "left join Person modifiedPerson",
            "left join EntityModel lastReviewedEntity",
            "left join Person lastReviewedPerson",
            "where property.propertyID"
    );
}

package com.arealytics.core.service;

import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.domain.empiricalProd.PropertyDetails;
import com.arealytics.core.domain.empiricalProd.SpecificUses;
import com.arealytics.core.domain.empiricalProd.Use;
import com.arealytics.core.domain.empiricalProd.UseType;
import com.arealytics.core.dto.response.PropertyDetailsDTO;
import com.arealytics.core.enumeration.ParentTable;
import com.arealytics.core.repository.empiricalProd.SpecificUsesRepository;
import com.arealytics.core.repository.empiricalProd.UseRepository;
import com.arealytics.core.repository.empiricalProd.UseTypeRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PropertyUseServiceTest {

    @Mock
    private UseRepository useRepository;

    @Mock
    private UseTypeRepository useTypeRepository;

    @Mock
    private SpecificUsesRepository specificUsesRepository;

    @InjectMocks
    private PropertyUseService propertyUseService;

    private PropertyDetailsDTO propertyDTO;
    private Property property;
    private PropertyDetails propertyDetails;
    private UseType useType;
    private SpecificUses specificUses;

    @BeforeEach
    void setUp() {
        propertyDTO = new PropertyDetailsDTO();
        propertyDTO.setUseTypeID(1);
        propertyDTO.setSpecificUseID(2);

        propertyDetails = new PropertyDetails();
        property = new Property();
        property.setPropertyDetails(propertyDetails);

        useType = new UseType();
        useType.setUseTypeId(1);
        useType.setUseTypeName("INDUSTRIAL");

        specificUses = new SpecificUses();
        specificUses.setSpecificUsesId(2);
        specificUses.setSpecificUsesName("OFFICE");
    }

    @Test
    void setUseDetailsForCreate_successfullySetsUseDetails() {
        // Arrange
        when(useTypeRepository.findById(1)).thenReturn(Optional.of(useType));
        when(specificUsesRepository.findById(2)).thenReturn(Optional.of(specificUses));

        // Act
        propertyUseService.setUseDetailsForCreate(propertyDTO, property);

        // Assert
        assertEquals(1, property.getPropertyDetails().getUseTypeID());
        assertEquals("INDUSTRIAL", property.getPropertyDetails().getUseTypeName());
        assertEquals(2, property.getPropertyDetails().getSpecificUseID());
        assertEquals("OFFICE", property.getPropertyDetails().getSpecificUseName());
        verify(useTypeRepository).findById(1);
        verify(specificUsesRepository).findById(2);
    }

    @Test
    void setUseDetailsForCreate_skipsWhenUseTypeIdIsNull() {
        // Arrange
        propertyDTO.setUseTypeID(null);

        // Act
        propertyUseService.setUseDetailsForCreate(propertyDTO, property);

        // Assert
        assertNull(property.getPropertyDetails().getUseTypeID());
        assertNull(property.getPropertyDetails().getUseTypeName());
        assertNull(property.getPropertyDetails().getSpecificUseID());
        assertNull(property.getPropertyDetails().getSpecificUseName());
        verify(useTypeRepository, never()).findById(any());
        verify(specificUsesRepository, never()).findById(any());
    }

    @Test
    void setUseDetailsForCreate_throwsExceptionWhenUseTypeNotFound() {
        // Arrange
        when(useTypeRepository.findById(1)).thenReturn(Optional.empty());

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                propertyUseService.setUseDetailsForCreate(propertyDTO, property));
        assertEquals("UseType not found with ID: 1", exception.getMessage());
        verify(useTypeRepository).findById(1);
        verify(specificUsesRepository, never()).findById(any());
    }

    @Test
    void setUseDetailsForCreate_throwsExceptionWhenSpecificUseNotFound() {
        // Arrange
        when(useTypeRepository.findById(1)).thenReturn(Optional.of(useType));
        when(specificUsesRepository.findById(2)).thenReturn(Optional.empty());

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                propertyUseService.setUseDetailsForCreate(propertyDTO, property));
        assertEquals("SpecificUses not found with ID: 2", exception.getMessage());
        verify(useTypeRepository).findById(1);
        verify(specificUsesRepository).findById(2);
    }

    @Test
    void setUseDetailsForUpdate_successfullyUpdatesUseDetails() {
        // Arrange
        Property existingProperty = new Property();
        PropertyDetails existingDetails = new PropertyDetails();
        existingProperty.setPropertyDetails(existingDetails);
        when(useTypeRepository.findById(1)).thenReturn(Optional.of(useType));
        when(specificUsesRepository.findById(2)).thenReturn(Optional.of(specificUses));

        // Act
        propertyUseService.setUseDetailsForUpdate(propertyDTO, existingProperty, property);

        // Assert
        assertEquals(1, property.getPropertyDetails().getUseTypeID());
        assertEquals("INDUSTRIAL", property.getPropertyDetails().getUseTypeName());
        assertEquals(2, property.getPropertyDetails().getSpecificUseID());
        assertEquals("OFFICE", property.getPropertyDetails().getSpecificUseName());
        verify(useTypeRepository).findById(1);
        verify(specificUsesRepository).findById(2);
    }

    @Test
    void setUseDetailsForUpdate_retainsExistingDetailsWhenIdsNotProvided() {
        // Arrange
        Property existingProperty = new Property();
        PropertyDetails existingDetails = new PropertyDetails();
        existingDetails.setUseTypeID(3);
        existingDetails.setUseTypeName("SPECIAL_USE");
        existingDetails.setSpecificUseID(4);
        existingDetails.setSpecificUseName("APARTMENTS");
        existingProperty.setPropertyDetails(existingDetails);

        propertyDTO.setUseTypeID(null);
        propertyDTO.setSpecificUseID(null);

        // Act
        propertyUseService.setUseDetailsForUpdate(propertyDTO, existingProperty, property);

        // Assert
        assertEquals(3, property.getPropertyDetails().getUseTypeID());
        assertEquals("SPECIAL_USE", property.getPropertyDetails().getUseTypeName());
        assertEquals(4, property.getPropertyDetails().getSpecificUseID());
        assertEquals("APARTMENTS", property.getPropertyDetails().getSpecificUseName());
        verify(useTypeRepository, never()).findById(any());
        verify(specificUsesRepository, never()).findById(any());
    }

    @Test
    void setUseDetailsForUpdate_throwsExceptionWhenUseTypeNotFound() {
        // Arrange
        Property existingProperty = new Property();
        existingProperty.setPropertyDetails(new PropertyDetails());
        when(useTypeRepository.findById(1)).thenReturn(Optional.empty());

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                propertyUseService.setUseDetailsForUpdate(propertyDTO, existingProperty, property));
        assertEquals("UseType not found with ID: 1", exception.getMessage());
        verify(useTypeRepository).findById(1);
        verify(specificUsesRepository, never()).findById(any());
    }

    @Test
    void saveUseRecord_createsNewUseRecordWhenNoneExists() {
        // Arrange
        when(useRepository.findByParentTableIdAndPropertyAndSequence(any(), any(), anyInt()))
                .thenReturn(Optional.empty());
        when(useTypeRepository.findById(1)).thenReturn(Optional.of(useType));
        when(specificUsesRepository.findById(2)).thenReturn(Optional.of(specificUses));
        when(useRepository.save(any(Use.class))).thenReturn(new Use());

        // Act
        propertyUseService.saveUseRecord(propertyDTO, property);

        // Assert
        verify(useRepository).findByParentTableIdAndPropertyAndSequence(ParentTable.Property, property, 1);
        verify(useTypeRepository).findById(1);
        verify(specificUsesRepository).findById(2);
        verify(useRepository).save(any(Use.class));
    }

    @Test
    void saveUseRecord_updatesExistingUseRecord() {
        // Arrange
        Use existingUse = new Use();
        when(useRepository.findByParentTableIdAndPropertyAndSequence(any(), any(), anyInt()))
                .thenReturn(Optional.of(existingUse));
        when(useTypeRepository.findById(1)).thenReturn(Optional.of(useType));
        when(specificUsesRepository.findById(2)).thenReturn(Optional.of(specificUses));
        when(useRepository.save(any(Use.class))).thenReturn(existingUse);

        // Act
        propertyUseService.saveUseRecord(propertyDTO, property);

        // Assert
        verify(useRepository).findByParentTableIdAndPropertyAndSequence(ParentTable.Property, property, 1);
        verify(useTypeRepository).findById(1);
        verify(specificUsesRepository).findById(2);
        verify(useRepository).save(existingUse);
        assertEquals(useType, existingUse.getUseType());
        assertEquals(specificUses, existingUse.getSpecificUses());
        assertTrue(existingUse.getIsActive());
        assertEquals(1, existingUse.getSequence());
        assertEquals(ParentTable.Property, existingUse.getParentTableId());
    }

    @Test
    void saveUseRecord_skipsWhenUseTypeIdIsNull() {
        // Arrange
        propertyDTO.setUseTypeID(null);

        // Act
        propertyUseService.saveUseRecord(propertyDTO, property);

        // Assert
        verify(useRepository, never()).findByParentTableIdAndPropertyAndSequence(any(), any(), anyInt());
        verify(useTypeRepository, never()).findById(any());
        verify(specificUsesRepository, never()).findById(any());
        verify(useRepository, never()).save(any());
    }

    @Test
    void saveUseRecord_setsSpecificUsesToNullWhenSpecificUseIdIsNull() {
        // Arrange
        propertyDTO.setSpecificUseID(null);
        when(useRepository.findByParentTableIdAndPropertyAndSequence(any(), any(), anyInt()))
                .thenReturn(Optional.empty());
        when(useTypeRepository.findById(1)).thenReturn(Optional.of(useType));
        when(useRepository.save(any(Use.class))).thenReturn(new Use());

        // Act
        propertyUseService.saveUseRecord(propertyDTO, property);

        // Assert
        verify(useRepository).findByParentTableIdAndPropertyAndSequence(ParentTable.Property, property, 1);
        verify(useTypeRepository).findById(1);
        verify(specificUsesRepository, never()).findById(any());
        verify(useRepository).save(argThat(use -> use.getSpecificUses() == null));
    }

    @Test
    void saveUseRecord_throwsExceptionWhenUseTypeNotFound() {
        // Arrange
        when(useRepository.findByParentTableIdAndPropertyAndSequence(any(), any(), anyInt()))
                .thenReturn(Optional.empty());
        when(useTypeRepository.findById(1)).thenReturn(Optional.empty());

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                propertyUseService.saveUseRecord(propertyDTO, property));
        assertEquals("UseType not found with ID: 1", exception.getMessage());
        verify(useRepository).findByParentTableIdAndPropertyAndSequence(ParentTable.Property, property, 1);
        verify(useTypeRepository).findById(1);
        verify(specificUsesRepository, never()).findById(any());
        verify(useRepository, never()).save(any());
    }
}

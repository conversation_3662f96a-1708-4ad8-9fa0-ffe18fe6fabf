package com.arealytics.core.service;

import com.arealytics.core.domain.empiricalProd.Location;
import com.arealytics.core.dto.response.LocationDTO;
import com.arealytics.core.dto.response.PropertyDetailsDTO;
import com.arealytics.core.mapper.PropertyMapper;
import com.arealytics.core.repository.empiricalProd.LocationRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LocationServiceTest {

    @Mock
    private LocationRepository locationRepository;

    @Mock
    private PropertyMapper propertyMapper;

    @InjectMocks
    private LocationService locationService;

    private PropertyDetailsDTO propertyDTO;
    private LocationDTO locationDTO;
    private Location location;
    private Integer existingLocationId = 1;
    private Integer propertyId = 100;

    @BeforeEach
    void setUp() {
        locationDTO = new LocationDTO();
        locationDTO.setLatitude(new BigDecimal("40.7128"));
        locationDTO.setLongitude(new BigDecimal("-74.0060"));
        locationDTO.setZCoordinate(new BigDecimal("10.0"));
        locationDTO.setGisShapeID(123);

        propertyDTO = new PropertyDetailsDTO();
        propertyDTO.setLocationFields(locationDTO);
        propertyDTO.setPropertyID(propertyId);

        location = new Location();
        location.setLatitude(new BigDecimal("40.7128"));
        location.setLongitude(new BigDecimal("-74.0060"));
        location.setZCoordinate(new BigDecimal("10.0"));
        location.setGisShapeID(123);
    }

    @Test
    void createLocation_successfullyCreatesAndSavesLocation() {
        when(propertyMapper.toLocationEntity(any(PropertyDetailsDTO.class))).thenReturn(location);
        when(locationRepository.save(any(Location.class))).thenReturn(location);

        Location result = locationService.createLocation(propertyDTO);

        assertNotNull(result);
        assertEquals(location.getLatitude(), result.getLatitude());
        assertEquals(location.getLongitude(), result.getLongitude());
        verify(propertyMapper).toLocationEntity(propertyDTO);
        verify(locationRepository).save(any(Location.class));
        assertNotNull(result.getLocationPoint());
        assertEquals(-74.0060, result.getLocationPoint().getX(), 0.0001);
        assertEquals(40.7128, result.getLocationPoint().getY(), 0.0001);
    }

    @Test
    void createLocation_throwsExceptionWhenLatitudeIsNull() {
        locationDTO.setLatitude(null);
        propertyDTO.setLocationFields(locationDTO);

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                locationService.createLocation(propertyDTO));
        assertEquals("Latitude and longitude must not be null for location creation", exception.getMessage());
        verify(propertyMapper, never()).toLocationEntity(any());
        verify(locationRepository, never()).save(any());
    }

    @Test
    void createLocation_throwsExceptionWhenLongitudeIsNull() {
        locationDTO.setLongitude(null);
        propertyDTO.setLocationFields(locationDTO);

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                locationService.createLocation(propertyDTO));
        assertEquals("Latitude and longitude must not be null for location creation", exception.getMessage());
        verify(propertyMapper, never()).toLocationEntity(any());
        verify(locationRepository, never()).save(any());
    }

    @Test
    void updateLocation_successfullyUpdatesExistingLocation() {
        // Arrange
        Location updatedLocation = new Location();
        updatedLocation.setLatitude(new BigDecimal("41.0000"));
        updatedLocation.setLongitude(new BigDecimal("-75.0000"));
        updatedLocation.setZCoordinate(new BigDecimal("20.0"));
        updatedLocation.setGisShapeID(456);

        // Update propertyDTO to match updatedLocation coordinates
        LocationDTO updatedLocationDTO = new LocationDTO();
        updatedLocationDTO.setLatitude(new BigDecimal("41.0000"));
        updatedLocationDTO.setLongitude(new BigDecimal("-75.0000"));
        updatedLocationDTO.setZCoordinate(new BigDecimal("20.0"));
        updatedLocationDTO.setGisShapeID(456);
        propertyDTO.setLocationFields(updatedLocationDTO);

        when(locationRepository.findById(existingLocationId)).thenReturn(Optional.of(location));
        when(propertyMapper.toLocationEntity(any(PropertyDetailsDTO.class))).thenReturn(updatedLocation);
        when(locationRepository.save(any(Location.class))).thenReturn(location);

        // Act
        Location result = locationService.updateLocation(propertyDTO, existingLocationId);

        // Assert
        assertNotNull(result);
        assertEquals(new BigDecimal("41.0000"), result.getLatitude());
        assertEquals(new BigDecimal("-75.0000"), result.getLongitude());
        assertEquals(new BigDecimal("20.0"), result.getZCoordinate());
        assertEquals(456, result.getGisShapeID());
        verify(locationRepository).findById(existingLocationId);
        verify(propertyMapper).toLocationEntity(propertyDTO);
        verify(locationRepository).save(any(Location.class));
        assertNotNull(result.getLocationPoint());
        assertEquals(-75.0000, result.getLocationPoint().getX(), 0.0001);
        assertEquals(41.0000, result.getLocationPoint().getY(), 0.0001);
    }

    @Test
    void updateLocation_throwsExceptionWhenLocationNotFound() {
        when(locationRepository.findById(existingLocationId)).thenReturn(Optional.empty());

        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                locationService.updateLocation(propertyDTO, existingLocationId));
        assertEquals("Location not found with ID: " + existingLocationId, exception.getMessage());
        verify(locationRepository).findById(existingLocationId);
        verify(propertyMapper, never()).toLocationEntity(any());
        verify(locationRepository, never()).save(any());
    }

    @Test
    void updateLocation_throwsExceptionWhenExistingLocationIdIsNullAndPropertyIdIsNotNull() {
        propertyDTO.setPropertyID(propertyId);

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                locationService.updateLocation(propertyDTO, null));
        assertEquals("Existing location ID must not be null for update.", exception.getMessage());
        verify(locationRepository, never()).save(any());
        verify(propertyMapper, never()).toLocationEntity(any());
    }

    @Test
    void updateLocation_createsNewLocationWhenExistingLocationIdIsNull() {
        propertyDTO.setPropertyID(null);
        when(propertyMapper.toLocationEntity(any(PropertyDetailsDTO.class))).thenReturn(location);
        when(locationRepository.save(any(Location.class))).thenReturn(location);

        Location result = locationService.updateLocation(propertyDTO, null);

        assertNotNull(result);
        assertEquals(location.getLatitude(), result.getLatitude());
        assertEquals(location.getLongitude(), result.getLongitude());
        verify(propertyMapper).toLocationEntity(propertyDTO);
        verify(locationRepository).save(any(Location.class));
        assertNotNull(result.getLocationPoint());
        assertEquals(-74.0060, result.getLocationPoint().getX(), 0.0001);
        assertEquals(40.7128, result.getLocationPoint().getY(), 0.0001);
    }

    @Test
    void updateLocation_skipsLocationPointUpdateWhenCoordinatesAreNull() {
        locationDTO.setLatitude(null);
        locationDTO.setLongitude(null);
        propertyDTO.setLocationFields(locationDTO);
        when(locationRepository.findById(existingLocationId)).thenReturn(Optional.of(location));
        when(propertyMapper.toLocationEntity(any(PropertyDetailsDTO.class))).thenReturn(location);
        when(locationRepository.save(any(Location.class))).thenReturn(location);

        Location result = locationService.updateLocation(propertyDTO, existingLocationId);

        assertNotNull(result);
        verify(locationRepository).findById(existingLocationId);
        verify(propertyMapper).toLocationEntity(propertyDTO);
        verify(locationRepository).save(any(Location.class));
        assertNull(result.getLocationPoint());
    }
}
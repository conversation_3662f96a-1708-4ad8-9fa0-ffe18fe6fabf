package com.arealytics.core.service;

import com.arealytics.core.domain.empiricalProd.Address;
import com.arealytics.core.dto.response.PropertyDetailsDTO;
import com.arealytics.core.mapper.AddressMapper;
import com.arealytics.core.repository.empiricalProd.AddressRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AddressServiceTest {

    @Mock
    private AddressRepository addressRepository;

    @Mock
    private AddressMapper addressMapper;

    @InjectMocks
    private AddressService addressService;

    private PropertyDetailsDTO propertyDTO;
    private Address address;
    private Integer locationId = 113389;
    private Integer propertyId = 154200;

    @BeforeEach
    void setUp() {
        propertyDTO = new PropertyDetailsDTO();
        address = new Address();
    }

    @Test
    void createAddress_successfullyCreatesAndSavesAddress() {
        // Arrange
        when(addressMapper.toAddress(any(PropertyDetailsDTO.class), anyInt(), anyInt()))
                .thenReturn(address);
        when(addressRepository.save(any(Address.class))).thenReturn(address);

        // Act
        Address result = addressService.createAddress(propertyDTO, locationId, propertyId);

        // Assert
        assertNotNull(result);
        verify(addressMapper).toAddress(propertyDTO, locationId, propertyId);
        verify(addressRepository).save(address);
    }

    @Test
    void updateAddress_successfullyUpdatesExistingAddress() {
        // Arrange
        when(addressRepository.findFirstByParentIdAndIsActiveTrueAndSequenceOrderByAddressIdDesc(propertyId, 1))
                .thenReturn(Optional.of(address));
        when(addressMapper.updateAddressFromDto(any(PropertyDetailsDTO.class), any(Address.class)))
                .thenReturn(address);
        when(addressRepository.save(any(Address.class))).thenReturn(address);

        // Act
        Address result = addressService.updateAddress(propertyDTO, locationId, propertyId);

        // Assert
        assertNotNull(result);
        verify(addressRepository).findFirstByParentIdAndIsActiveTrueAndSequenceOrderByAddressIdDesc(propertyId, 1);
        verify(addressMapper).updateAddressFromDto(propertyDTO, address);
        verify(addressRepository).save(address);
    }

    @Test
    void updateAddress_throwsExceptionWhenNoActiveAddressFound() {
        // Arrange
        when(addressRepository.findFirstByParentIdAndIsActiveTrueAndSequenceOrderByAddressIdDesc(propertyId, 1))
                .thenReturn(Optional.empty());

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                addressService.updateAddress(propertyDTO, locationId, propertyId));
        assertEquals("No active address found for property: " + propertyId, exception.getMessage());
        verify(addressRepository).findFirstByParentIdAndIsActiveTrueAndSequenceOrderByAddressIdDesc(propertyId, 1);
        verify(addressMapper, never()).updateAddressFromDto(any(), any());
        verify(addressRepository, never()).save(any());
    }

    @Test
    void findByParentIdAndIsActiveTrue_returnsAddressWhenFound() {
        // Arrange
        when(addressRepository.findFirstByParentIdAndIsActiveTrueAndSequenceOrderByAddressIdDesc(propertyId, 1))
                .thenReturn(Optional.of(address));

        // Act
        Optional<Address> result = addressService.findByParentIdAndIsActiveTrue(propertyId);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(address, result.get());
        verify(addressRepository).findFirstByParentIdAndIsActiveTrueAndSequenceOrderByAddressIdDesc(propertyId, 1);
    }

    @Test
    void findByParentIdAndIsActiveTrue_returnsEmptyWhenNotFound() {
        // Arrange
        when(addressRepository.findFirstByParentIdAndIsActiveTrueAndSequenceOrderByAddressIdDesc(propertyId, 1))
                .thenReturn(Optional.empty());

        // Act
        Optional<Address> result = addressService.findByParentIdAndIsActiveTrue(propertyId);

        // Assert
        assertTrue(result.isEmpty());
        verify(addressRepository).findFirstByParentIdAndIsActiveTrueAndSequenceOrderByAddressIdDesc(propertyId, 1);
    }
}

package com.arealytics.core.common.querydsl.join;

import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.enumeration.MediaRelationType;
import com.arealytics.core.enumeration.ParentTable;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class JoinConditionsTest {


    @Test
    void joinAddressOnPropertyPrimaryAddress() {
        QAddress address = QAddress.address;
        QProperty property = QProperty.property;

        BooleanExpression expression = JoinConditions.joinAddressOnPropertyPrimaryAddress(address, property);
        String expressionStr = expression.toString();
        assertNotNull(expression);
        assertTrue(expressionStr.contains("address.parentTableId = " + ParentTable.Property), "Expected expression to contain enum value 'Property'");
        assertTrue(expressionStr.contains("address.isActive = true"), "Expected expression to contain 'isActive = true'");
        assertTrue(expressionStr.contains("address.sequence = 1"), "Expected expression to contain 'sequence = 1'");
    }

    @Test
    void joinUseOnPropertyPrimaryUse() {
        QUse use = QUse.use;
        QProperty property = QProperty.property;

        BooleanExpression expression = JoinConditions.joinUseOnPropertyPrimaryUse(use);
        String expressionStr = expression.toString();

        assertNotNull(expression);
        assertTrue(expressionStr.contains("use.parentTableId = " + ParentTable.Property), "Expected expression to contain enum value 'Property'");
        assertTrue(expressionStr.contains("use.sequence = 1"), "Expected expression to contain 'sequence = 1'");
    }


    @Test
    void joinMarketOnProperty() {
        QMarket market = QMarket.market;
        QProperty property = QProperty.property;

        BooleanExpression expression = JoinConditions.JoinMarketOnProperty(market, property);
        String expressionStr = expression.toString();
        assertNotNull(expression);
        assertTrue(expressionStr.contains("market.marketId = property.propertyLocation.marketId"),
                "Expected expression to contain 'market.marketId = property.propertyLocation.marketId'");
    }

    @Test
    void joinSubMarketOnProperty() {
        QSubMarket subMarket = QSubMarket.subMarket;
        QProperty property = QProperty.property;

        BooleanExpression expression = JoinConditions.JoinSubMarketOnProperty(subMarket, property);
        String expressionStr = expression.toString();

        assertNotNull(expression);
        assertTrue(expressionStr.contains("subMarket.subMarketId = property.propertyLocation.subMarketID"),
                "Expected expression to contain 'subMarket.subMarketId = property.propertyLocation.subMarketID'");
    }

    @Test
    void joinMetroOnMarket() {
        QMetro metro = QMetro.metro;
        QMarket market = QMarket.market;

        BooleanExpression expression = JoinConditions.JoinMetroOnMarket(metro, market);
        String expressionStr = expression.toString();

        assertNotNull(expression);
        assertTrue(expressionStr.contains("metro.metroId = market.metroId"),
                "Expected expression to contain 'metro.metroId = market.metroId'");
    }

    @Test
    void joinSpecificUsesOnUse() {
        QSpecificUses specificUses = QSpecificUses.specificUses;
        QUse use = QUse.use;

        BooleanExpression expression = JoinConditions.JoinSpecificUsesOnUse(specificUses, use);
        String expressionStr = expression.toString();

        assertNotNull(expression);
        assertTrue(expressionStr.contains("specificUses.specificUsesId = use.SpecificUsesID"),
                "Expected expression to contain 'specificUses.specificUsesId = use.SpecificUsesID'");
    }

    @Test
    void joinCountyOnAddress() {
        QCounty county = QCounty.county;
        QAddress address = QAddress.address;

        BooleanExpression expression = JoinConditions.JoinCountyOnAddress(county, address);
        String expressionStr = expression.toString();

        assertNotNull(expression);
        assertTrue(expressionStr.contains("county.countyId = address.countyId"),
                "Expected expression to contain 'county.countyId = address.countyId'");
    }

    @Test
    void joinCountryOnAddress() {
        QCountry country = QCountry.country;
        QAddress address = QAddress.address;

        BooleanExpression expression = JoinConditions.JoinCountryOnAddress(country, address);
        String expressionStr = expression.toString();

        assertNotNull(expression);
        assertTrue(expressionStr.contains("country.countryId = address.countryId"),
                "Expected expression to contain 'country.countryId = address.countryId'");
    }

    @Test
    void joinMediaRelationshipOnProperty() {
        QMediaRelationship mediaRelationship = QMediaRelationship.mediaRelationship;
        QProperty property = QProperty.property;

        BooleanExpression expression = JoinConditions.JoinMediaRelationshipOnProperty(mediaRelationship, property);
        String expressionStr = expression.toString();

        assertNotNull(expression);
        assertTrue(expressionStr.contains("mediaRelationship.relationId = property.propertyID"),
                "Expected expression to contain 'mediaRelationship.relationId = property.propertyID'");
        assertTrue(expressionStr.contains("mediaRelationship.mediaRelationTypeId = " + MediaRelationType.PROPERTY),
                "Expected expression to contain 'mediaRelationTypeId = PROPERTY'");
        assertTrue(expressionStr.contains("mediaRelationship.isActive = true"),
                "Expected expression to contain 'isActive = true'");
        assertTrue(expressionStr.contains("mediaRelationship.isDefault = true"),
                "Expected expression to contain 'isDefault = true'");
    }

    @Test
    void joinMediaOnMediaRelationship() {
        QMedia media = QMedia.media;
        QMediaRelationship mediaRelationship = QMediaRelationship.mediaRelationship;

        BooleanExpression expression = JoinConditions.JoinMediaOnMediaRelationship(media, mediaRelationship);
        String expressionStr = expression.toString();

        assertNotNull(expression);
        assertTrue(expressionStr.contains("media.mediaId = mediaRelationship.mediaId"),
                "Expected expression to contain 'media.mediaId = mediaRelationship.mediaId'");
    }

    @Test
    void joinEntityModelOnPropertyModifiedBy() {
        QEntityModel entityModel = QEntityModel.entityModel;
        QProperty property = QProperty.property;

        BooleanExpression expression = JoinConditions.JoinEntityModelOnPropertyModifiedBy(entityModel, property);
        String expressionStr = expression.toString();

        assertNotNull(expression);
        assertTrue(expressionStr.contains("entityModel.entityId = property.modifiedBy.entityId"),
                "Expected expression to contain 'entityModel.entityId = property.modifiedBy.entityId'");
    }

    @Test
    void joinEntityModelOnPropertyCreatedBy() {
        QEntityModel entityModel = QEntityModel.entityModel;
        QProperty property = QProperty.property;

        BooleanExpression expression = JoinConditions.JoinEntityModelOnPropertyCreatedBy(entityModel, property);
        String expressionStr = expression.toString();

        assertNotNull(expression);
        assertTrue(expressionStr.contains("entityModel.entityId = property.createdBy.entityId"),
                "Expected expression to contain 'entityModel.entityId = property.createdBy.entityId'");
    }

    @Test
    void joinEntityModelOnPropertyLastReviewedBy() {
        QEntityModel entityModel = QEntityModel.entityModel;
        QProperty property = QProperty.property;

        BooleanExpression expression = JoinConditions.JoinEntityModelOnPropertyLastReviewedBy(entityModel, property);
        String expressionStr = expression.toString();

        assertNotNull(expression);
        assertTrue(expressionStr.contains("entityModel.entityId = property.propertyDetails.lastReviewedBy"),
                "Expected expression to contain 'entityModel.entityId = property.propertyDetails.lastReviewedBy'");
    }

    @Test
    void joinPersonOnEntityModel() {
        QPerson person = QPerson.person;
        QEntityModel entityModel = QEntityModel.entityModel;

        BooleanExpression expression = JoinConditions.JoinPersonOnEntityModel(person, entityModel);
        String expressionStr = expression.toString();

        assertNotNull(expression);
        assertTrue(expressionStr.contains("person.personId = entityModel.personId"),
                "Expected expression to contain 'person.personId = entityModel.personId'");
    }

    @Test
    void joinPropertyStrataRelationshipOnProperty() {
        QPropertyStrataRelationship propertyStrataRelationship = QPropertyStrataRelationship.propertyStrataRelationship;
        QProperty property = QProperty.property;

        BooleanExpression expression = JoinConditions.JoinPropertyStrataRelationshipOnProperty(propertyStrataRelationship, property);
        String expressionStr = expression.toString();

        assertNotNull(expression);
        assertTrue(expressionStr.contains("propertyStrataRelationship.strataPropertyId = property.propertyID"),
                "Expected expression to contain 'strataPropertyId = property.propertyID'");
        assertTrue(expressionStr.contains("propertyStrataRelationship.isActive = true"),
                "Expected expression to contain 'isActive = true'");
    }
}

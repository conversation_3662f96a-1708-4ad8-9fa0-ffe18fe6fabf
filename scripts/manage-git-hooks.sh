#!/bin/bash

# Git Hooks Management Script
# This script helps you manage Git hooks for the project

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show help
show_help() {
    echo "Git Hooks Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  install     Install/reinstall the pre-commit hook"
    echo "  uninstall   Remove the pre-commit hook"
    echo "  test        Test the pre-commit hook without committing"
    echo "  status      Show current hook status"
    echo "  config      Show current configuration"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 install    # Install the pre-commit hook"
    echo "  $0 test       # Test the hook"
    echo "  $0 status     # Check if hooks are installed"
}

# Function to install pre-commit hook
install_hook() {
    print_status "Installing pre-commit hook..."
    
    if [ -f ".git/hooks/pre-commit" ]; then
        print_warning "Pre-commit hook already exists. Backing up..."
        cp .git/hooks/pre-commit .git/hooks/pre-commit.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    # The hook should already be there, just make sure it's executable
    if [ -f ".git/hooks/pre-commit" ]; then
        chmod +x .git/hooks/pre-commit
        print_success "Pre-commit hook installed and made executable ✓"
    else
        print_error "Pre-commit hook file not found!"
        return 1
    fi
}

# Function to uninstall pre-commit hook
uninstall_hook() {
    print_status "Uninstalling pre-commit hook..."
    
    if [ -f ".git/hooks/pre-commit" ]; then
        rm .git/hooks/pre-commit
        print_success "Pre-commit hook removed ✓"
    else
        print_warning "Pre-commit hook not found"
    fi
}

# Function to test pre-commit hook
test_hook() {
    print_status "Testing pre-commit hook..."
    
    if [ -f ".git/hooks/pre-commit" ]; then
        if [ -x ".git/hooks/pre-commit" ]; then
            print_status "Running pre-commit hook test..."
            .git/hooks/pre-commit
        else
            print_error "Pre-commit hook is not executable"
            return 1
        fi
    else
        print_error "Pre-commit hook not found"
        return 1
    fi
}

# Function to show hook status
show_status() {
    print_status "Git Hooks Status:"
    echo ""
    
    if [ -f ".git/hooks/pre-commit" ]; then
        if [ -x ".git/hooks/pre-commit" ]; then
            print_success "✓ Pre-commit hook is installed and executable"
        else
            print_warning "⚠ Pre-commit hook exists but is not executable"
        fi
    else
        print_error "✗ Pre-commit hook is not installed"
    fi
    
    if [ -f ".git/hooks/pre-commit-config" ]; then
        print_success "✓ Pre-commit configuration file exists"
    else
        print_warning "⚠ Pre-commit configuration file not found"
    fi
}

# Function to show configuration
show_config() {
    print_status "Current Configuration:"
    echo ""
    
    if [ -f ".git/hooks/pre-commit-config" ]; then
        cat .git/hooks/pre-commit-config
    else
        print_warning "Configuration file not found"
    fi
}

# Main function
main() {
    case "${1:-help}" in
        install)
            install_hook
            ;;
        uninstall)
            uninstall_hook
            ;;
        test)
            test_hook
            ;;
        status)
            show_status
            ;;
        config)
            show_config
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    print_error "This is not a Git repository!"
    exit 1
fi

# Run main function
main "$@"

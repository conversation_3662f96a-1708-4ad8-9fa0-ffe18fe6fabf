# Git Hooks Documentation

This project includes automated Git hooks to ensure code quality and prevent broken commits.

## Pre-commit Hook

The pre-commit hook automatically runs before each commit to validate:

### ✅ What it checks:

1. **Compilation Errors**: Ensures the code compiles successfully
2. **Test Execution**: Runs all unit tests and ensures they pass
3. **QueryDSL Generation**: Generates Q-classes for QueryDSL
4. **Code Style**: Checks for basic code style issues (if configured)
5. **Common Issues**: Looks for problematic patterns like:
   - `System.out.println` statements
   - `printStackTrace()` calls
   - TODO/FIXME comments
6. **File Sizes**: Warns about large files that might need Git LFS

### 🚀 Installation

The pre-commit hook is automatically installed when you clone the repository. To manually install or reinstall:

```bash
# Using the management script
./scripts/manage-git-hooks.sh install

# Or manually
chmod +x .git/hooks/pre-commit
```

### 🧪 Testing the Hook

You can test the pre-commit hook without making a commit:

```bash
# Test the hook
./scripts/manage-git-hooks.sh test

# Or run directly
.git/hooks/pre-commit
```

### ⚙️ Configuration

You can customize the hook behavior by editing `.git/hooks/pre-commit-config`:

```bash
# Enable/disable specific checks
ENABLE_COMPILATION_CHECK=true
ENABLE_TEST_EXECUTION=true
ENABLE_CODE_STYLE_CHECK=true
ENABLE_COMMON_ISSUES_CHECK=true
ENABLE_FILE_SIZE_CHECK=true

# Test configuration
SKIP_INTEGRATION_TESTS=false
TEST_TIMEOUT=300  # seconds

# File size limits
MAX_FILE_SIZE_MB=10
```

### 🔧 Management Commands

Use the management script for easy hook management:

```bash
# Show hook status
./scripts/manage-git-hooks.sh status

# Install/reinstall hooks
./scripts/manage-git-hooks.sh install

# Remove hooks
./scripts/manage-git-hooks.sh uninstall

# Test hooks
./scripts/manage-git-hooks.sh test

# Show configuration
./scripts/manage-git-hooks.sh config

# Show help
./scripts/manage-git-hooks.sh help
```

### 🚨 Bypassing Checks

#### Skip Tests Only (Recommended)

```bash
# Skip tests but keep compilation and other checks
SKIP_TESTS=true git commit -m "WIP: fixing broken tests"
```

#### Skip All Checks (Emergency Only)

**⚠️ Not recommended**, but you can bypass all pre-commit checks in emergencies:

```bash
git commit --no-verify -m "Emergency commit"
```

#### When to Skip Tests

✅ **Good reasons to skip tests:**
- Tests are broken due to external dependencies
- Working on test fixes and need to commit progress
- Emergency hotfix where tests will be fixed in next commit
- Refactoring that temporarily breaks tests

❌ **Bad reasons to skip tests:**
- "Tests take too long"
- "I'm sure my code works"
- Regular development workflow
- Avoiding fixing failing tests

### 📋 What happens during a commit:

1. **🧹 Clean & Generate**: Cleans the project and generates QueryDSL Q-classes
2. **🔨 Compile**: Compiles the entire project
3. **🧪 Test**: Runs all unit tests
4. **🔍 Analyze**: Checks for common code issues
5. **📏 Validate**: Checks file sizes and other validations
6. **✅ Commit**: If all checks pass, the commit proceeds

### 🐛 Troubleshooting

#### Hook not running:
```bash
# Check if hook is executable
ls -la .git/hooks/pre-commit

# Make it executable
chmod +x .git/hooks/pre-commit
```

#### Compilation errors:
```bash
# Run Maven manually to see detailed errors
mvn clean compile

# Or with QueryDSL generation
mvn clean generate-sources compile
```

#### Test failures:
```bash
# Run tests manually
mvn test

# Run specific test
mvn test -Dtest=YourTestClass
```

#### QueryDSL issues:
```bash
# Generate Q-classes manually
mvn clean generate-sources

# Check if Q-classes are generated
find target/generated-sources -name "Q*.java"
```

### 📝 Example Output

When you run `git commit`, you'll see output like:

```
🔍 Running pre-commit checks...
🚀 Starting pre-commit validation...
================================================
[INFO] Checking compilation...
[INFO] Cleaning and generating sources...
[INFO] Source generation successful
[INFO] Compiling...
[SUCCESS] Compilation successful ✓
[INFO] Running tests...
[SUCCESS] All tests passed ✓
[INFO] Checking for common issues...
[SUCCESS] Common issues check completed ✓
[INFO] Checking file sizes...
[SUCCESS] File size check completed ✓
================================================
[SUCCESS] 🎉 All pre-commit checks passed! Proceeding with commit.
```

### 🔄 Integration with CI/CD

The same checks that run in the pre-commit hook should also run in your CI/CD pipeline to ensure consistency across all environments.

### 📚 Best Practices

1. **Always run tests locally** before committing
2. **Fix compilation errors immediately** - don't commit broken code
3. **Keep commits small and focused** - easier to debug if issues arise
4. **Use meaningful commit messages** - helps with debugging and code review
5. **Don't bypass the hook** unless absolutely necessary

### 🆘 Getting Help

If you encounter issues with the Git hooks:

1. Check this documentation
2. Run the test command to see detailed error output
3. Check the Maven build manually
4. Ask the team for help

Remember: The pre-commit hook is there to help maintain code quality and prevent issues from reaching the main branch!

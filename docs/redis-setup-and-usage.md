# Redis Setup and Usage Documentation

## Objective
This document provides a generic guide to setting up and using Redis for caching in a Spring Boot application. It includes installation steps, configuration details, interaction commands, and various methods available for managing the cache.

---

## 1. Installing Redis

### Steps to Install Redis:

1. **Update the package lists:**
   ```bash
   sudo apt update
   ```

2. **Install Redis:**
   ```bash
   sudo apt install redis
   ```

3. **Start the Redis service:**
   ```bash
   sudo systemctl start redis
   ```

4. **Verify that <PERSON><PERSON> is running:**
   ```bash
   redis-cli ping
   # Expected output: PONG
   ```

---

## 2. Interacting with Redis

### Using `redis-cli`:

1. **Connect to Redis locally:**
   ```bash
   redis-cli
   ```

2. **Connect to a remote Redis server:**
   ```bash
   redis-cli -h <hostname> -p <port>
   # Example
   redis-cli -h ************* -p 6379
   ```

3. **Configure Redis Memory Settings:**

   - **Set maximum memory:**
     ```bash
     redis-cli CONFIG SET maxmemory 1gb
     ```

   - **Set memory eviction policy:**
     ```bash
     redis-cli CONFIG SET maxmemory-policy allkeys-lru
     ```

   - **Verify settings:**
     ```bash
     redis-cli CONFIG GET maxmemory
     redis-cli CONFIG GET maxmemory-policy
     ```

---

## 3. Caching Methods in Spring Boot

### 1. **@Cacheable**
Caches the result of a method call. If the cache already contains the data, the method is not executed.

```java
@Cacheable(value = "cacheName", key = "#key")
public DataType getData(String key) {
    // Fetch data logic
}
```

### 2. **@CacheEvict**
Removes data from the cache.

```java
@CacheEvict(value = "cacheName", key = "#key")
public void deleteData(String key) {
    // Delete data logic
}
```

### 3. **@CachePut**
Updates the cache without skipping method execution.

```java
@CachePut(value = "cacheName", key = "#key")
public DataType updateData(String key, DataType data) {
    // Update data logic
    return updatedData;
}
```

### 4. **@Caching**
Combines multiple cache operations.

```java
@Caching(
    put = { @CachePut(value = "cacheName", key = "#key") },
    evict = { @CacheEvict(value = "otherCache", key = "#key") }
)
public DataType complexOperation(String key, DataType data) {
    // Some logic
}
```

---

## 4. Testing Redis

### Verify Redis is Running

1. **Use `redis-cli` to test connectivity:**
   ```bash
   redis-cli ping
   # Expected output: PONG
   ```

2. **Verify Caching Works:**

   - Call the service method that uses caching.

   - Use `redis-cli` to check the keys stored in Redis:
     ```bash
     redis-cli
     > KEYS *
     ```

   - Retrieve data from Redis for verification:
     ```bash
     redis-cli GET "cacheName::key"
     ```

  > 🚨 **Note:** Always evict or update cache entries promptly after any data changes to avoid serving stale or inconsistent data.

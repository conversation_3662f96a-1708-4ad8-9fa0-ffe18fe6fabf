FROM maven:3.9.6-eclipse-temurin-21 AS build 

WORKDIR /app

COPY pom.xml . 
# Download dependencies first (this layer will be cached if pom.xml doesn't change)
#RUN mvn dependency:go-offline
RUN --mount=type=cache,target=/root/.m2 mvn dependency:go-offline -B

COPY src ./src

#RUN mvn clean package -DskipTests -Dmaven.test.skip=true Dspotless.skip=true -Pdev
RUN --mount=type=cache,target=/root/.m2 mvn clean package -Dmaven.test.skip=true -Pdev -Dspotless.skip=true

# Runtime stage
FROM eclipse-temurin:21-jre-jammy

# Add a non-root user to run the application
RUN groupadd -r appuser && useradd -r -g appuser appuser

WORKDIR /app

COPY --from=build /app/target/*.jar app.jar

# Create directory for logs with proper permissions
RUN mkdir -p /app/logs /app/uploads /tmp/heap-dumps && \
    chown -R appuser:appuser /app && \
    chmod -R 755 /app

# Use non-root user for runtime
USER appuser

# Configure JVM options for production
ENV JAVA_OPTS="-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:+UseG1GC -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/heap-dumps -Djava.security.egd=file:/dev/./urandom"

# Runtime configuration directory
VOLUME ["/app/logs", "/app/uploads", "/tmp/heap-dumps"]

# Add metadata labels
LABEL maintainer="Zessta Software Services"
LABEL version="1.0"
LABEL description="phoneix  Spring Boot Application"


EXPOSE 8080 

#ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
# Start the application with proper JVM options and Spring profile
#ENTRYPOINT ["sh", "-c", "java $DEFAULT_OPTS $JAVA_OPTS -Dspring.profiles.active=$SPRING_PROFILES_ACTIVE -jar app.jar"]

ENTRYPOINT ["sh", "-c", "java $DEFAULT_OPTS $JAVA_OPTS -jar /app/app.jar --spring.profiles.active=${SPRING_PROFILES_ACTIVE:-dev}"]

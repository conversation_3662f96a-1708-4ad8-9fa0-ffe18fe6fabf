name: SonarQube Scan for ar-phoenix-core-api
on:
  workflow_dispatch:

jobs:
  sonarqube:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Required for SonarQube to detect changes

    - name: Set up JDK
      uses: actions/setup-java@v4
      with:
        java-version: '21'  # Your specified Java version
        distribution: 'temurin'
        cache: 'maven'  # Cache Maven dependencies for faster builds

    - name: Run SonarQube Scan with Maven
      run: |
        mvn clean compile sonar:sonar -DskipTests \
          -Dsonar.organization=arealytics \
          -Dsonar.projectKey=ar-phoenix-core-api \
          -Dsonar.projectName=ar-phoenix-core-api \
          #-Dsonar.host.url=${{ secrets.SONAR_HOST_URL }} \
          #-Dsonar.token=${{ secrets.SONAR_TOKEN }} \
      env:
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

    # - name: SonarQube Quality Gate Check
    #   uses: sonarsource/sonarqube-quality-gate-action@v1
    #   env:
    #     SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
    #     SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

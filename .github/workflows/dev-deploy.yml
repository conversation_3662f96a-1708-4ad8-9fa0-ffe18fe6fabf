name: Phoenix API Deployment

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment Environment'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - production

env:
  APP_NAME: 'ar-phoenix-core-api-phoenix-app'
  REPOSITORY_PATH: '/home/<USER>/ar-phoenix-core-api'

jobs:
  deploy:
    name: Deploy to ${{ github.event.inputs.environment }}
    runs-on: ubuntu-latest
    environment:
      name: ${{ github.event.inputs.environment }}
      url: https://api-phoenix-dev.areadocs.arealytics.com.au

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.ref }}
          fetch-depth: 0
          
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Generate version tag
        id: version
        run: |
          echo "VERSION=$(date +'%Y%m%d%H%M%S')-$(git rev-parse --short HEAD)" >> $GITHUB_ENV
          echo "BRANCH=$(echo ${{ github.ref }} | sed -e 's#refs/heads/##g')" >> $GITHUB_ENV

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.DEV_EC2_PRIVATE_KEY }}

      - name: Build & Deploy
        env:
          SSH_PRIVATE_KEY: ${{ secrets.DEV_EC2_PRIVATE_KEY }}
          REMOTE_HOST: ${{ secrets.DEV_HOSTNAME }}
          REMOTE_USER: ${{ secrets.DEV_USERNAME }}
          VERSION: ${{ env.VERSION }}
          BRANCH: ${{ env.BRANCH }}
        run: |
          echo "$SSH_PRIVATE_KEY" > private_key && chmod 600 private_key
          echo "Starting deployment version $VERSION..."
       
            ssh -o StrictHostKeyChecking=no -i private_key ${REMOTE_USER}@${REMOTE_HOST} "
           # Source the environment variables
            source /etc/profile.d/maven.sh &&
            cd /home/<USER>/ar-phoenix-core-api &&
            mvn test &&            
            set -e
            # Debug environment
            echo 'VERSION=$VERSION'
            echo 'BRANCH=$BRANCH'
            echo 'APP_NAME=$APP_NAME'

            # Check for docker-compose.yml
            cd $REPOSITORY_PATH
            if [ ! -f docker-compose.yml ]; then
              echo 'Error: docker-compose.yml not found in $REPOSITORY_PATH'
              exit 1
            fi
            echo 'docker-compose.yml contents:'
            cat docker-compose.yml

            # Remove obsolete version attribute
            sed -i '/^version:/d' docker-compose.yml

            # Pull latest code
            echo 'Pulling latest code from branch $BRANCH...'
            git fetch --all
            git checkout $BRANCH
            git pull

            # Backup current deployment
            echo 'Creating backup of current deployment...'
            if sudo docker image inspect ${APP_NAME}:latest >/dev/null 2>&1; then
              echo 'Tagging ${APP_NAME}:latest as ${APP_NAME}:backup-$VERSION'
              sudo docker tag ${APP_NAME}:latest ${APP_NAME}:backup-$VERSION
            else
              echo 'Image ${APP_NAME}:latest not found. Skipping backup.'
            fi

            # Build and deploy new version
            echo 'Building and deploying new version...'
            sudo docker compose down
            sudo docker compose build --pull --no-cache
            sudo docker tag ${APP_NAME}:latest ${APP_NAME}:$VERSION
            sudo docker compose up -d

            # # Save image to S3
            # echo 'Saving image to S3...'
            # sudo docker save ${APP_NAME}:$VERSION | gzip > /tmp/image-$VERSION.tar.gz
            # aws s3 cp /tmp/image-$VERSION.tar.gz s3://areadocs/artifacts/image-$VERSION.tar.gz
            # rm -f /tmp/image-$VERSION.tar.gz

            # Verify container is running
            cd $REPOSITORY_PATH &&
            CONTAINER_ID=\$(sudo docker compose ps -q phoenix-app)
            if [ -z \"\$CONTAINER_ID\" ]; then
              echo 'Deployment failed! No container found for phoenix-app.'
              echo 'Rolling back to previous version...'
              sudo docker compose down
              sed -i 's|image: ${APP_NAME}:latest|image: ${APP_NAME}:backup-$VERSION|g' docker-compose.yml
              sudo docker compose up -d
              exit 1
            fi
            if [ \"\$(sudo docker inspect --format='{{.State.Status}}' \$CONTAINER_ID)\" != 'running' ]; then
              echo 'Deployment failed! Container is not running.'
              echo 'Rolling back to previous version...'
              sudo docker compose down
              sed -i 's|image: ${APP_NAME}:latest|image: ${APP_NAME}:backup-$VERSION|g' docker-compose.yml
              sudo docker compose up -d
              exit 1
            fi

            # Clean up old images (keep last 3 versions)
            echo 'Cleaning up old images...'
            sudo docker images ${APP_NAME} --format '{{.Tag}}' |
              grep -v 'latest\|backup-$VERSION' |
              sort -r |
              tail -n +4 |
              xargs -I {} sudo docker rmi ${APP_NAME}:{} 2>/dev/null || true

            echo 'Deployment successful!'
          "

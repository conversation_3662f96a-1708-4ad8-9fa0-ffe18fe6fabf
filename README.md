# Arealytics Core API

A Spring Boot application for property analytics and management.

## 🚀 Quick Start

### Prerequisites
- Java 21+
- Maven 3.6+ (or use the included Maven wrapper)
- Git

### Setup Development Environment

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ar-poc-core-api
   ```

2. **Run the setup script**
   ```bash
   ./scripts/setup-dev-environment.sh
   ```

3. **Build the project**
   ```bash
   mvn clean install
   ```

4. **Start the application**
   ```bash
   mvn spring-boot:run
   ```

The application will start on `http://localhost:8080`

## 🔧 Development

### Git Hooks

This project includes automated Git hooks to ensure code quality:

- **Pre-commit hook**: Runs compilation checks and tests before each commit
- **Commit message hook**: Validates commit message format

#### What the Pre-commit Hook Does

✅ **Compilation Check**: Ensures code compiles without errors
✅ **Test Execution**: Runs all unit tests
✅ **QueryDSL Generation**: Generates Q-classes automatically
✅ **Code Quality**: Checks for common issues and style violations
✅ **File Validation**: Warns about large files

#### Normal Git Workflow

```bash
# Standard commit (runs all checks)
git add .
git commit -m "Your commit message"
```

#### Skipping Tests (When Needed)

```bash
# Skip tests but keep other checks
SKIP_TESTS=true git commit -m "Emergency commit without tests"

# Skip all pre-commit checks (NOT RECOMMENDED)
git commit --no-verify -m "Emergency commit bypassing all checks"
```

#### Managing Git Hooks

```bash
# Check hook status
./scripts/manage-git-hooks.sh status

# Test hooks without committing
./scripts/manage-git-hooks.sh test

# Install/reinstall hooks
./scripts/manage-git-hooks.sh install
```

#### Hook Behavior

| Check Type | Failure Action | Can Skip? |
|------------|----------------|----------|
| Compilation | ❌ Blocks commit | No |
| Tests | ❌ Blocks commit | Yes (`SKIP_TESTS=true`) |
| Code Style | ⚠️ Warns only | N/A |
| Common Issues | ⚠️ Warns only | N/A |

For detailed information, see [Git Hooks Documentation](docs/git-hooks.md).

### Building the Project

```bash
# Clean build
mvn clean install

# Skip tests (not recommended)
mvn clean install -DskipTests

# Generate QueryDSL Q-classes
mvn clean generate-sources

# Run only tests
mvn test
```

### Running the Application

```bash
# Using Maven
mvn spring-boot:run

# Using Maven wrapper
./mvnw spring-boot:run

# With specific profile
mvn spring-boot:run -Dspring.profiles.active=dev
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=PropertyServiceTest

# Run tests with coverage
mvn test jacoco:report
```

### Test Categories

- **Unit Tests**: Fast tests that don't require external dependencies
- **Integration Tesmts**: Tests that require database or external services
- **API Tests**: End-to-end API testing

## 📁 Project Structure

```
src/
├── main/
│   ├── java/
│   │   └── com/arealytics/core/
│   │       ├── controller/     # REST controllers
│   │       ├── service/        # Business logic
│   │       ├── repository/     # Data access layer
│   │       ├── domain/         # Entity classes
│   │       ├── dto/           # Data transfer objects
│   │       ├── config/        # Configuration classes
│   │       └── exception/     # Exception handling
│   └── resources/
│       ├── application.properties
│       └── static/
└── test/
    └── java/                  # Test classes
```

## 🔍 Code Quality

### Pre-commit Checks

Every commit automatically runs:
- ✅ Compilation check
- ✅ Unit tests
- ✅ QueryDSL Q-class generation
- ✅ Code style validation
- ✅ Common issue detection

### Manual Quality Checks

```bash
# Run checkstyle (if configured)
mvn checkstyle:check

# Run SpotBugs (if configured)
mvn spotbugs:check

# Generate test coverage report
mvn jacoco:report
```

## 🐛 Troubleshooting

### Common Issues

#### Java Version Issues
```bash
# Check Java version
java -version

# Should show Java 21+
# If not, install Java 21:
# Ubuntu/Debian: sudo apt install openjdk-21-jdk
# macOS: brew install openjdk@21
# Windows: Download from Oracle or use SDKMAN

# Set JAVA_HOME if needed
export JAVA_HOME=/usr/lib/jvm/java-21-openjdk-amd64
```

#### Maven Wrapper Issues
```bash
# If Maven wrapper fails, use system Maven
mvn clean install

# Or fix the wrapper
mvn -N io.takari:maven:wrapper
```

#### QueryDSL Q-classes not found
```bash
mvn clean generate-sources compile
```

#### Compilation errors
```bash
# Check detailed compilation output
mvn clean compile

# Clean everything and rebuild
mvn clean install
```

#### Tests failing
```bash
# Run tests with detailed output
mvn test -X

# Run specific failing test
mvn test -Dtest=FailingTestClass -X

# Skip tests temporarily
SKIP_TESTS=true git commit -m "Fix without running tests"
```

#### Git hooks not working
```bash
# Check hook status
./scripts/manage-git-hooks.sh status

# Reinstall hooks
./scripts/manage-git-hooks.sh install

# Test hooks manually
./scripts/manage-git-hooks.sh test
```

#### Pre-commit Hook Failures
```bash
# If compilation fails
mvn clean compile  # Fix compilation errors first

# If tests fail
mvn test  # Fix failing tests
# OR skip tests temporarily
SKIP_TESTS=true git commit -m "Emergency commit"

# If you need to bypass all checks (NOT RECOMMENDED)
git commit --no-verify -m "Emergency bypass"
```

### Getting Help

1. Check this README
2. Review the [Git Hooks Documentation](docs/git-hooks.md)
3. Run the setup script: `./scripts/setup-dev-environment.sh`
4. Check the application logs in the `logs/` directory
5. Ask the development team

## 📚 Documentation

- [Git Hooks Documentation](docs/git-hooks.md) - Detailed information about Git hooks
- [API Documentation](docs/api.md) - API endpoints and usage (if available)
- [Database Schema](docs/database.md) - Database structure (if available)

## 🚀 Quick Reference

### Common Git Commands with Hooks

```bash
# Normal development workflow
git add .
git commit -m "Add new feature"  # Runs all checks

# When tests are broken but you need to commit
SKIP_TESTS=true git commit -m "WIP: fixing tests"

# Emergency bypass (use sparingly)
git commit --no-verify -m "Hotfix: critical bug"

# Test hooks without committing
./scripts/manage-git-hooks.sh test

# Check what the hooks will do
./scripts/manage-git-hooks.sh status
```

### Maven Commands

```bash
# Full build with tests
mvn clean install

# Quick compile check
mvn clean compile

# Generate QueryDSL classes
mvn clean generate-sources

# Run tests only
mvn test

# Skip tests
mvn clean install -DskipTests

# Run specific test
mvn test -Dtest=YourTestClass
```

### Development Environment

```bash
# Setup everything
./scripts/setup-dev-environment.sh

# Start application
mvn spring-boot:run

# Start with specific profile
mvn spring-boot:run -Dspring.profiles.active=dev
```

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** (Git hooks will validate your commits)
4. **Commit your changes**: `git commit -m "Add amazing feature"`
5. **Push to the branch**: `git push origin feature/amazing-feature`
6. **Open a Pull Request**

### Commit Message Guidelines

- Use present tense ("Add feature" not "Added feature")
- Use imperative mood ("Move cursor to..." not "Moves cursor to...")
- Limit the first line to 72 characters or less
- Reference issues and pull requests liberally after the first line

## 📄 License

This project is licensed under the [MIT License](LICENSE) - see the LICENSE file for details.

## 🆘 Support

If you encounter any issues or have questions:

1. Check the troubleshooting section above
2. Search existing issues in the repository
3. Create a new issue with detailed information
4. Contact the development team

---

**Happy coding! 🚀**

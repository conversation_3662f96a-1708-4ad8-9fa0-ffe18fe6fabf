# De<PERSON><PERSON> ignored files
/shelf/
/workspace.xml

# Java
*.class
*.jar
*.war
*.ear
*.log

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties

# STS / IntelliJ
*.iml
*.ipr
*.iws
.idea/
.classpath
.project
.settings/
out/

# VS Code
.vscode/

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log
*.out

# Generated
generated/
